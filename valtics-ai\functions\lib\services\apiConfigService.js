"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.APIConfigService = void 0;
const admin = __importStar(require("firebase-admin"));
const CryptoJS = __importStar(require("crypto-js"));
const aiService_1 = require("./aiService");
class APIConfigService {
    /**
     * Decrypt encrypted data
     */
    static decrypt(encryptedData) {
        try {
            const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
            return bytes.toString(CryptoJS.enc.Utf8);
        }
        catch (error) {
            console.error('Decryption failed:', error);
            throw new Error('Failed to decrypt API key');
        }
    }
    /**
     * Get API configuration from Firestore
     */
    static async getAPIConfig() {
        try {
            // Use the same collection and document as the frontend
            const configDoc = await admin.firestore()
                .collection('apiConfig')
                .doc('main')
                .get();
            if (!configDoc.exists) {
                console.log('API config document does not exist');
                return null;
            }
            const data = configDoc.data();
            console.log('API config found:', {
                hasClaudeKey: !!(data === null || data === void 0 ? void 0 : data.claudeApiKey),
                hasOpenaiKey: !!(data === null || data === void 0 ? void 0 : data.openaiApiKey),
                hasClaudeModelConfig: !!(data === null || data === void 0 ? void 0 : data.claudeModelConfig),
                hasOpenaiModelConfig: !!(data === null || data === void 0 ? void 0 : data.openaiModelConfig)
            });
            // Add default model configurations if not present
            if (data) {
                if (!data.claudeModelConfig) {
                    data.claudeModelConfig = {
                        model: 'claude-3-5-sonnet-latest',
                        maxTokens: 4000,
                        temperature: 0.7
                    };
                }
                if (!data.openaiModelConfig) {
                    data.openaiModelConfig = {
                        model: 'gpt-4-vision-preview',
                        maxTokens: 4000,
                        temperature: 0.7
                    };
                }
            }
            return data;
        }
        catch (error) {
            console.error('Error fetching API config:', error);
            return null;
        }
    }
    /**
     * Check if API keys are configured
     */
    static async areAPIKeysConfigured() {
        const config = await this.getAPIConfig();
        const claudeConfigured = !!(config === null || config === void 0 ? void 0 : config.claudeApiKey);
        const openaiConfigured = !!(config === null || config === void 0 ? void 0 : config.openaiApiKey);
        return {
            claudeConfigured,
            openaiConfigured,
            anyConfigured: claudeConfigured || openaiConfigured
        };
    }
    /**
     * Initialize AI services with stored API keys
     */
    static async initializeAIServices() {
        const config = await this.getAPIConfig();
        if (!config) {
            throw new Error('No API configuration found');
        }
        // Initialize Claude if key is available
        if (config.claudeApiKey) {
            try {
                const claudeKey = this.decrypt(config.claudeApiKey);
                aiService_1.AIService.initializeClaude(claudeKey);
                console.log('Claude API initialized successfully');
            }
            catch (error) {
                console.error('Failed to initialize Claude API:', error);
            }
        }
        // Initialize OpenAI if key is available
        if (config.openaiApiKey) {
            try {
                const openaiKey = this.decrypt(config.openaiApiKey);
                aiService_1.AIService.initializeOpenAI(openaiKey);
                console.log('OpenAI API initialized successfully');
            }
            catch (error) {
                console.error('Failed to initialize OpenAI API:', error);
            }
        }
    }
}
exports.APIConfigService = APIConfigService;
// Use the same encryption key as the frontend
APIConfigService.ENCRYPTION_KEY = 'raviteja'; // This should match NEXT_PUBLIC_ENCRYPTION_KEY
//# sourceMappingURL=apiConfigService.js.map