(()=>{var e={};e.id=122,e.ids=[122],e.modules={2101:function(e,t,s){var r;r=s(98846),s(28165),s(40465),s(24613),s(86348),function(){var e=r.lib.StreamCipher,t=r.algo,s=[],n=[],a=[],i=t.Rabbit=e.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,s=0;s<4;s++)e[s]=(e[s]<<8|e[s]>>>24)&0xff00ff|(e[s]<<24|e[s]>>>8)&0xff00ff00;var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,0xffff0000&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,0xffff0000&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,0xffff0000&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,0xffff0000&e[3]|65535&e[0]];this._b=0;for(var s=0;s<4;s++)o.call(this);for(var s=0;s<8;s++)n[s]^=r[s+4&7];if(t){var a=t.words,i=a[0],l=a[1],c=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,d=(l<<8|l>>>24)&0xff00ff|(l<<24|l>>>8)&0xff00ff00,h=c>>>16|0xffff0000&d,u=d<<16|65535&c;n[0]^=c,n[1]^=h,n[2]^=d,n[3]^=u,n[4]^=c,n[5]^=h,n[6]^=d,n[7]^=u;for(var s=0;s<4;s++)o.call(this)}},_doProcessBlock:function(e,t){var r=this._X;o.call(this),s[0]=r[0]^r[5]>>>16^r[3]<<16,s[1]=r[2]^r[7]>>>16^r[5]<<16,s[2]=r[4]^r[1]>>>16^r[7]<<16,s[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)s[n]=(s[n]<<8|s[n]>>>24)&0xff00ff|(s[n]<<24|s[n]>>>8)&0xff00ff00,e[t+n]^=s[n]},blockSize:4,ivSize:2});function o(){for(var e=this._X,t=this._C,s=0;s<8;s++)n[s]=t[s];t[0]=t[0]+0x4d34d34d+this._b|0,t[1]=t[1]+0xd34d34d3+ +(t[0]>>>0<n[0]>>>0)|0,t[2]=t[2]+0x34d34d34+ +(t[1]>>>0<n[1]>>>0)|0,t[3]=t[3]+0x4d34d34d+ +(t[2]>>>0<n[2]>>>0)|0,t[4]=t[4]+0xd34d34d3+ +(t[3]>>>0<n[3]>>>0)|0,t[5]=t[5]+0x34d34d34+ +(t[4]>>>0<n[4]>>>0)|0,t[6]=t[6]+0x4d34d34d+ +(t[5]>>>0<n[5]>>>0)|0,t[7]=t[7]+0xd34d34d3+ +(t[6]>>>0<n[6]>>>0)|0,this._b=+(t[7]>>>0<n[7]>>>0);for(var s=0;s<8;s++){var r=e[s]+t[s],i=65535&r,o=r>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((0xffff0000&r)*r|0)+((65535&r)*r|0);a[s]=l^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}r.Rabbit=e._createHelper(i)}(),e.exports=r.Rabbit},2613:function(e,t,s){var r;r=s(98846),s(28165),s(40465),s(24613),s(86348),function(){var e=r.lib,t=e.WordArray,s=e.BlockCipher,n=r.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],i=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],o=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,0x10000000:32768,0x20000000:8421378,0x30000000:2,0x40000000:512,0x50000000:8421890,0x60000000:8389122,0x70000000:8388608,0x80000000:514,0x90000000:8389120,0xa0000000:33280,0xb0000000:8421376,0xc0000000:32770,0xd0000000:8388610,0xe0000000:0,0xf0000000:33282,0x8000000:0,0x18000000:8421890,0x28000000:33282,0x38000000:32768,0x48000000:8421888,0x58000000:512,0x68000000:8421378,0x78000000:2,0x88000000:8389120,0x98000000:33280,0xa8000000:8421376,0xb8000000:8389122,0xc8000000:8388610,0xd8000000:32770,0xe8000000:514,0xf8000000:8388608,1:32768,0x10000001:2,0x20000001:8421888,0x30000001:8388608,0x40000001:8421378,0x50000001:33280,0x60000001:512,0x70000001:8389122,0x80000001:8421890,0x90000001:8421376,0xa0000001:8388610,0xb0000001:33282,0xc0000001:514,0xd0000001:8389120,0xe0000001:32770,0xf0000001:0,0x8000001:8421890,0x18000001:8421376,0x28000001:8388608,0x38000001:512,0x48000001:32768,0x58000001:8388610,0x68000001:2,0x78000001:33282,0x88000001:32770,0x98000001:8389122,0xa8000001:514,0xb8000001:8421888,0xc8000001:8389120,0xd8000001:0,0xe8000001:33280,0xf8000001:8421378},{0:0x40084010,0x1000000:16384,0x2000000:524288,0x3000000:0x40080010,0x4000000:0x40000010,0x5000000:0x40084000,0x6000000:0x40004000,0x7000000:16,0x8000000:540672,0x9000000:0x40004010,0xa000000:0x40000000,0xb000000:540688,0xc000000:524304,0xd000000:0,0xe000000:16400,0xf000000:0x40080000,8388608:0x40004000,0x1800000:540688,0x2800000:16,0x3800000:0x40004010,0x4800000:0x40084010,0x5800000:0x40000000,0x6800000:524288,0x7800000:0x40080010,0x8800000:524304,0x9800000:0,0xa800000:16384,0xb800000:0x40080000,0xc800000:0x40000010,0xd800000:540672,0xe800000:0x40084000,0xf800000:16400,0x10000000:0,0x11000000:0x40080010,0x12000000:0x40004010,0x13000000:0x40084000,0x14000000:0x40080000,0x15000000:16,0x16000000:540688,0x17000000:16384,0x18000000:16400,0x19000000:524288,0x1a000000:524304,0x1b000000:0x40000010,0x1c000000:540672,0x1d000000:0x40004000,0x1e000000:0x40000000,0x1f000000:0x40084010,0x10800000:540688,0x11800000:524288,0x12800000:0x40080000,0x13800000:16384,0x14800000:0x40004000,0x15800000:0x40084010,0x16800000:16,0x17800000:0x40000000,0x18800000:0x40084000,0x19800000:0x40000010,0x1a800000:0x40004010,0x1b800000:524304,0x1c800000:0,0x1d800000:16400,0x1e800000:0x40080010,0x1f800000:540672},{0:260,1048576:0,2097152:0x4000100,3145728:65796,4194304:65540,5242880:0x4000004,6291456:0x4010104,7340032:0x4010000,8388608:0x4000000,9437184:0x4010100,0xa00000:65792,0xb00000:0x4010004,0xc00000:0x4000104,0xd00000:65536,0xe00000:4,0xf00000:256,524288:0x4010100,1572864:0x4010004,2621440:0,3670016:0x4000100,4718592:0x4000004,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,0xa80000:0x4010000,0xb80000:65796,0xc80000:65792,0xd80000:0x4000104,0xe80000:0x4010104,0xf80000:0x4000000,0x1000000:0x4010100,0x1100000:65540,0x1200000:65536,0x1300000:0x4000100,0x1400000:256,0x1500000:0x4010104,0x1600000:0x4000004,0x1700000:0,0x1800000:0x4000104,0x1900000:0x4000000,0x1a00000:4,0x1b00000:65792,0x1c00000:0x4010000,0x1d00000:260,0x1e00000:65796,0x1f00000:0x4010004,0x1080000:0x4000000,0x1180000:260,0x1280000:0x4010100,0x1380000:0,0x1480000:65540,0x1580000:0x4000100,0x1680000:256,0x1780000:0x4010004,0x1880000:65536,0x1980000:0x4010104,0x1a80000:65796,0x1b80000:0x4000004,0x1c80000:0x4000104,0x1d80000:0x4010000,0x1e80000:4,0x1f80000:65792},{0:0x80401000,65536:0x80001040,131072:4198464,196608:0x80400000,262144:0,327680:4198400,393216:0x80000040,458752:4194368,524288:0x80000000,589824:4194304,655360:64,720896:0x80001000,786432:0x80400040,851968:4160,917504:4096,983040:0x80401040,32768:0x80001040,98304:64,163840:0x80400040,229376:0x80001000,294912:4198400,360448:0x80401040,425984:0,491520:0x80400000,557056:4096,622592:0x80401000,688128:4194304,753664:4160,819200:0x80000000,884736:4194368,950272:4198464,1015808:0x80000040,1048576:4194368,1114112:4198400,1179648:0x80000040,1245184:0,1310720:4160,1376256:0x80400040,1441792:0x80401000,1507328:0x80001040,1572864:0x80401040,1638400:0x80000000,1703936:0x80400000,1769472:4198464,1835008:0x80001000,1900544:4194304,1966080:64,2031616:4096,1081344:0x80400000,1146880:0x80401040,1212416:0,1277952:4198400,1343488:4194368,1409024:0x80000000,1474560:0x80001040,1540096:64,1605632:0x80000040,1671168:4096,1736704:0x80001000,1802240:0x80400040,1867776:4160,1933312:0x80401000,1998848:4194304,2064384:4198464},{0:128,4096:0x1040000,8192:262144,12288:0x20000000,16384:0x20040080,20480:0x1000080,24576:0x21000080,28672:262272,32768:0x1000000,36864:0x20040000,40960:0x20000080,45056:0x21040080,49152:0x21040000,53248:0,57344:0x1040080,61440:0x21000000,2048:0x1040080,6144:0x21000080,10240:128,14336:0x1040000,18432:262144,22528:0x20040080,26624:0x21040000,30720:0x20000000,34816:0x20040000,38912:0,43008:0x21040080,47104:0x1000080,51200:0x20000080,55296:0x21000000,59392:0x1000000,63488:262272,65536:262144,69632:128,73728:0x20000000,77824:0x21000080,81920:0x1000080,86016:0x21040000,90112:0x20040080,94208:0x1000000,98304:0x21040080,102400:0x21000000,106496:0x1040000,110592:0x20040000,114688:262272,118784:0x20000080,122880:0,126976:0x1040080,67584:0x21000080,71680:0x1000000,75776:0x1040000,79872:0x20040080,83968:0x20000000,88064:0x1040080,92160:128,96256:0x21040000,100352:262272,104448:0x21040080,108544:0,112640:0x21000000,116736:0x1000080,120832:262144,124928:0x20040000,129024:0x20000080},{0:0x10000008,256:8192,512:0x10200000,768:0x10202008,1024:0x10002000,1280:2097152,1536:2097160,1792:0x10000000,2048:0,2304:0x10002008,2560:2105344,2816:8,3072:0x10200008,3328:2105352,3584:8200,3840:0x10202000,128:0x10200000,384:0x10202008,640:8,896:2097152,1152:2105352,1408:0x10000008,1664:0x10002000,1920:8200,2176:2097160,2432:8192,2688:0x10002008,2944:0x10200008,3200:0,3456:0x10202000,3712:2105344,3968:0x10000000,4096:0x10002000,4352:0x10200008,4608:0x10202008,4864:8200,5120:2097152,5376:0x10000000,5632:0x10000008,5888:2105344,6144:2105352,6400:0,6656:8,6912:0x10200000,7168:8192,7424:0x10002008,7680:0x10202000,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:0x10000008,5248:0x10002000,5504:8200,5760:0x10202008,6016:0x10200000,6272:0x10202000,6528:0x10200008,6784:8192,7040:2105352,7296:2097160,7552:0,7808:0x10000000,8064:0x10002008},{0:1048576,16:0x2000401,32:1024,48:1049601,64:0x2100401,80:0,96:1,112:0x2100001,128:0x2000400,144:1048577,160:0x2000001,176:0x2100400,192:0x2100000,208:1025,224:1049600,240:0x2000000,8:0x2100001,24:0,40:0x2000401,56:0x2100400,72:1048576,88:0x2000001,104:0x2000000,120:1025,136:1049601,152:0x2000400,168:0x2100000,184:1048577,200:1024,216:0x2100401,232:1,248:1049600,256:0x2000000,272:1048576,288:0x2000401,304:0x2100001,320:1048577,336:0x2000400,352:0x2100400,368:1049601,384:1025,400:0x2100401,416:1049600,432:1,448:0,464:0x2100000,480:0x2000001,496:1024,264:1049600,280:0x2000401,296:0x2100001,312:1,328:0x2000000,344:1048576,360:1025,376:0x2100400,392:0x2000001,408:0x2100000,424:0,440:0x2100401,456:1049601,472:1024,488:0x2000400,504:1048577},{0:0x8000820,1:131072,2:0x8000000,3:32,4:131104,5:0x8020820,6:0x8020800,7:2048,8:0x8020000,9:0x8000800,10:133120,11:0x8020020,12:2080,13:0,14:0x8000020,15:133152,0x80000000:2048,0x80000001:0x8020820,0x80000002:0x8000820,0x80000003:0x8000000,0x80000004:0x8020000,0x80000005:133120,0x80000006:133152,0x80000007:32,0x80000008:0x8000020,0x80000009:2080,0x8000000a:131104,0x8000000b:0x8020800,0x8000000c:0,0x8000000d:0x8020020,0x8000000e:0x8000800,0x8000000f:131072,16:133152,17:0x8020800,18:32,19:2048,20:0x8000800,21:0x8000020,22:0x8020020,23:131072,24:0,25:131104,26:0x8020000,27:0x8000820,28:0x8020820,29:133120,30:2080,31:0x8000000,0x80000010:131072,0x80000011:2048,0x80000012:0x8020020,0x80000013:133152,0x80000014:32,0x80000015:0x8020000,0x80000016:0x8000000,0x80000017:0x8000820,0x80000018:0x8020820,0x80000019:0x8000020,0x8000001a:0x8000800,0x8000001b:0,0x8000001c:133120,0x8000001d:2080,0x8000001e:131104,0x8000001f:0x8020800}],c=[0xf8000001,0x1f800000,0x1f80000,2064384,129024,8064,504,0x8000001f],d=n.DES=s.extend({_doReset:function(){for(var e=this._key.words,t=[],s=0;s<56;s++){var r=a[s]-1;t[s]=e[r>>>5]>>>31-r%32&1}for(var n=this._subKeys=[],l=0;l<16;l++){for(var c=n[l]=[],d=o[l],s=0;s<24;s++)c[s/6|0]|=t[(i[s]-1+d)%28]<<31-s%6,c[4+(s/6|0)]|=t[28+(i[s+24]-1+d)%28]<<31-s%6;c[0]=c[0]<<1|c[0]>>>31;for(var s=1;s<7;s++)c[s]=c[s]>>>(s-1)*4+3;c[7]=c[7]<<5|c[7]>>>27}for(var h=this._invSubKeys=[],s=0;s<16;s++)h[s]=n[15-s]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,s){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,0xf0f0f0f),h.call(this,16,65535),u.call(this,2,0x33333333),u.call(this,8,0xff00ff),h.call(this,1,0x55555555);for(var r=0;r<16;r++){for(var n=s[r],a=this._lBlock,i=this._rBlock,o=0,d=0;d<8;d++)o|=l[d][((i^n[d])&c[d])>>>0];this._lBlock=i,this._rBlock=a^o}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,h.call(this,1,0x55555555),u.call(this,8,0xff00ff),u.call(this,2,0x33333333),h.call(this,16,65535),h.call(this,4,0xf0f0f0f),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var s=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=s,this._lBlock^=s<<e}function u(e,t){var s=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=s,this._rBlock^=s<<e}r.DES=s._createHelper(d);var f=n.TripleDES=s.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var s=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=d.createEncryptor(t.create(s)),this._des2=d.createEncryptor(t.create(r)),this._des3=d.createEncryptor(t.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});r.TripleDES=s._createHelper(f)}(),e.exports=r.TripleDES},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4765:function(e,t,s){var r;r=s(98846),s(23135),s(25353),s(76910),s(28165),s(50584),s(40465),s(18796),s(51398),s(16279),s(9095),s(45422),s(78978),s(36401),s(22938),s(25408),s(24613),s(86348),s(77026),s(65028),s(30671),s(94326),s(81381),s(51150),s(44174),s(37085),s(13926),s(99305),s(39914),s(7246),s(2613),s(91824),s(2101),s(77089),s(15619),e.exports=r},5481:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(60687),n=s(85814),a=s.n(n),i=s(30474),o=s(51108),l=s(16189),c=s(27436),d=s(31769);function h({title:e="VALTICS AI",showBackButton:t=!1,backUrl:s="/dashboard",backText:n="← Back to Dashboard"}){let{user:h,logOut:u,isAdmin:f}=(0,o.A)(),p=(0,l.useRouter)(),x=async()=>{try{await u(),p.push("/")}catch(e){console.error("Error logging out:",e)}};return h?(0,r.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(a(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,r.jsx)(i.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,r.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&(0,r.jsx)(a(),{href:s,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:n}),!t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,r.jsx)(a(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),f&&(0,r.jsx)(a(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,r.jsx)(a(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,r.jsx)(d.I,{}),(0,r.jsx)(c.default,{}),(0,r.jsx)("button",{onClick:x,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},7246:function(e,t,s){var r,n,a,i,o,l,c,d,h,u,f,p,x,m,g;r=s(98846),s(28165),s(40465),s(24613),s(86348),n=r.lib.BlockCipher,a=r.algo,i=[],o=[],l=[],c=[],d=[],h=[],u=[],f=[],p=[],x=[],function(){for(var e=[],t=0;t<256;t++)t<128?e[t]=t<<1:e[t]=t<<1^283;for(var s=0,r=0,t=0;t<256;t++){var n=r^r<<1^r<<2^r<<3^r<<4;n=n>>>8^255&n^99,i[s]=n,o[n]=s;var a=e[s],m=e[a],g=e[m],b=257*e[n]^0x1010100*n;l[s]=b<<24|b>>>8,c[s]=b<<16|b>>>16,d[s]=b<<8|b>>>24,h[s]=b;var b=0x1010101*g^65537*m^257*a^0x1010100*s;u[n]=b<<24|b>>>8,f[n]=b<<16|b>>>16,p[n]=b<<8|b>>>24,x[n]=b,s?(s=a^e[e[e[g^a]]],r^=e[e[r]]):s=r=1}}(),m=[0,1,2,4,8,16,32,64,128,27,54],g=a.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e,t=this._keyPriorReset=this._key,s=t.words,r=t.sigBytes/4,n=((this._nRounds=r+6)+1)*4,a=this._keySchedule=[],o=0;o<n;o++)o<r?a[o]=s[o]:(e=a[o-1],o%r?r>6&&o%r==4&&(e=i[e>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e]):e=(i[(e=e<<8|e>>>24)>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e])^m[o/r|0]<<24,a[o]=a[o-r]^e);for(var l=this._invKeySchedule=[],c=0;c<n;c++){var o=n-c;if(c%4)var e=a[o];else var e=a[o-4];c<4||o<=4?l[c]=e:l[c]=u[i[e>>>24]]^f[i[e>>>16&255]]^p[i[e>>>8&255]]^x[i[255&e]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,l,c,d,h,i)},decryptBlock:function(e,t){var s=e[t+1];e[t+1]=e[t+3],e[t+3]=s,this._doCryptBlock(e,t,this._invKeySchedule,u,f,p,x,o);var s=e[t+1];e[t+1]=e[t+3],e[t+3]=s},_doCryptBlock:function(e,t,s,r,n,a,i,o){for(var l=this._nRounds,c=e[t]^s[0],d=e[t+1]^s[1],h=e[t+2]^s[2],u=e[t+3]^s[3],f=4,p=1;p<l;p++){var x=r[c>>>24]^n[d>>>16&255]^a[h>>>8&255]^i[255&u]^s[f++],m=r[d>>>24]^n[h>>>16&255]^a[u>>>8&255]^i[255&c]^s[f++],g=r[h>>>24]^n[u>>>16&255]^a[c>>>8&255]^i[255&d]^s[f++],b=r[u>>>24]^n[c>>>16&255]^a[d>>>8&255]^i[255&h]^s[f++];c=x,d=m,h=g,u=b}var x=(o[c>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&u])^s[f++],m=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[u>>>8&255]<<8|o[255&c])^s[f++],g=(o[h>>>24]<<24|o[u>>>16&255]<<16|o[c>>>8&255]<<8|o[255&d])^s[f++],b=(o[u>>>24]<<24|o[c>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^s[f++];e[t]=x,e[t+1]=m,e[t+2]=g,e[t+3]=b},keySize:8}),r.AES=n._createHelper(g),e.exports=r.AES},9095:function(e,t,s){var r;r=s(98846),s(23135),function(){var e=r.lib.Hasher,t=r.x64,s=t.Word,n=t.WordArray,a=r.algo;function i(){return s.create.apply(s,arguments)}for(var o=[i(0x428a2f98,0xd728ae22),i(0x71374491,0x23ef65cd),i(0xb5c0fbcf,0xec4d3b2f),i(0xe9b5dba5,0x8189dbbc),i(0x3956c25b,0xf348b538),i(0x59f111f1,0xb605d019),i(0x923f82a4,0xaf194f9b),i(0xab1c5ed5,0xda6d8118),i(0xd807aa98,0xa3030242),i(0x12835b01,0x45706fbe),i(0x243185be,0x4ee4b28c),i(0x550c7dc3,0xd5ffb4e2),i(0x72be5d74,0xf27b896f),i(0x80deb1fe,0x3b1696b1),i(0x9bdc06a7,0x25c71235),i(0xc19bf174,0xcf692694),i(0xe49b69c1,0x9ef14ad2),i(0xefbe4786,0x384f25e3),i(0xfc19dc6,0x8b8cd5b5),i(0x240ca1cc,0x77ac9c65),i(0x2de92c6f,0x592b0275),i(0x4a7484aa,0x6ea6e483),i(0x5cb0a9dc,0xbd41fbd4),i(0x76f988da,0x831153b5),i(0x983e5152,0xee66dfab),i(0xa831c66d,0x2db43210),i(0xb00327c8,0x98fb213f),i(0xbf597fc7,0xbeef0ee4),i(0xc6e00bf3,0x3da88fc2),i(0xd5a79147,0x930aa725),i(0x6ca6351,0xe003826f),i(0x14292967,0xa0e6e70),i(0x27b70a85,0x46d22ffc),i(0x2e1b2138,0x5c26c926),i(0x4d2c6dfc,0x5ac42aed),i(0x53380d13,0x9d95b3df),i(0x650a7354,0x8baf63de),i(0x766a0abb,0x3c77b2a8),i(0x81c2c92e,0x47edaee6),i(0x92722c85,0x1482353b),i(0xa2bfe8a1,0x4cf10364),i(0xa81a664b,0xbc423001),i(0xc24b8b70,0xd0f89791),i(0xc76c51a3,0x654be30),i(0xd192e819,0xd6ef5218),i(0xd6990624,0x5565a910),i(0xf40e3585,0x5771202a),i(0x106aa070,0x32bbd1b8),i(0x19a4c116,0xb8d2d0c8),i(0x1e376c08,0x5141ab53),i(0x2748774c,0xdf8eeb99),i(0x34b0bcb5,0xe19b48a8),i(0x391c0cb3,0xc5c95a63),i(0x4ed8aa4a,0xe3418acb),i(0x5b9cca4f,0x7763e373),i(0x682e6ff3,0xd6b2b8a3),i(0x748f82ee,0x5defb2fc),i(0x78a5636f,0x43172f60),i(0x84c87814,0xa1f0ab72),i(0x8cc70208,0x1a6439ec),i(0x90befffa,0x23631e28),i(0xa4506ceb,0xde82bde9),i(0xbef9a3f7,0xb2c67915),i(0xc67178f2,0xe372532b),i(0xca273ece,0xea26619c),i(0xd186b8c7,0x21c0c207),i(0xeada7dd6,0xcde0eb1e),i(0xf57d4f7f,0xee6ed178),i(0x6f067aa,0x72176fba),i(0xa637dc5,0xa2c898a6),i(0x113f9804,0xbef90dae),i(0x1b710b35,0x131c471b),i(0x28db77f5,0x23047d84),i(0x32caab7b,0x40c72493),i(0x3c9ebe0a,0x15c9bebc),i(0x431d67c4,0x9c100d4c),i(0x4cc5d4be,0xcb3e42b6),i(0x597f299c,0xfc657e2a),i(0x5fcb6fab,0x3ad6faec),i(0x6c44198c,0x4a475817)],l=[],c=0;c<80;c++)l[c]=i();var d=a.SHA512=e.extend({_doReset:function(){this._hash=new n.init([new s.init(0x6a09e667,0xf3bcc908),new s.init(0xbb67ae85,0x84caa73b),new s.init(0x3c6ef372,0xfe94f82b),new s.init(0xa54ff53a,0x5f1d36f1),new s.init(0x510e527f,0xade682d1),new s.init(0x9b05688c,0x2b3e6c1f),new s.init(0x1f83d9ab,0xfb41bd6b),new s.init(0x5be0cd19,0x137e2179)])},_doProcessBlock:function(e,t){for(var s=this._hash.words,r=s[0],n=s[1],a=s[2],i=s[3],c=s[4],d=s[5],h=s[6],u=s[7],f=r.high,p=r.low,x=n.high,m=n.low,g=a.high,b=a.low,y=i.high,w=i.low,_=c.high,v=c.low,k=d.high,S=d.low,A=h.high,R=h.low,I=u.high,P=u.low,$=f,C=p,E=x,O=m,B=g,N=b,j=y,M=w,T=_,D=v,L=k,W=S,U=A,H=R,q=I,F=P,z=0;z<80;z++){var K,X,J=l[z];if(z<16)X=J.high=0|e[t+2*z],K=J.low=0|e[t+2*z+1];else{var V=l[z-15],G=V.high,Q=V.low,Y=(G>>>1|Q<<31)^(G>>>8|Q<<24)^G>>>7,Z=(Q>>>1|G<<31)^(Q>>>8|G<<24)^(Q>>>7|G<<25),ee=l[z-2],et=ee.high,es=ee.low,er=(et>>>19|es<<13)^(et<<3|es>>>29)^et>>>6,en=(es>>>19|et<<13)^(es<<3|et>>>29)^(es>>>6|et<<26),ea=l[z-7],ei=ea.high,eo=ea.low,el=l[z-16],ec=el.high,ed=el.low;X=Y+ei+ +((K=Z+eo)>>>0<Z>>>0),K+=en,X=X+er+ +(K>>>0<en>>>0),K+=ed,J.high=X=X+ec+ +(K>>>0<ed>>>0),J.low=K}var eh=T&L^~T&U,eu=D&W^~D&H,ef=$&E^$&B^E&B,ep=C&O^C&N^O&N,ex=($>>>28|C<<4)^($<<30|C>>>2)^($<<25|C>>>7),em=(C>>>28|$<<4)^(C<<30|$>>>2)^(C<<25|$>>>7),eg=(T>>>14|D<<18)^(T>>>18|D<<14)^(T<<23|D>>>9),eb=(D>>>14|T<<18)^(D>>>18|T<<14)^(D<<23|T>>>9),ey=o[z],ew=ey.high,e_=ey.low,ev=F+eb,ek=q+eg+ +(ev>>>0<F>>>0),ev=ev+eu,ek=ek+eh+ +(ev>>>0<eu>>>0),ev=ev+e_,ek=ek+ew+ +(ev>>>0<e_>>>0),ev=ev+K,ek=ek+X+ +(ev>>>0<K>>>0),eS=em+ep,eA=ex+ef+ +(eS>>>0<em>>>0);q=U,F=H,U=L,H=W,L=T,W=D,T=j+ek+ +((D=M+ev|0)>>>0<M>>>0)|0,j=B,M=N,B=E,N=O,E=$,O=C,$=ek+eA+ +((C=ev+eS|0)>>>0<ev>>>0)|0}p=r.low=p+C,r.high=f+$+ +(p>>>0<C>>>0),m=n.low=m+O,n.high=x+E+ +(m>>>0<O>>>0),b=a.low=b+N,a.high=g+B+ +(b>>>0<N>>>0),w=i.low=w+M,i.high=y+j+ +(w>>>0<M>>>0),v=c.low=v+D,c.high=_+T+ +(v>>>0<D>>>0),S=d.low=S+W,d.high=k+L+ +(S>>>0<W>>>0),R=h.low=R+H,h.high=A+U+ +(R>>>0<H>>>0),P=u.low=P+F,u.high=I+q+ +(P>>>0<F>>>0)},_doFinalize:function(){var e=this._data,t=e.words,s=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[(r+128>>>10<<5)+30]=Math.floor(s/0x100000000),t[(r+128>>>10<<5)+31]=s,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});r.SHA512=e._createHelper(d),r.HmacSHA512=e._createHmacHelper(d)}(),e.exports=r.SHA512},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13926:function(e,t,s){var r;r=s(98846),s(86348),r.pad.ZeroPadding={pad:function(e,t){var s=4*t;e.clamp(),e.sigBytes+=s-(e.sigBytes%s||s)},unpad:function(e){for(var t=e.words,s=e.sigBytes-1,s=e.sigBytes-1;s>=0;s--)if(t[s>>>2]>>>24-s%4*8&255){e.sigBytes=s+1;break}}},e.exports=r.pad.ZeroPadding},14985:e=>{"use strict";e.exports=require("dns")},15619:function(e,t,s){var r;r=s(98846),s(28165),s(40465),s(24613),s(86348),function(){var e=r.lib.BlockCipher,t=r.algo;let s=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],n=[[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a],[0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7],[0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0],[0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6]];var a={pbox:[],sbox:[]};function i(e,t){let s=e.sbox[0][t>>24&255]+e.sbox[1][t>>16&255];return s^=e.sbox[2][t>>8&255],s+=e.sbox[3][255&t]}function o(e,t,s){let r,n=t,a=s;for(let t=0;t<16;++t)n^=e.pbox[t],a=i(e,n)^a,r=n,n=a,a=r;return r=n,n=a,a=r^e.pbox[16],{left:n^=e.pbox[17],right:a}}var l=t.Blowfish=e.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key;!function(e,t,r){for(let t=0;t<4;t++){e.sbox[t]=[];for(let s=0;s<256;s++)e.sbox[t][s]=n[t][s]}let a=0;for(let n=0;n<18;n++)e.pbox[n]=s[n]^t[a],++a>=r&&(a=0);let i=0,l=0,c=0;for(let t=0;t<18;t+=2)i=(c=o(e,i,l)).left,l=c.right,e.pbox[t]=i,e.pbox[t+1]=l;for(let t=0;t<4;t++)for(let s=0;s<256;s+=2)i=(c=o(e,i,l)).left,l=c.right,e.sbox[t][s]=i,e.sbox[t][s+1]=l}(a,e.words,e.sigBytes/4)}},encryptBlock:function(e,t){var s=o(a,e[t],e[t+1]);e[t]=s.left,e[t+1]=s.right},decryptBlock:function(e,t){var s=function(e,t,s){let r,n=t,a=s;for(let t=17;t>1;--t)n^=e.pbox[t],a=i(e,n)^a,r=n,n=a,a=r;return r=n,n=a,a=r^e.pbox[1],{left:n^=e.pbox[0],right:a}}(a,e[t],e[t+1]);e[t]=s.left,e[t+1]=s.right},blockSize:2,keySize:4,ivSize:2});r.Blowfish=e._createHelper(l)}(),e.exports=r.Blowfish},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},16279:function(e,t,s){var r,n,a,i,o;r=s(98846),s(51398),n=r.lib.WordArray,i=(a=r.algo).SHA256,o=a.SHA224=i.extend({_doReset:function(){this._hash=new n.init([0xc1059ed8,0x367cd507,0x3070dd17,0xf70e5939,0xffc00b31,0x68581511,0x64f98fa7,0xbefa4fa4])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=4,e}}),r.SHA224=i._createHelper(o),r.HmacSHA224=i._createHmacHelper(o),e.exports=r.SHA224},18796:function(e,t,s){var r,n,a,i,o,l,c;a=(n=(r=s(98846)).lib).WordArray,i=n.Hasher,o=r.algo,l=[],c=o.SHA1=i.extend({_doReset:function(){this._hash=new a.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var s=this._hash.words,r=s[0],n=s[1],a=s[2],i=s[3],o=s[4],c=0;c<80;c++){if(c<16)l[c]=0|e[t+c];else{var d=l[c-3]^l[c-8]^l[c-14]^l[c-16];l[c]=d<<1|d>>>31}var h=(r<<5|r>>>27)+o+l[c];c<20?h+=(n&a|~n&i)+0x5a827999:c<40?h+=(n^a^i)+0x6ed9eba1:c<60?h+=(n&a|n&i|a&i)-0x70e44324:h+=(n^a^i)-0x359d3e2a,o=i,i=a,a=n<<30|n>>>2,n=r,r=h}s[0]=s[0]+r|0,s[1]=s[1]+n|0,s[2]=s[2]+a|0,s[3]=s[3]+i|0,s[4]=s[4]+o|0},_doFinalize:function(){var e=this._data,t=e.words,s=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[(r+64>>>9<<4)+14]=Math.floor(s/0x100000000),t[(r+64>>>9<<4)+15]=s,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=i._createHelper(c),r.HmacSHA1=i._createHmacHelper(c),e.exports=r.SHA1},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22938:function(e,t,s){var r,n,a;e.exports=void(n=(r=s(98846)).lib.Base,a=r.enc.Utf8,r.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var s=e.blockSize,r=4*s;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),i=this._iKey=t.clone(),o=n.words,l=i.words,c=0;c<s;c++)o[c]^=0x5c5c5c5c,l[c]^=0x36363636;n.sigBytes=i.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,s=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(s))}}))},23135:function(e,t,s){var r,n,a,i,o;a=(n=(r=s(98846)).lib).Base,i=n.WordArray,(o=r.x64={}).Word=a.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=a.extend({init:function(e,t){e=this.words=e||[],void 0!=t?this.sigBytes=t:this.sigBytes=8*e.length},toX32:function(){for(var e=this.words,t=e.length,s=[],r=0;r<t;r++){var n=e[r];s.push(n.high),s.push(n.low)}return i.create(s,this.sigBytes)},clone:function(){for(var e=a.clone.call(this),t=e.words=this.words.slice(0),s=t.length,r=0;r<s;r++)t[r]=t[r].clone();return e}}),e.exports=r},24613:function(e,t,s){var r,n,a,i,o,l,c;r=s(98846),s(18796),s(22938),a=(n=r.lib).Base,i=n.WordArray,l=(o=r.algo).MD5,c=o.EvpKDF=a.extend({cfg:a.extend({keySize:4,hasher:l,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var s,r=this.cfg,n=r.hasher.create(),a=i.create(),o=a.words,l=r.keySize,c=r.iterations;o.length<l;){s&&n.update(s),s=n.update(e).finalize(t),n.reset();for(var d=1;d<c;d++)s=n.finalize(s),n.reset();a.concat(s)}return a.sigBytes=4*l,a}}),r.EvpKDF=function(e,t,s){return c.create(s).compute(e,t)},e.exports=r.EvpKDF},25353:function(e,t,s){e.exports=function(e){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,s=t.init;(t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,r=[],n=0;n<t;n++)r[n>>>2]|=e[n]<<24-n%4*8;s.call(this,r,t)}else s.apply(this,arguments)}).prototype=t}return e.lib.WordArray}(s(98846))},25408:function(e,t,s){var r,n,a,i,o,l,c,d;r=s(98846),s(51398),s(22938),a=(n=r.lib).Base,i=n.WordArray,l=(o=r.algo).SHA256,c=o.HMAC,d=o.PBKDF2=a.extend({cfg:a.extend({keySize:4,hasher:l,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var s=this.cfg,r=c.create(s.hasher,e),n=i.create(),a=i.create([1]),o=n.words,l=a.words,d=s.keySize,h=s.iterations;o.length<d;){var u=r.update(t).finalize(a);r.reset();for(var f=u.words,p=f.length,x=u,m=1;m<h;m++){x=r.finalize(x),r.reset();for(var g=x.words,b=0;b<p;b++)f[b]^=g[b]}n.concat(u),l[0]++}return n.sigBytes=4*d,n}}),r.PBKDF2=function(e,t,s){return d.create(s).compute(e,t)},e.exports=r.PBKDF2},27910:e=>{"use strict";e.exports=require("stream")},28165:function(e,t,s){var r,n;n=(r=s(98846)).lib.WordArray,r.enc.Base64={stringify:function(e){var t=e.words,s=e.sigBytes,r=this._map;e.clamp();for(var n=[],a=0;a<s;a+=3)for(var i=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,o=0;o<4&&a+.75*o<s;o++)n.push(r.charAt(i>>>6*(3-o)&63));var l=r.charAt(64);if(l)for(;n.length%4;)n.push(l);return n.join("")},parse:function(e){var t=e.length,s=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var a=0;a<s.length;a++)r[s.charCodeAt(a)]=a}var i=s.charAt(64);if(i){var o=e.indexOf(i);-1!==o&&(t=o)}for(var l=e,c=t,d=r,h=[],u=0,f=0;f<c;f++)if(f%4){var p=d[l.charCodeAt(f-1)]<<f%4*2|d[l.charCodeAt(f)]>>>6-f%4*2;h[u>>>2]|=p<<24-u%4*8,u++}return n.create(h,u)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.exports=r.enc.Base64},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,s)=>{"use strict";s.d(t,{default:()=>n.a});var r=s(31261),n=s.n(r)},30671:function(e,t,s){var r;r=s(98846),s(86348),r.mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function t(e){if((e>>24&255)==255){var t=e>>16&255,s=e>>8&255,r=255&e;255===t?(t=0,255===s?(s=0,255===r?r=0:++r):++s):++t,e=0+(t<<16)+(s<<8)+r}else e+=0x1000000;return e}var s=e.Encryptor=e.extend({processBlock:function(e,s){var r,n=this._cipher,a=n.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0),0===((r=o)[0]=t(r[0]))&&(r[1]=t(r[1]));var l=o.slice(0);n.encryptBlock(l,0);for(var c=0;c<a;c++)e[s+c]^=l[c]}});return e.Decryptor=s,e}(),e.exports=r.mode.CTRGladman},31261:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return l},getImageProps:function(){return o}});let r=s(37366),n=s(44953),a=s(46533),i=r._(s(1933));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let l=a.Image},31769:(e,t,s)=>{"use strict";s.d(t,{A:()=>c,I:()=>d});var r=s(60687),n=s(43210),a=s(85814),i=s.n(a),o=s(51108),l=s(53836);function c(){let{user:e}=(0,o.A)(),[t,s]=(0,n.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||t)return null;let{message:a,type:c,daysRemaining:d}=(0,l.Mo)(e);if(!a)return null;let h=()=>{switch(c){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,r.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(c){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:"error"===c?(0,r.jsx)("svg",{className:`h-5 w-5 ${h()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===c?(0,r.jsx)("svg",{className:`h-5 w-5 ${h()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,r.jsx)("svg",{className:`h-5 w-5 ${h()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:a})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(i(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(c){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,r.jsx)("button",{onClick:()=>s(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,r.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function d(){let{user:e}=(0,o.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:t}=(0,l.Mo)(e);return t<=0?(0,r.jsx)(i(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,r.jsxs)(i(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${t<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[t," day",1===t?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36401:function(e,t,s){var r;r=s(98846),function(e){var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.algo,i=s.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=s.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=s.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=s.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),d=s.create([0,0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xa953fd4e]),h=s.create([0x50a28be6,0x5c4dd124,0x6d703ef3,0x7a6d76e9,0]),u=a.RIPEMD160=n.extend({_doReset:function(){this._hash=s.create([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var s,r,n,a,u,p,x,m,g,b,y,w,_,v,k,S,A,R,I,P=0;P<16;P++){var $=t+P,C=e[$];e[$]=(C<<8|C>>>24)&0xff00ff|(C<<24|C>>>8)&0xff00ff00}var E=this._hash.words,O=d.words,B=h.words,N=i.words,j=o.words,M=l.words,T=c.words;v=g=E[0],k=b=E[1],S=y=E[2],A=w=E[3],R=_=E[4];for(var P=0;P<80;P+=1){I=g+e[t+N[P]]|0,P<16?I+=(b^y^w)+O[0]:P<32?I+=((s=b)&y|~s&w)+O[1]:P<48?I+=((b|~y)^w)+O[2]:P<64?I+=(r=b,n=y,(r&(a=w)|n&~a)+O[3]):I+=(b^(y|~w))+O[4],I|=0,I=(I=f(I,M[P]))+_|0,g=_,_=w,w=f(y,10),y=b,b=I,I=v+e[t+j[P]]|0,P<16?I+=(k^(S|~A))+B[0]:P<32?I+=(u=k,p=S,(u&(x=A)|p&~x)+B[1]):P<48?I+=((k|~S)^A)+B[2]:P<64?I+=((m=k)&S|~m&A)+B[3]:I+=(k^S^A)+B[4],I|=0,I=(I=f(I,T[P]))+R|0,v=R,R=A,A=f(S,10),S=k,k=I}I=E[1]+y+A|0,E[1]=E[2]+w+R|0,E[2]=E[3]+_+v|0,E[3]=E[4]+g+k|0,E[4]=E[0]+b+S|0,E[0]=I},_doFinalize:function(){var e=this._data,t=e.words,s=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[(r+64>>>9<<4)+14]=(s<<8|s>>>24)&0xff00ff|(s<<24|s>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();for(var n=this._hash,a=n.words,i=0;i<5;i++){var o=a[i];a[i]=(o<<8|o>>>24)&0xff00ff|(o<<24|o>>>8)&0xff00ff00}return n},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function f(e,t){return e<<t|e>>>32-t}r.RIPEMD160=n._createHelper(u),r.HmacRIPEMD160=n._createHmacHelper(u)}(Math),e.exports=r.RIPEMD160},37085:function(e,t,s){var r;r=s(98846),s(86348),r.pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([0x80000000],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},e.exports=r.pad.Iso97971},39914:function(e,t,s){var r,n,a;r=s(98846),s(86348),n=r.lib.CipherParams,a=r.enc.Hex,r.format.Hex={stringify:function(e){return e.ciphertext.toString(a)},parse:function(e){var t=a.parse(e);return n.create({ciphertext:t})}},e.exports=r.format.Hex},40465:function(e,t,s){var r;r=s(98846),function(e){for(var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.algo,i=[],o=0;o<64;o++)i[o]=0x100000000*e.abs(e.sin(o+1))|0;var l=a.MD5=n.extend({_doReset:function(){this._hash=new s.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476])},_doProcessBlock:function(e,t){for(var s=0;s<16;s++){var r=t+s,n=e[r];e[r]=(n<<8|n>>>24)&0xff00ff|(n<<24|n>>>8)&0xff00ff00}var a=this._hash.words,o=e[t+0],l=e[t+1],f=e[t+2],p=e[t+3],x=e[t+4],m=e[t+5],g=e[t+6],b=e[t+7],y=e[t+8],w=e[t+9],_=e[t+10],v=e[t+11],k=e[t+12],S=e[t+13],A=e[t+14],R=e[t+15],I=a[0],P=a[1],$=a[2],C=a[3];I=c(I,P,$,C,o,7,i[0]),C=c(C,I,P,$,l,12,i[1]),$=c($,C,I,P,f,17,i[2]),P=c(P,$,C,I,p,22,i[3]),I=c(I,P,$,C,x,7,i[4]),C=c(C,I,P,$,m,12,i[5]),$=c($,C,I,P,g,17,i[6]),P=c(P,$,C,I,b,22,i[7]),I=c(I,P,$,C,y,7,i[8]),C=c(C,I,P,$,w,12,i[9]),$=c($,C,I,P,_,17,i[10]),P=c(P,$,C,I,v,22,i[11]),I=c(I,P,$,C,k,7,i[12]),C=c(C,I,P,$,S,12,i[13]),$=c($,C,I,P,A,17,i[14]),P=c(P,$,C,I,R,22,i[15]),I=d(I,P,$,C,l,5,i[16]),C=d(C,I,P,$,g,9,i[17]),$=d($,C,I,P,v,14,i[18]),P=d(P,$,C,I,o,20,i[19]),I=d(I,P,$,C,m,5,i[20]),C=d(C,I,P,$,_,9,i[21]),$=d($,C,I,P,R,14,i[22]),P=d(P,$,C,I,x,20,i[23]),I=d(I,P,$,C,w,5,i[24]),C=d(C,I,P,$,A,9,i[25]),$=d($,C,I,P,p,14,i[26]),P=d(P,$,C,I,y,20,i[27]),I=d(I,P,$,C,S,5,i[28]),C=d(C,I,P,$,f,9,i[29]),$=d($,C,I,P,b,14,i[30]),P=d(P,$,C,I,k,20,i[31]),I=h(I,P,$,C,m,4,i[32]),C=h(C,I,P,$,y,11,i[33]),$=h($,C,I,P,v,16,i[34]),P=h(P,$,C,I,A,23,i[35]),I=h(I,P,$,C,l,4,i[36]),C=h(C,I,P,$,x,11,i[37]),$=h($,C,I,P,b,16,i[38]),P=h(P,$,C,I,_,23,i[39]),I=h(I,P,$,C,S,4,i[40]),C=h(C,I,P,$,o,11,i[41]),$=h($,C,I,P,p,16,i[42]),P=h(P,$,C,I,g,23,i[43]),I=h(I,P,$,C,w,4,i[44]),C=h(C,I,P,$,k,11,i[45]),$=h($,C,I,P,R,16,i[46]),P=h(P,$,C,I,f,23,i[47]),I=u(I,P,$,C,o,6,i[48]),C=u(C,I,P,$,b,10,i[49]),$=u($,C,I,P,A,15,i[50]),P=u(P,$,C,I,m,21,i[51]),I=u(I,P,$,C,k,6,i[52]),C=u(C,I,P,$,p,10,i[53]),$=u($,C,I,P,_,15,i[54]),P=u(P,$,C,I,l,21,i[55]),I=u(I,P,$,C,y,6,i[56]),C=u(C,I,P,$,R,10,i[57]),$=u($,C,I,P,g,15,i[58]),P=u(P,$,C,I,S,21,i[59]),I=u(I,P,$,C,x,6,i[60]),C=u(C,I,P,$,v,10,i[61]),$=u($,C,I,P,f,15,i[62]),P=u(P,$,C,I,w,21,i[63]),a[0]=a[0]+I|0,a[1]=a[1]+P|0,a[2]=a[2]+$|0,a[3]=a[3]+C|0},_doFinalize:function(){var t=this._data,s=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;s[n>>>5]|=128<<24-n%32;var a=e.floor(r/0x100000000);s[(n+64>>>9<<4)+15]=(a<<8|a>>>24)&0xff00ff|(a<<24|a>>>8)&0xff00ff00,s[(n+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,t.sigBytes=(s.length+1)*4,this._process();for(var i=this._hash,o=i.words,l=0;l<4;l++){var c=o[l];o[l]=(c<<8|c>>>24)&0xff00ff|(c<<24|c>>>8)&0xff00ff00}return i},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,s,r,n,a,i){var o=e+(t&s|~t&r)+n+i;return(o<<a|o>>>32-a)+t}function d(e,t,s,r,n,a,i){var o=e+(t&r|s&~r)+n+i;return(o<<a|o>>>32-a)+t}function h(e,t,s,r,n,a,i){var o=e+(t^s^r)+n+i;return(o<<a|o>>>32-a)+t}function u(e,t,s,r,n,a,i){var o=e+(s^(t|~r))+n+i;return(o<<a|o>>>32-a)+t}r.MD5=n._createHelper(l),r.HmacMD5=n._createHmacHelper(l)}(Math),e.exports=r.MD5},44174:function(e,t,s){var r;r=s(98846),s(86348),r.pad.Iso10126={pad:function(e,t){var s=4*t,n=s-e.sigBytes%s;e.concat(r.lib.WordArray.random(n-1)).concat(r.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.exports=r.pad.Iso10126},45422:function(e,t,s){var r,n,a,i,o,l,c;r=s(98846),s(23135),s(9095),a=(n=r.x64).Word,i=n.WordArray,l=(o=r.algo).SHA512,c=o.SHA384=l.extend({_doReset:function(){this._hash=new i.init([new a.init(0xcbbb9d5d,0xc1059ed8),new a.init(0x629a292a,0x367cd507),new a.init(0x9159015a,0x3070dd17),new a.init(0x152fecd8,0xf70e5939),new a.init(0x67332667,0xffc00b31),new a.init(0x8eb44a87,0x68581511),new a.init(0xdb0c2e0d,0x64f98fa7),new a.init(0x47b5481d,0xbefa4fa4)])},_doFinalize:function(){var e=l._doFinalize.call(this);return e.sigBytes-=16,e}}),r.SHA384=l._createHelper(c),r.HmacSHA384=l._createHmacHelper(c),e.exports=r.SHA384},50584:function(e,t,s){var r,n;n=(r=s(98846)).lib.WordArray,r.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var s=e.words,r=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var a=[],i=0;i<r;i+=3)for(var o=(s[i>>>2]>>>24-i%4*8&255)<<16|(s[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|s[i+2>>>2]>>>24-(i+2)%4*8&255,l=0;l<4&&i+.75*l<r;l++)a.push(n.charAt(o>>>6*(3-l)&63));var c=n.charAt(64);if(c)for(;a.length%4;)a.push(c);return a.join("")},parse:function(e,t){void 0===t&&(t=!0);var s=e.length,r=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<r.length;i++)a[r.charCodeAt(i)]=i}var o=r.charAt(64);if(o){var l=e.indexOf(o);-1!==l&&(s=l)}for(var c=e,d=s,h=a,u=[],f=0,p=0;p<d;p++)if(p%4){var x=h[c.charCodeAt(p-1)]<<p%4*2|h[c.charCodeAt(p)]>>>6-p%4*2;u[f>>>2]|=x<<24-f%4*8,f++}return n.create(u,f)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},e.exports=r.enc.Base64url},51150:function(e,t,s){var r;r=s(98846),s(86348),r.pad.AnsiX923={pad:function(e,t){var s=e.sigBytes,r=4*t,n=r-s%r,a=s+n-1;e.clamp(),e.words[a>>>2]|=n<<24-a%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.exports=r.pad.Ansix923},51398:function(e,t,s){var r;r=s(98846),function(e){var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.algo,i=[],o=[];function l(e){return(e-(0|e))*0x100000000|0}for(var c=2,d=0;d<64;)(function(t){for(var s=e.sqrt(t),r=2;r<=s;r++)if(!(t%r))return!1;return!0})(c)&&(d<8&&(i[d]=l(e.pow(c,.5))),o[d]=l(e.pow(c,1/3)),d++),c++;var h=[],u=a.SHA256=n.extend({_doReset:function(){this._hash=new s.init(i.slice(0))},_doProcessBlock:function(e,t){for(var s=this._hash.words,r=s[0],n=s[1],a=s[2],i=s[3],l=s[4],c=s[5],d=s[6],u=s[7],f=0;f<64;f++){if(f<16)h[f]=0|e[t+f];else{var p=h[f-15],x=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,m=h[f-2],g=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;h[f]=x+h[f-7]+g+h[f-16]}var b=l&c^~l&d,y=r&n^r&a^n&a,w=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),_=u+((l<<26|l>>>6)^(l<<21|l>>>11)^(l<<7|l>>>25))+b+o[f]+h[f],v=w+y;u=d,d=c,c=l,l=i+_|0,i=a,a=n,n=r,r=_+v|0}s[0]=s[0]+r|0,s[1]=s[1]+n|0,s[2]=s[2]+a|0,s[3]=s[3]+i|0,s[4]=s[4]+l|0,s[5]=s[5]+c|0,s[6]=s[6]+d|0,s[7]=s[7]+u|0},_doFinalize:function(){var t=this._data,s=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return s[n>>>5]|=128<<24-n%32,s[(n+64>>>9<<4)+14]=e.floor(r/0x100000000),s[(n+64>>>9<<4)+15]=r,t.sigBytes=4*s.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=n._createHelper(u),r.HmacSHA256=n._createHmacHelper(u)}(Math),e.exports=r.SHA256},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65028:function(e,t,s){var r,n,a;r=s(98846),s(86348),r.mode.CTR=(a=(n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){var s=this._cipher,r=s.blockSize,n=this._iv,a=this._counter;n&&(a=this._counter=n.slice(0),this._iv=void 0);var i=a.slice(0);s.encryptBlock(i,0),a[r-1]=a[r-1]+1|0;for(var o=0;o<r;o++)e[t+o]^=i[o]}}),n.Decryptor=a,n),e.exports=r.mode.CTR},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74920:(e,t,s)=>{Promise.resolve().then(s.bind(s,74959))},74959:(e,t,s)=>{"use strict";let r,n,a,i,o,l;s.r(t),s.d(t,{default:()=>aT});var c,d,h,u,f,p,x,m,g,b,y,w,_,v,k,S,A,R,I,P,$,C,E,O,B,N,j,M,T,D,L,W,U,H,q,F,z,K,X,J,V,G,Q,Y,Z,ee,et,es,er,en,ea,ei,eo,el,ec,ed,eh,eu,ef,ep,ex,em,eg,eb,ey,ew,e_,ev,ek,eS,eA,eR,eI,eP,e$,eC,eE,eO,eB,eN,ej,eM,eT,eD,eL,eW,eU,eH,eq,eF,ez,eK,eX,eJ,eV,eG,eQ,eY,eZ,e0,e1,e2,e4,e8,e6,e5,e3,e9,e7,te,tt,ts,tr,tn,ta,ti,to,tl,tc,td=s(60687),th=s(43210),tu=s(51108),tf=s(16189),tp=s(5481),tx=s(75535),tm=s(56304);function tg(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function tb(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}let ty=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return ty=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function tw(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let t_=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class tv extends Error{}class tk extends tv{constructor(e,t,s,r){super(`${tk.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("request-id"),this.error=t}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){return e&&r?400===e?new tI(e,t,s,r):401===e?new tP(e,t,s,r):403===e?new t$(e,t,s,r):404===e?new tC(e,t,s,r):409===e?new tE(e,t,s,r):422===e?new tO(e,t,s,r):429===e?new tB(e,t,s,r):e>=500?new tN(e,t,s,r):new tk(e,t,s,r):new tA({message:s,cause:t_(t)})}}class tS extends tk{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class tA extends tk{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class tR extends tA{constructor({message:e}={}){super({message:e??"Request timed out."})}}class tI extends tk{}class tP extends tk{}class t$ extends tk{}class tC extends tk{}class tE extends tk{}class tO extends tk{}class tB extends tk{}class tN extends tk{}let tj=/^[a-z][a-z0-9+.-]*:/i,tM=e=>tj.test(e);function tT(e){return"object"!=typeof e?{}:e??{}}let tD=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new tv(`${e} must be an integer`);if(t<0)throw new tv(`${e} must be a positive integer`);return t},tL=e=>{try{return JSON.parse(e)}catch(e){return}},tW=e=>new Promise(t=>setTimeout(t,e)),tU={off:0,error:200,warn:300,info:400,debug:500},tH=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(tU,e))return e;tX(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(tU))}`)}};function tq(){}function tF(e,t,s){return!t||tU[e]>tU[s]?tq:t[e].bind(t)}let tz={error:tq,warn:tq,info:tq,debug:tq},tK=new WeakMap;function tX(e){let t=e.logger,s=e.logLevel??"off";if(!t)return tz;let r=tK.get(t);if(r&&r[0]===s)return r[1];let n={error:tF("error",t,s),warn:tF("warn",t,s),info:tF("info",t,s),debug:tF("debug",t,s)};return tK.set(t,[s,n]),n}let tJ=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"x-api-key"===e.toLowerCase()||"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),tV="0.52.0",tG=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,tQ=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":tZ(Deno.build.os),"X-Stainless-Arch":tY(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":tZ(globalThis.process.platform),"X-Stainless-Arch":tY(globalThis.process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},tY=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tZ=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",t0=()=>r??(r=tQ());function t1(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function t2(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return t1({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function t4(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function t8(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let t6=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)});function t5(e){let t;return(n??(n=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function t3(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class t9{constructor(){c.set(this,void 0),d.set(this,void 0),tg(this,c,new Uint8Array,"f"),tg(this,d,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?t5(e):e;tg(this,c,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([tb(this,c,"f"),s]),"f");let r=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(tb(this,c,"f"),tb(this,d,"f")));){if(t.carriage&&null==tb(this,d,"f")){tg(this,d,t.index,"f");continue}if(null!=tb(this,d,"f")&&(t.index!==tb(this,d,"f")+1||t.carriage)){r.push(t3(tb(this,c,"f").subarray(0,tb(this,d,"f")-1))),tg(this,c,tb(this,c,"f").subarray(tb(this,d,"f")),"f"),tg(this,d,null,"f");continue}let e=null!==tb(this,d,"f")?t.preceding-1:t.preceding,s=t3(tb(this,c,"f").subarray(0,e));r.push(s),tg(this,c,tb(this,c,"f").subarray(t.index),"f"),tg(this,d,null,"f")}return r}flush(){return tb(this,c,"f").length?this.decode("\n"):[]}}c=new WeakMap,d=new WeakMap,t9.NEWLINE_CHARS=new Set(["\n","\r"]),t9.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class t7{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*r(){if(s)throw new tv("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of se(e,t)){if("completion"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("message_start"===s.event||"message_delta"===s.event||"message_stop"===s.event||"content_block_start"===s.event||"content_block_delta"===s.event||"content_block_stop"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("ping"!==s.event&&"error"===s.event)throw new tk(void 0,tL(s.data)??s.data,void 0,e.headers)}r=!0}catch(e){if(tw(e))return;throw e}finally{r||t.abort()}}return new t7(r,t)}static fromReadableStream(e,t){let s=!1;async function*r(){let t=new t9;for await(let s of t4(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new t7(async function*(){if(s)throw new tv("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(tw(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new t7(()=>r(e),this.controller),new t7(()=>r(t),this.controller)]}toReadableStream(){let e,t=this;return t1({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=t5(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*se(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new tv("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new tv("Attempted to iterate over a response with no body")}let s=new ss,r=new t9;for await(let t of st(t4(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*st(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?t5(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class ss{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function sr(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(tX(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):t7.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?sn(await s.json(),s):await s.text()})();return tX(e).debug(`[${r}] response parsed`,tJ({retryOfRequestLogID:n,url:s.url,status:s.status,body:i,durationMs:Date.now()-a})),i}function sn(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("request-id"),enumerable:!1})}class sa extends Promise{constructor(e,t,s=sr){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,h.set(this,void 0),tg(this,h,e,"f")}_thenUnwrap(e){return new sa(tb(this,h,"f"),this.responsePromise,async(t,s)=>sn(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(tb(this,h,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}h=new WeakMap;class si{constructor(e,t,s,r){u.set(this,void 0),tg(this,u,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new tv("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await tb(this,u,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(u=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class so extends sa{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await sr(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class sl extends si{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1,this.first_id=s.first_id||null,this.last_id=s.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){if(this.options.query?.before_id){let e=this.first_id;return e?{...this.options,query:{...tT(this.options.query),before_id:e}}:null}let e=this.last_id;return e?{...this.options,query:{...tT(this.options.query),after_id:e}}:null}}let sc=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function sd(e,t,s){return sc(),new File(e,t??"unknown_file",s)}function sh(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let su=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],sf=async(e,t)=>({...e,body:await sx(e.body,t)}),sp=new WeakMap,sx=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=sp.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return sp.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>sy(s,e,t))),s},sm=e=>e instanceof Blob&&"name"in e,sg=e=>"object"==typeof e&&null!==e&&(e instanceof Response||su(e)||sm(e)),sb=e=>{if(sg(e))return!0;if(Array.isArray(e))return e.some(sb);if(e&&"object"==typeof e){for(let t in e)if(sb(e[t]))return!0}return!1},sy=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response){let r={},n=s.headers.get("Content-Type");n&&(r={type:n}),e.append(t,sd([await s.blob()],sh(s),r))}else if(su(s))e.append(t,sd([await new Response(t2(s)).blob()],sh(s)));else if(sm(s))e.append(t,sd([s],sh(s),{type:s.type}));else if(Array.isArray(s))await Promise.all(s.map(s=>sy(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>sy(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},sw=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,s_=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&sw(e),sv=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function sk(e,t,s){if(sc(),e=await e,t||(t=sh(e)),s_(e))return e instanceof File&&null==t&&null==s?e:sd([await e.arrayBuffer()],t??e.name,{type:e.type,lastModified:e.lastModified,...s});if(sv(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),sd(await sS(r),t,s)}let r=await sS(e);if(!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return sd(r,t,s)}async function sS(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(sw(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(su(e))for await(let s of e)t.push(...await sS(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class sA{constructor(e){this._client=e}}let sR=Symbol.for("brand.privateNullableHeaders"),sI=Array.isArray,sP=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(sR in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():sI(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=sI(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===a?(t.delete(n),s.add(r)):(t.append(n,a),s.delete(r))}}return{[sR]:!0,values:t,nulls:s}};function s$(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let sC=((e=s$)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,r,a)=>(/[?#]/.test(r)&&(n=!0),t+r+(a===s.length?"":(n?encodeURIComponent:e)(String(s[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(i));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new tv(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(s$);class sE extends sA{list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/files",sl,{query:r,...t,headers:sP([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(sC`/v1/files/${e}`,{...s,headers:sP([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}download(e,t={},s){let{betas:r}=t??{};return this._client.get(sC`/v1/files/${e}/content`,{...s,headers:sP([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},s){let{betas:r}=t??{};return this._client.get(sC`/v1/files/${e}`,{...s,headers:sP([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}upload(e,t){let{betas:s,...r}=e;return this._client.post("/v1/files",sf({body:r,...t,headers:sP([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])},this._client))}}class sO extends sA{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(sC`/v1/models/${e}?beta=true`,{...s,headers:sP([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models?beta=true",sl,{query:r,...t,headers:sP([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}class sB{constructor(e,t){this.iterator=e,this.controller=t}async *decoder(){let e=new t9;for await(let t of this.iterator)for(let s of e.decode(t))yield JSON.parse(s);for(let t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new tv("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new tv("Attempted to iterate over a response with no body")}return new sB(t4(e.body),t)}}class sN extends sA{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/batches?beta=true",{body:r,...t,headers:sP([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(sC`/v1/messages/batches/${e}?beta=true`,{...s,headers:sP([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/messages/batches?beta=true",sl,{query:r,...t,headers:sP([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(sC`/v1/messages/batches/${e}?beta=true`,{...s,headers:sP([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}cancel(e,t={},s){let{betas:r}=t??{};return this._client.post(sC`/v1/messages/batches/${e}/cancel?beta=true`,{...s,headers:sP([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}async results(e,t={},s){let r=await this.retrieve(e);if(!r.results_url)throw new tv(`No batch \`results_url\`; Has it finished processing? ${r.processing_status} - ${r.id}`);let{betas:n}=t??{};return this._client.get(r.results_url,{...s,headers:sP([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},s?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>sB.fromResponse(t.response,t.controller))}}let sj=e=>{let t=0,s=[];for(;t<e.length;){let r=e[t];if("\\"===r){t++;continue}if("{"===r){s.push({type:"brace",value:"{"}),t++;continue}if("}"===r){s.push({type:"brace",value:"}"}),t++;continue}if("["===r){s.push({type:"paren",value:"["}),t++;continue}if("]"===r){s.push({type:"paren",value:"]"}),t++;continue}if(":"===r){s.push({type:"separator",value:":"}),t++;continue}if(","===r){s.push({type:"delimiter",value:","}),t++;continue}if('"'===r){let n="",a=!1;for(r=e[++t];'"'!==r;){if(t===e.length){a=!0;break}if("\\"===r){if(++t===e.length){a=!0;break}n+=r+e[t],r=e[++t]}else n+=r,r=e[++t]}r=e[++t],a||s.push({type:"string",value:n});continue}let n=/\s/;if(r&&n.test(r)){t++;continue}let a=/[0-9]/;if(r&&a.test(r)||"-"===r||"."===r){let n="";for("-"===r&&(n+=r,r=e[++t]);r&&a.test(r)||"."===r;)n+=r,r=e[++t];s.push({type:"number",value:n});continue}let i=/[a-z]/i;if(r&&i.test(r)){let n="";for(;r&&i.test(r)&&t!==e.length;)n+=r,r=e[++t];"true"==n||"false"==n||"null"===n?s.push({type:"name",value:n}):t++;continue}t++}return s},sM=e=>{if(0===e.length)return e;let t=e[e.length-1];switch(t.type){case"separator":return sM(e=e.slice(0,e.length-1));case"number":let s=t.value[t.value.length-1];if("."===s||"-"===s)return sM(e=e.slice(0,e.length-1));case"string":let r=e[e.length-2];if(r?.type==="delimiter"||r?.type==="brace"&&"{"===r.value)return sM(e=e.slice(0,e.length-1));break;case"delimiter":return sM(e=e.slice(0,e.length-1))}return e},sT=e=>{let t=[];return e.map(e=>{"brace"===e.type&&("{"===e.value?t.push("}"):t.splice(t.lastIndexOf("}"),1)),"paren"===e.type&&("["===e.value?t.push("]"):t.splice(t.lastIndexOf("]"),1))}),t.length>0&&t.reverse().map(t=>{"}"===t?e.push({type:"brace",value:"}"}):"]"===t&&e.push({type:"paren",value:"]"})}),e},sD=e=>{let t="";return e.map(e=>{"string"===e.type?t+='"'+e.value+'"':t+=e.value}),t},sL=e=>JSON.parse(sD(sT(sM(sj(e))))),sW="__json_buf";class sU{constructor(){f.add(this),this.messages=[],this.receivedMessages=[],p.set(this,void 0),this.controller=new AbortController,x.set(this,void 0),m.set(this,()=>{}),g.set(this,()=>{}),b.set(this,void 0),y.set(this,()=>{}),w.set(this,()=>{}),_.set(this,{}),v.set(this,!1),k.set(this,!1),S.set(this,!1),A.set(this,!1),R.set(this,void 0),I.set(this,void 0),C.set(this,e=>{if(tg(this,k,!0,"f"),tw(e)&&(e=new tS),e instanceof tS)return tg(this,S,!0,"f"),this._emit("abort",e);if(e instanceof tv)return this._emit("error",e);if(e instanceof Error){let t=new tv(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new tv(String(e)))}),tg(this,x,new Promise((e,t)=>{tg(this,m,e,"f"),tg(this,g,t,"f")}),"f"),tg(this,b,new Promise((e,t)=>{tg(this,y,e,"f"),tg(this,w,t,"f")}),"f"),tb(this,x,"f").catch(()=>{}),tb(this,b,"f").catch(()=>{})}get response(){return tb(this,R,"f")}get request_id(){return tb(this,I,"f")}async withResponse(){let e=await tb(this,x,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new sU;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new sU;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},tb(this,C,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),tb(this,f,"m",E).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))tb(this,f,"m",O).call(this,e);if(a.controller.signal?.aborted)throw new tS;tb(this,f,"m",B).call(this)}_connected(e){this.ended||(tg(this,R,e,"f"),tg(this,I,e?.headers.get("request-id"),"f"),tb(this,m,"f").call(this,e),this._emit("connect"))}get ended(){return tb(this,v,"f")}get errored(){return tb(this,k,"f")}get aborted(){return tb(this,S,"f")}abort(){this.controller.abort()}on(e,t){return(tb(this,_,"f")[e]||(tb(this,_,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=tb(this,_,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(tb(this,_,"f")[e]||(tb(this,_,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{tg(this,A,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){tg(this,A,!0,"f"),await tb(this,b,"f")}get currentMessage(){return tb(this,p,"f")}async finalMessage(){return await this.done(),tb(this,f,"m",P).call(this)}async finalText(){return await this.done(),tb(this,f,"m",$).call(this)}_emit(e,...t){if(tb(this,v,"f"))return;"end"===e&&(tg(this,v,!0,"f"),tb(this,y,"f").call(this));let s=tb(this,_,"f")[e];if(s&&(tb(this,_,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];tb(this,A,"f")||s?.length||Promise.reject(e),tb(this,g,"f").call(this,e),tb(this,w,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];tb(this,A,"f")||s?.length||Promise.reject(e),tb(this,g,"f").call(this,e),tb(this,w,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",tb(this,f,"m",P).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),tb(this,f,"m",E).call(this),this._connected(null);let r=t7.fromReadableStream(e,this.controller);for await(let e of r)tb(this,f,"m",O).call(this,e);if(r.controller.signal?.aborted)throw new tS;tb(this,f,"m",B).call(this)}[(p=new WeakMap,x=new WeakMap,m=new WeakMap,g=new WeakMap,b=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,v=new WeakMap,k=new WeakMap,S=new WeakMap,A=new WeakMap,R=new WeakMap,I=new WeakMap,C=new WeakMap,f=new WeakSet,P=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},$=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new tv("stream ended without producing a content block with type=text");return e.join(" ")},E=function(){this.ended||tg(this,p,void 0,"f")},O=function(e){if(this.ended)return;let t=tb(this,f,"m",N).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":("tool_use"===s.type||"mcp_tool_use"===s.type)&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":tg(this,p,t,"f")}},B=function(){if(this.ended)throw new tv("stream has ended, this shouldn't happen");let e=tb(this,p,"f");if(!e)throw new tv("request ended without sending any chunks");return tg(this,p,void 0,"f"),e},N=function(e){let t=tb(this,p,"f");if("message_start"===e.type){if(t)throw new tv(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new tv(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.container=e.delta.container,t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"||s?.type==="mcp_tool_use"){let t=s[sW]||"";Object.defineProperty(s,sW,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=sL(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new t7(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}let sH={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},sq={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};class sF extends sA{constructor(){super(...arguments),this.batches=new sN(this._client)}create(e,t){let{betas:s,...r}=e;r.model in sq&&console.warn(`The model '${r.model}' is deprecated and will reach end-of-life on ${sq[r.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let n=this._client._options.timeout;if(!r.stream&&null==n){let e=sH[r.model]??void 0;n=this._client.calculateNonstreamingTimeout(r.max_tokens,e)}return this._client.post("/v1/messages?beta=true",{body:r,timeout:n??6e5,...t,headers:sP([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}stream(e,t){return sU.createMessage(this,e,t)}countTokens(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:r,...t,headers:sP([{"anthropic-beta":[...s??[],"token-counting-2024-11-01"].toString()},t?.headers])})}}sF.Batches=sN;class sz extends sA{constructor(){super(...arguments),this.models=new sO(this._client),this.messages=new sF(this._client),this.files=new sE(this._client)}}sz.Models=sO,sz.Messages=sF,sz.Files=sE;class sK extends sA{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/complete",{body:r,timeout:this._client._options.timeout??6e5,...t,headers:sP([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}}let sX="__json_buf";class sJ{constructor(){j.add(this),this.messages=[],this.receivedMessages=[],M.set(this,void 0),this.controller=new AbortController,T.set(this,void 0),D.set(this,()=>{}),L.set(this,()=>{}),W.set(this,void 0),U.set(this,()=>{}),H.set(this,()=>{}),q.set(this,{}),F.set(this,!1),z.set(this,!1),K.set(this,!1),X.set(this,!1),J.set(this,void 0),V.set(this,void 0),Y.set(this,e=>{if(tg(this,z,!0,"f"),tw(e)&&(e=new tS),e instanceof tS)return tg(this,K,!0,"f"),this._emit("abort",e);if(e instanceof tv)return this._emit("error",e);if(e instanceof Error){let t=new tv(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new tv(String(e)))}),tg(this,T,new Promise((e,t)=>{tg(this,D,e,"f"),tg(this,L,t,"f")}),"f"),tg(this,W,new Promise((e,t)=>{tg(this,U,e,"f"),tg(this,H,t,"f")}),"f"),tb(this,T,"f").catch(()=>{}),tb(this,W,"f").catch(()=>{})}get response(){return tb(this,J,"f")}get request_id(){return tb(this,V,"f")}async withResponse(){let e=await tb(this,T,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new sJ;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new sJ;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},tb(this,Y,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),tb(this,j,"m",Z).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))tb(this,j,"m",ee).call(this,e);if(a.controller.signal?.aborted)throw new tS;tb(this,j,"m",et).call(this)}_connected(e){this.ended||(tg(this,J,e,"f"),tg(this,V,e?.headers.get("request-id"),"f"),tb(this,D,"f").call(this,e),this._emit("connect"))}get ended(){return tb(this,F,"f")}get errored(){return tb(this,z,"f")}get aborted(){return tb(this,K,"f")}abort(){this.controller.abort()}on(e,t){return(tb(this,q,"f")[e]||(tb(this,q,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=tb(this,q,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(tb(this,q,"f")[e]||(tb(this,q,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{tg(this,X,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){tg(this,X,!0,"f"),await tb(this,W,"f")}get currentMessage(){return tb(this,M,"f")}async finalMessage(){return await this.done(),tb(this,j,"m",G).call(this)}async finalText(){return await this.done(),tb(this,j,"m",Q).call(this)}_emit(e,...t){if(tb(this,F,"f"))return;"end"===e&&(tg(this,F,!0,"f"),tb(this,U,"f").call(this));let s=tb(this,q,"f")[e];if(s&&(tb(this,q,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];tb(this,X,"f")||s?.length||Promise.reject(e),tb(this,L,"f").call(this,e),tb(this,H,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];tb(this,X,"f")||s?.length||Promise.reject(e),tb(this,L,"f").call(this,e),tb(this,H,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",tb(this,j,"m",G).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),tb(this,j,"m",Z).call(this),this._connected(null);let r=t7.fromReadableStream(e,this.controller);for await(let e of r)tb(this,j,"m",ee).call(this,e);if(r.controller.signal?.aborted)throw new tS;tb(this,j,"m",et).call(this)}[(M=new WeakMap,T=new WeakMap,D=new WeakMap,L=new WeakMap,W=new WeakMap,U=new WeakMap,H=new WeakMap,q=new WeakMap,F=new WeakMap,z=new WeakMap,K=new WeakMap,X=new WeakMap,J=new WeakMap,V=new WeakMap,Y=new WeakMap,j=new WeakSet,G=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},Q=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new tv("stream ended without producing a content block with type=text");return e.join(" ")},Z=function(){this.ended||tg(this,M,void 0,"f")},ee=function(e){if(this.ended)return;let t=tb(this,j,"m",es).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":"tool_use"===s.type&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":tg(this,M,t,"f")}},et=function(){if(this.ended)throw new tv("stream has ended, this shouldn't happen");let e=tb(this,M,"f");if(!e)throw new tv("request ended without sending any chunks");return tg(this,M,void 0,"f"),e},es=function(e){let t=tb(this,M,"f");if("message_start"===e.type){if(t)throw new tv(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new tv(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"){let t=s[sX]||"";Object.defineProperty(s,sX,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=sL(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new t7(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class sV extends sA{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(sC`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",sl,{query:e,...t})}delete(e,t){return this._client.delete(sC`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(sC`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){let s=await this.retrieve(e);if(!s.results_url)throw new tv(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);return this._client.get(s.results_url,{...t,headers:sP([{Accept:"application/binary"},t?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>sB.fromResponse(t.response,t.controller))}}class sG extends sA{constructor(){super(...arguments),this.batches=new sV(this._client)}create(e,t){e.model in sQ&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${sQ[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let s=this._client._options.timeout;if(!e.stream&&null==s){let t=sH[e.model]??void 0;s=this._client.calculateNonstreamingTimeout(e.max_tokens,t)}return this._client.post("/v1/messages",{body:e,timeout:s??6e5,...t,stream:e.stream??!1})}stream(e,t){return sJ.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}}let sQ={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};sG.Batches=sV;class sY extends sA{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(sC`/v1/models/${e}`,{...s,headers:sP([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models",sl,{query:r,...t,headers:sP([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}let sZ=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class s0{constructor({baseURL:e=sZ("ANTHROPIC_BASE_URL"),apiKey:t=sZ("ANTHROPIC_API_KEY")??null,authToken:s=sZ("ANTHROPIC_AUTH_TOKEN")??null,...r}={}){er.set(this,void 0);let n={apiKey:t,authToken:s,...r,baseURL:e||"https://api.anthropic.com"};if(!n.dangerouslyAllowBrowser&&tG())throw new tv("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\n");this.baseURL=n.baseURL,this.timeout=n.timeout??s1.DEFAULT_TIMEOUT,this.logger=n.logger??console;let a="warn";this.logLevel=a,this.logLevel=tH(n.logLevel,"ClientOptions.logLevel",this)??tH(sZ("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??a,this.fetchOptions=n.fetchOptions,this.maxRetries=n.maxRetries??2,this.fetch=n.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),tg(this,er,t6,"f"),this._options=n,this.apiKey=t,this.authToken=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key")||t.has("x-api-key")||this.authToken&&e.get("authorization"))&&!t.has("authorization"))throw Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(e){return sP([this.apiKeyAuth(e),this.bearerAuth(e)])}apiKeyAuth(e){if(null!=this.apiKey)return sP([{"X-Api-Key":this.apiKey}])}bearerAuth(e){if(null!=this.authToken)return sP([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new tv(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${tV}`}defaultIdempotencyKey(){return`stainless-node-retry-${ty()}`}makeStatusError(e,t,s,r){return tk.generate(e,t,s,r)}buildURL(e,t){let s=new URL(tM(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new tv("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details");return 6e5}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new sa(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:a,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,d=Date.now();if(tX(this).debug(`[${l}] sending request`,tJ({retryOfRequestLogID:s,method:r.method,url:i,options:r,headers:a.headers})),r.signal?.aborted)throw new tS;let h=new AbortController,u=await this.fetchWithTimeout(i,a,o,h).catch(t_),f=Date.now();if(u instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new tS;let n=tw(u)||/timed? ?out/i.test(String(u)+("cause"in u?String(u.cause):""));if(t)return tX(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),tX(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,tJ({retryOfRequestLogID:s,url:i,durationMs:f-d,message:u.message})),this.retryRequest(r,t,s??l);if(tX(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),tX(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,tJ({retryOfRequestLogID:s,url:i,durationMs:f-d,message:u.message})),n)throw new tR;throw new tA({cause:u})}let p=[...u.headers.entries()].filter(([e])=>"request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),x=`[${l}${c}${p}] ${a.method} ${i} ${u.ok?"succeeded":"failed"} with status ${u.status} in ${f-d}ms`;if(!u.ok){let e=this.shouldRetry(u);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await t8(u.body),tX(this).info(`${x} - ${e}`),tX(this).debug(`[${l}] response error (${e})`,tJ({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),this.retryRequest(r,t,s??l,u.headers)}let n=e?"error; no more retries left":"error; not retryable";tX(this).info(`${x} - ${n}`);let a=await u.text().catch(e=>t_(e).message),i=tL(a),o=i?void 0:a;throw tX(this).debug(`[${l}] response error (${n})`,tJ({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,message:o,durationMs:Date.now()-d})),this.makeStatusError(u.status,i,o,u.headers)}return tX(this).info(x),tX(this).debug(`[${l}] response start`,tJ({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),{response:u,options:r,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:d}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new so(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,r){let n,a=r?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=r?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await tW(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||null!=t&&e>t)throw new tv("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:a}=s,i=this.buildURL(n,a);"timeout"in s&&tD("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:i,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=sP([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...t0(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=sP([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:t2(e)}:tb(this,er,"f").call(this,{body:e,headers:s})}}er=new WeakMap,s0.Anthropic=s0,s0.HUMAN_PROMPT="\n\nHuman:",s0.AI_PROMPT="\n\nAssistant:",s0.DEFAULT_TIMEOUT=6e5,s0.AnthropicError=tv,s0.APIError=tk,s0.APIConnectionError=tA,s0.APIConnectionTimeoutError=tR,s0.APIUserAbortError=tS,s0.NotFoundError=tC,s0.ConflictError=tE,s0.RateLimitError=tB,s0.BadRequestError=tI,s0.AuthenticationError=tP,s0.InternalServerError=tN,s0.PermissionDeniedError=t$,s0.UnprocessableEntityError=tO,s0.toFile=sk;class s1 extends s0{constructor(){super(...arguments),this.completions=new sK(this),this.messages=new sG(this),this.models=new sY(this),this.beta=new sz(this)}}s1.Completions=sK,s1.Messages=sG,s1.Models=sY,s1.Beta=sz;let{HUMAN_PROMPT:s2,AI_PROMPT:s4}=s1;function s8(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function s6(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}let s5=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return s5=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function s3(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let s9=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class s7 extends Error{}class re extends s7{constructor(e,t,s,r){super(`${re.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){if(!e||!r)return new rs({message:s,cause:s9(t)});let n=t?.error;return 400===e?new rn(e,n,s,r):401===e?new ra(e,n,s,r):403===e?new ri(e,n,s,r):404===e?new ro(e,n,s,r):409===e?new rl(e,n,s,r):422===e?new rc(e,n,s,r):429===e?new rd(e,n,s,r):e>=500?new rh(e,n,s,r):new re(e,n,s,r)}}class rt extends re{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class rs extends re{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class rr extends rs{constructor({message:e}={}){super({message:e??"Request timed out."})}}class rn extends re{}class ra extends re{}class ri extends re{}class ro extends re{}class rl extends re{}class rc extends re{}class rd extends re{}class rh extends re{}class ru extends s7{constructor(){super("Could not parse response content as the length limit was reached")}}class rf extends s7{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let rp=/^[a-z][a-z0-9+.-]*:/i,rx=e=>rp.test(e);function rm(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let rg=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new s7(`${e} must be an integer`);if(t<0)throw new s7(`${e} must be a positive integer`);return t},rb=e=>{try{return JSON.parse(e)}catch(e){return}},ry=e=>new Promise(t=>setTimeout(t,e)),rw={off:0,error:200,warn:300,info:400,debug:500},r_=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(rw,e))return e;rR(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(rw))}`)}};function rv(){}function rk(e,t,s){return!t||rw[e]>rw[s]?rv:t[e].bind(t)}let rS={error:rv,warn:rv,info:rv,debug:rv},rA=new WeakMap;function rR(e){let t=e.logger,s=e.logLevel??"off";if(!t)return rS;let r=rA.get(t);if(r&&r[0]===s)return r[1];let n={error:rk("error",t,s),warn:rk("warn",t,s),info:rk("info",t,s),debug:rk("debug",t,s)};return rA.set(t,[s,n]),n}let rI=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),rP="5.0.1",r$=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,rC=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":rP,"X-Stainless-OS":rO(Deno.build.os),"X-Stainless-Arch":rE(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":rP,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":rP,"X-Stainless-OS":rO(globalThis.process.platform??"unknown"),"X-Stainless-Arch":rE(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":rP,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":rP,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},rE=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",rO=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",rB=()=>i??(i=rC());function rN(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function rj(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return rN({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function rM(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function rT(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let rD=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),rL="RFC3986",rW={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},rU=(Object.prototype.hasOwnProperty,Array.isArray),rH=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function rq(e,t){if(rU(e)){let s=[];for(let r=0;r<e.length;r+=1)s.push(t(e[r]));return s}return t(e)}let rF=Object.prototype.hasOwnProperty,rz={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},rK=Array.isArray,rX=Array.prototype.push,rJ=function(e,t){rX.apply(e,rK(t)?t:[t])},rV=Date.prototype.toISOString,rG={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,r,n)=>{if(0===e.length)return e;let a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===s)return escape(a).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let i="";for(let e=0;e<a.length;e+=1024){let t=a.length>=1024?a.slice(e,e+1024):a,s=[];for(let e=0;e<t.length;++e){let r=t.charCodeAt(e);if(45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||"RFC1738"===n&&(40===r||41===r)){s[s.length]=t.charAt(e);continue}if(r<128){s[s.length]=rH[r];continue}if(r<2048){s[s.length]=rH[192|r>>6]+rH[128|63&r];continue}if(r<55296||r>=57344){s[s.length]=rH[224|r>>12]+rH[128|r>>6&63]+rH[128|63&r];continue}e+=1,r=65536+((1023&r)<<10|1023&t.charCodeAt(e)),s[s.length]=rH[240|r>>18]+rH[128|r>>12&63]+rH[128|r>>6&63]+rH[128|63&r]}i+=s.join("")}return i},encodeValuesOnly:!1,format:rL,formatter:rW[rL],indices:!1,serializeDate:e=>rV.call(e),skipNulls:!1,strictNullHandling:!1},rQ={};function rY(e){let t;return(o??(o=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function rZ(e){let t;return(l??(l=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class r0{constructor(){en.set(this,void 0),ea.set(this,void 0),s8(this,en,new Uint8Array,"f"),s8(this,ea,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?rY(e):e;s8(this,en,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([s6(this,en,"f"),s]),"f");let r=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(s6(this,en,"f"),s6(this,ea,"f")));){if(t.carriage&&null==s6(this,ea,"f")){s8(this,ea,t.index,"f");continue}if(null!=s6(this,ea,"f")&&(t.index!==s6(this,ea,"f")+1||t.carriage)){r.push(rZ(s6(this,en,"f").subarray(0,s6(this,ea,"f")-1))),s8(this,en,s6(this,en,"f").subarray(s6(this,ea,"f")),"f"),s8(this,ea,null,"f");continue}let e=null!==s6(this,ea,"f")?t.preceding-1:t.preceding,s=rZ(s6(this,en,"f").subarray(0,e));r.push(s),s8(this,en,s6(this,en,"f").subarray(t.index),"f"),s8(this,ea,null,"f")}return r}flush(){return s6(this,en,"f").length?this.decode("\n"):[]}}en=new WeakMap,ea=new WeakMap,r0.NEWLINE_CHARS=new Set(["\n","\r"]),r0.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class r1{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*r(){if(s)throw new s7("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of r2(e,t))if(!r){if(s.data.startsWith("[DONE]")){r=!0;continue}if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if(t&&t.error)throw new re(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new re(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}}r=!0}catch(e){if(s3(e))return;throw e}finally{r||t.abort()}}return new r1(r,t)}static fromReadableStream(e,t){let s=!1;async function*r(){let t=new r0;for await(let s of rM(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new r1(async function*(){if(s)throw new s7("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(s3(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new r1(()=>r(e),this.controller),new r1(()=>r(t),this.controller)]}toReadableStream(){let e,t=this;return rN({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=rY(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*r2(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new s7("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new s7("Attempted to iterate over a response with no body")}let s=new r8,r=new r0;for await(let t of r4(rM(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*r4(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?rY(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class r8{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function r6(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(rR(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):r1.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?r5(await s.json(),s):await s.text()})();return rR(e).debug(`[${r}] response parsed`,rI({retryOfRequestLogID:n,url:s.url,status:s.status,body:i,durationMs:Date.now()-a})),i}function r5(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class r3 extends Promise{constructor(e,t,s=r6){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,ei.set(this,void 0),s8(this,ei,e,"f")}_thenUnwrap(e){return new r3(s6(this,ei,"f"),this.responsePromise,async(t,s)=>r5(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(s6(this,ei,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}ei=new WeakMap;class r9{constructor(e,t,s,r){eo.set(this,void 0),s8(this,eo,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new s7("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await s6(this,eo,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(eo=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class r7 extends r3{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await r6(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class ne extends r9{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class nt extends r9{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let ns=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function nr(e,t,s){return ns(),new File(e,t??"unknown_file",s)}function nn(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let na=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],ni=async(e,t)=>({...e,body:await nl(e.body,t)}),no=new WeakMap,nl=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=no.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return no.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>nu(s,e,t))),s},nc=e=>e instanceof Blob&&"name"in e,nd=e=>"object"==typeof e&&null!==e&&(e instanceof Response||na(e)||nc(e)),nh=e=>{if(nd(e))return!0;if(Array.isArray(e))return e.some(nh);if(e&&"object"==typeof e){for(let t in e)if(nh(e[t]))return!0}return!1},nu=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,nr([await s.blob()],nn(s)));else if(na(s))e.append(t,nr([await new Response(rj(s)).blob()],nn(s)));else if(nc(s))e.append(t,s,nn(s));else if(Array.isArray(s))await Promise.all(s.map(s=>nu(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>nu(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},nf=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,np=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&nf(e),nx=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function nm(e,t,s){if(ns(),np(e=await e))return e instanceof File?e:nr([await e.arrayBuffer()],e.name);if(nx(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),nr(await ng(r),t,s)}let r=await ng(e);if(t||(t=nn(e)),!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return nr(r,t,s)}async function ng(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(nf(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(na(e))for await(let s of e)t.push(...await ng(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class nb{constructor(e){this._client=e}}function ny(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let nw=((e=ny)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,r,a)=>(/[?#]/.test(r)&&(n=!0),t+r+(a===s.length?"":(n?encodeURIComponent:e)(String(s[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(i));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new s7(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(ny);class n_ extends nb{list(e,t={},s){return this._client.getAPIList(nw`/chat/completions/${e}/messages`,nt,{query:t,...s})}}let nv=e=>e?.role==="assistant",nk=e=>e?.role==="tool";class nS{constructor(){el.add(this),this.controller=new AbortController,ec.set(this,void 0),ed.set(this,()=>{}),eh.set(this,()=>{}),eu.set(this,void 0),ef.set(this,()=>{}),ep.set(this,()=>{}),ex.set(this,{}),em.set(this,!1),eg.set(this,!1),eb.set(this,!1),ey.set(this,!1),s8(this,ec,new Promise((e,t)=>{s8(this,ed,e,"f"),s8(this,eh,t,"f")}),"f"),s8(this,eu,new Promise((e,t)=>{s8(this,ef,e,"f"),s8(this,ep,t,"f")}),"f"),s6(this,ec,"f").catch(()=>{}),s6(this,eu,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},s6(this,el,"m",ew).bind(this))},0)}_connected(){this.ended||(s6(this,ed,"f").call(this),this._emit("connect"))}get ended(){return s6(this,em,"f")}get errored(){return s6(this,eg,"f")}get aborted(){return s6(this,eb,"f")}abort(){this.controller.abort()}on(e,t){return(s6(this,ex,"f")[e]||(s6(this,ex,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=s6(this,ex,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(s6(this,ex,"f")[e]||(s6(this,ex,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{s8(this,ey,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){s8(this,ey,!0,"f"),await s6(this,eu,"f")}_emit(e,...t){if(s6(this,em,"f"))return;"end"===e&&(s8(this,em,!0,"f"),s6(this,ef,"f").call(this));let s=s6(this,ex,"f")[e];if(s&&(s6(this,ex,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];s6(this,ey,"f")||s?.length||Promise.reject(e),s6(this,eh,"f").call(this,e),s6(this,ep,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];s6(this,ey,"f")||s?.length||Promise.reject(e),s6(this,eh,"f").call(this,e),s6(this,ep,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function nA(e){return e?.$brand==="auto-parseable-response-format"}function nR(e){return e?.$brand==="auto-parseable-tool"}function nI(e,t){let s=e.choices.map(e=>{var s,r;if("length"===e.finish_reason)throw new ru;if("content_filter"===e.finish_reason)throw new rf;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:nR(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,r=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(r):JSON.parse(r):null):null}}});return{...e,choices:s}}function nP(e){return!!nA(e.response_format)||(e.tools?.some(e=>nR(e)||"function"===e.type&&!0===e.function.strict)??!1)}ec=new WeakMap,ed=new WeakMap,eh=new WeakMap,eu=new WeakMap,ef=new WeakMap,ep=new WeakMap,ex=new WeakMap,em=new WeakMap,eg=new WeakMap,eb=new WeakMap,ey=new WeakMap,el=new WeakSet,ew=function(e){if(s8(this,eg,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new rt),e instanceof rt)return s8(this,eb,!0,"f"),this._emit("abort",e);if(e instanceof s7)return this._emit("error",e);if(e instanceof Error){let t=new s7(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new s7(String(e)))};class n$ extends nS{constructor(){super(...arguments),e_.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),nk(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(nv(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new s7("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),s6(this,e_,"m",ev).call(this)}async finalMessage(){return await this.done(),s6(this,e_,"m",ek).call(this)}async finalFunctionToolCall(){return await this.done(),s6(this,e_,"m",eS).call(this)}async finalFunctionToolCallResult(){return await this.done(),s6(this,e_,"m",eA).call(this)}async totalUsage(){return await this.done(),s6(this,e_,"m",eR).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=s6(this,e_,"m",ek).call(this);t&&this._emit("finalMessage",t);let s=s6(this,e_,"m",ev).call(this);s&&this._emit("finalContent",s);let r=s6(this,e_,"m",eS).call(this);r&&this._emit("finalFunctionToolCall",r);let n=s6(this,e_,"m",eA).call(this);null!=n&&this._emit("finalFunctionToolCallResult",n),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",s6(this,e_,"m",eR).call(this))}async _createChatCompletion(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),s6(this,e_,"m",eI).call(this,t);let n=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(nI(n,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let r="tool",{tool_choice:n="auto",stream:a,...i}=t,o="string"!=typeof n&&n?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(nR(e)){if(!e.$callback)throw new s7("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),d={};for(let e of c)"function"===e.type&&(d[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...i,tool_choice:n,tools:h,messages:[...this.messages]},s),a=t.choices[0]?.message;if(!a)throw new s7("missing message in ChatCompletion response");if(!a.tool_calls?.length)break;for(let e of a.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:n,arguments:a}=e.function,i=d[n];if(i){if(o&&o!==n){let e=`Invalid tool_call: ${JSON.stringify(n)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:r,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(n)}. Available options are: ${Object.keys(d).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:r,tool_call_id:s,content:e});continue}try{t="function"==typeof i.parse?await i.parse(a):a}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:r,tool_call_id:s,content:e});continue}let l=await i.function(t,this),c=s6(this,e_,"m",eP).call(this,l);if(this._addMessage({role:r,tool_call_id:s,content:c}),o)return}}}}e_=new WeakSet,ev=function(){return s6(this,e_,"m",ek).call(this).content??null},ek=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(nv(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new s7("stream ended without producing a ChatCompletionMessage with role=assistant")},eS=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(nv(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},eA=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(nk(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},eR=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},eI=function(e){if(null!=e.n&&e.n>1)throw new s7("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},eP=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class nC extends n${static runTools(e,t,s){let r=new nC,n={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}_addMessage(e,t=!0){super._addMessage(e,t),nv(e)&&e.content&&this._emit("content",e.content)}}let nE={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class nO extends Error{}class nB extends Error{}let nN=(e,t)=>{let s=e.length,r=0,n=e=>{throw new nO(`${e} at position ${r}`)},a=e=>{throw new nB(`${e} at position ${r}`)},i=()=>(h(),r>=s&&n("Unexpected end of input"),'"'===e[r])?o():"{"===e[r]?l():"["===e[r]?c():"null"===e.substring(r,r+4)||nE.NULL&t&&s-r<4&&"null".startsWith(e.substring(r))?(r+=4,null):"true"===e.substring(r,r+4)||nE.BOOL&t&&s-r<4&&"true".startsWith(e.substring(r))?(r+=4,!0):"false"===e.substring(r,r+5)||nE.BOOL&t&&s-r<5&&"false".startsWith(e.substring(r))?(r+=5,!1):"Infinity"===e.substring(r,r+8)||nE.INFINITY&t&&s-r<8&&"Infinity".startsWith(e.substring(r))?(r+=8,1/0):"-Infinity"===e.substring(r,r+9)||nE.MINUS_INFINITY&t&&1<s-r&&s-r<9&&"-Infinity".startsWith(e.substring(r))?(r+=9,-1/0):"NaN"===e.substring(r,r+3)||nE.NAN&t&&s-r<3&&"NaN".startsWith(e.substring(r))?(r+=3,NaN):d(),o=()=>{let i=r,o=!1;for(r++;r<s&&('"'!==e[r]||o&&"\\"===e[r-1]);)o="\\"===e[r]&&!o,r++;if('"'==e.charAt(r))try{return JSON.parse(e.substring(i,++r-Number(o)))}catch(e){a(String(e))}else if(nE.STR&t)try{return JSON.parse(e.substring(i,r-Number(o))+'"')}catch(t){return JSON.parse(e.substring(i,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{r++,h();let a={};try{for(;"}"!==e[r];){if(h(),r>=s&&nE.OBJ&t)return a;let n=o();h(),r++;try{let e=i();Object.defineProperty(a,n,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(nE.OBJ&t)return a;throw e}h(),","===e[r]&&r++}}catch(e){if(nE.OBJ&t)return a;n("Expected '}' at end of object")}return r++,a},c=()=>{r++;let s=[];try{for(;"]"!==e[r];)s.push(i()),h(),","===e[r]&&r++}catch(e){if(nE.ARR&t)return s;n("Expected ']' at end of array")}return r++,s},d=()=>{if(0===r){"-"===e&&nE.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(nE.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}a(String(s))}}let i=r;for("-"===e[r]&&r++;e[r]&&!",]}".includes(e[r]);)r++;r!=s||nE.NUM&t||n("Unterminated number literal");try{return JSON.parse(e.substring(i,r))}catch(s){"-"===e.substring(i,r)&&nE.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(i,e.lastIndexOf("e")))}catch(e){a(String(e))}}},h=()=>{for(;r<s&&" \n\r	".includes(e[r]);)r++};return i()},nj=e=>(function(e,t=nE.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return nN(e.trim(),t)})(e,nE.ALL^nE.NUM);class nM extends n${constructor(e){super(),e$.add(this),eC.set(this,void 0),eE.set(this,void 0),eO.set(this,void 0),s8(this,eC,e,"f"),s8(this,eE,[],"f")}get currentChatCompletionSnapshot(){return s6(this,eO,"f")}static fromReadableStream(e){let t=new nM(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let r=new nM(t);return r._run(()=>r._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createChatCompletion(e,t,s){super._createChatCompletion;let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),s6(this,e$,"m",eB).call(this);let n=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),n))s6(this,e$,"m",ej).call(this,e);if(n.controller.signal?.aborted)throw new rt;return this._addChatCompletion(s6(this,e$,"m",eD).call(this))}async _fromReadableStream(e,t){let s,r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),s6(this,e$,"m",eB).call(this),this._connected();let n=r1.fromReadableStream(e,this.controller);for await(let e of n)s&&s!==e.id&&this._addChatCompletion(s6(this,e$,"m",eD).call(this)),s6(this,e$,"m",ej).call(this,e),s=e.id;if(n.controller.signal?.aborted)throw new rt;return this._addChatCompletion(s6(this,e$,"m",eD).call(this))}[(eC=new WeakMap,eE=new WeakMap,eO=new WeakMap,e$=new WeakSet,eB=function(){this.ended||s8(this,eO,void 0,"f")},eN=function(e){let t=s6(this,eE,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},s6(this,eE,"f")[e.index]=t),t},ej=function(e){if(this.ended)return;let t=s6(this,e$,"m",eW).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let r=s6(this,e$,"m",eN).call(this,e);for(let t of(e.finish_reason&&(s6(this,e$,"m",eT).call(this,e),null!=r.current_tool_call_index&&s6(this,e$,"m",eM).call(this,e,r.current_tool_call_index)),s.delta.tool_calls??[]))r.current_tool_call_index!==t.index&&(s6(this,e$,"m",eT).call(this,e),null!=r.current_tool_call_index&&s6(this,e$,"m",eM).call(this,e,r.current_tool_call_index)),r.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},eM=function(e,t){if(s6(this,e$,"m",eN).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=s6(this,eC,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:nR(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},eT=function(e){let t=s6(this,e$,"m",eN).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=s6(this,e$,"m",eL).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},eD=function(){if(this.ended)throw new s7("stream has ended, this shouldn't happen");let e=s6(this,eO,"f");if(!e)throw new s7("request ended without sending any chunks");return s8(this,eO,void 0,"f"),s8(this,eE,[],"f"),function(e,t){var s;let{id:r,choices:n,created:a,model:i,system_fingerprint:o,...l}=e;return s={...l,id:r,choices:n.map(({message:t,finish_reason:s,index:r,logprobs:n,...a})=>{if(!s)throw new s7(`missing finish_reason for choice ${r}`);let{content:i=null,function_call:o,tool_calls:l,...c}=t,d=t.role;if(!d)throw new s7(`missing role for choice ${r}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new s7(`missing function_call.arguments for choice ${r}`);if(!l)throw new s7(`missing function_call.name for choice ${r}`);return{...a,message:{content:i,function_call:{arguments:e,name:l},role:d,refusal:t.refusal??null},finish_reason:s,index:r,logprobs:n}}return l?{...a,index:r,finish_reason:s,logprobs:n,message:{...c,role:d,content:i,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:n,type:a,id:i,...o}=t,{arguments:l,name:c,...d}=n||{};if(null==i)throw new s7(`missing choices[${r}].tool_calls[${s}].id
${nT(e)}`);if(null==a)throw new s7(`missing choices[${r}].tool_calls[${s}].type
${nT(e)}`);if(null==c)throw new s7(`missing choices[${r}].tool_calls[${s}].function.name
${nT(e)}`);if(null==l)throw new s7(`missing choices[${r}].tool_calls[${s}].function.arguments
${nT(e)}`);return{...o,id:i,type:a,function:{...d,name:c,arguments:l}}})}}:{...a,message:{...c,content:i,role:d,refusal:t.refusal??null},finish_reason:s,index:r,logprobs:n}}),created:a,model:i,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&nP(t)?nI(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,s6(this,eC,"f"))},eL=function(){let e=s6(this,eC,"f")?.response_format;return nA(e)?e:null},eW=function(e){var t,s,r,n;let a=s6(this,eO,"f"),{choices:i,...o}=e;for(let{delta:i,finish_reason:l,index:c,logprobs:d=null,...h}of(a?Object.assign(a,o):a=s8(this,eO,{...o,choices:[]},"f"),e.choices)){let e=a.choices[c];if(e||(e=a.choices[c]={finish_reason:l,index:c,message:{},logprobs:d,...h}),d)if(e.logprobs){let{content:r,refusal:n,...a}=d;Object.assign(e.logprobs,a),r&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...r)),n&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...n))}else e.logprobs=Object.assign({},d);if(l&&(e.finish_reason=l,s6(this,eC,"f")&&nP(s6(this,eC,"f")))){if("length"===l)throw new ru;if("content_filter"===l)throw new rf}if(Object.assign(e,h),!i)continue;let{content:o,refusal:u,function_call:f,role:p,tool_calls:x,...m}=i;if(Object.assign(e.message,m),u&&(e.message.refusal=(e.message.refusal||"")+u),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((r=e.message.function_call).arguments??(r.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&s6(this,e$,"m",eL).call(this)&&(e.message.parsed=nj(e.message.content))),x)for(let{index:t,id:s,type:r,function:a,...i}of(e.message.tool_calls||(e.message.tool_calls=[]),x)){let o=(n=e.message.tool_calls)[t]??(n[t]={});Object.assign(o,i),s&&(o.id=s),r&&(o.type=r),a&&(o.function??(o.function={name:a.name??"",arguments:""})),a?.name&&(o.function.name=a.name),a?.arguments&&(o.function.arguments+=a.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return nR(s)||s?.function.strict||!1}(s6(this,eC,"f"),o)&&(o.function.parsed_arguments=nj(o.function.arguments)))}}return a},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new r1(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function nT(e){return JSON.stringify(e)}class nD extends nM{static fromReadableStream(e){let t=new nD(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let r=new nD(t),n={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}}class nL extends nb{constructor(){super(...arguments),this.messages=new n_(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(nw`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(nw`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new s7(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new s7(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>nI(t,e))}runTools(e,t){return e.stream?nD.runTools(this._client,e,t):nC.runTools(this._client,e,t)}stream(e,t){return nM.createChatCompletion(this._client,e,t)}}nL.Messages=n_;class nW extends nb{constructor(){super(...arguments),this.completions=new nL(this._client)}}nW.Completions=nL;let nU=Symbol("brand.privateNullableHeaders"),nH=Array.isArray,nq=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(nU in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():nH(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=nH(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===a?(t.delete(n),s.add(r)):(t.append(n,a),s.delete(r))}}return{[nU]:!0,values:t,nulls:s}};class nF extends nb{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:nq([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class nz extends nb{create(e,t){return this._client.post("/audio/transcriptions",ni({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class nK extends nb{create(e,t){return this._client.post("/audio/translations",ni({body:e,...t,__metadata:{model:e.model}},this._client))}}class nX extends nb{constructor(){super(...arguments),this.transcriptions=new nz(this._client),this.translations=new nK(this._client),this.speech=new nF(this._client)}}nX.Transcriptions=nz,nX.Translations=nK,nX.Speech=nF;class nJ extends nb{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",nt,{query:e,...t})}cancel(e,t){return this._client.post(nw`/batches/${e}/cancel`,t)}}class nV extends nb{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(nw`/assistants/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(nw`/assistants/${e}`,{body:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",nt,{query:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(nw`/assistants/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class nG extends nb{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class nQ extends nb{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class nY extends nb{constructor(){super(...arguments),this.sessions=new nG(this._client),this.transcriptionSessions=new nQ(this._client)}}nY.Sessions=nG,nY.TranscriptionSessions=nQ;class nZ extends nb{create(e,t,s){return this._client.post(nw`/threads/${e}/messages`,{body:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:r}=t;return this._client.get(nw`/threads/${r}/messages/${e}`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:r,...n}=t;return this._client.post(nw`/threads/${r}/messages/${e}`,{body:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(nw`/threads/${e}/messages`,nt,{query:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:r}=t;return this._client.delete(nw`/threads/${r}/messages/${e}`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class n0 extends nb{retrieve(e,t,s){let{thread_id:r,run_id:n,...a}=t;return this._client.get(nw`/threads/${r}/runs/${n}/steps/${e}`,{query:a,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:r,...n}=t;return this._client.getAPIList(nw`/threads/${r}/runs/${e}/steps`,nt,{query:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let n1=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,r=new Uint8Array(s);for(let e=0;e<s;e++)r[e]=t.charCodeAt(e);return Array.from(new Float32Array(r.buffer))}},n2=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class n4 extends nS{constructor(){super(...arguments),eU.add(this),eq.set(this,[]),eF.set(this,{}),ez.set(this,{}),eK.set(this,void 0),eX.set(this,void 0),eJ.set(this,void 0),eV.set(this,void 0),eG.set(this,void 0),eQ.set(this,void 0),eY.set(this,void 0),eZ.set(this,void 0),e0.set(this,void 0)}[(eq=new WeakMap,eF=new WeakMap,ez=new WeakMap,eK=new WeakMap,eX=new WeakMap,eJ=new WeakMap,eV=new WeakMap,eG=new WeakMap,eQ=new WeakMap,eY=new WeakMap,eZ=new WeakMap,e0=new WeakMap,eU=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new eH;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let r=r1.fromReadableStream(e,this.controller);for await(let e of r)s6(this,eU,"m",e1).call(this,e);if(r.controller.signal?.aborted)throw new rt;return this._addRun(s6(this,eU,"m",e2).call(this))}toReadableStream(){return new r1(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,r){let n=new eH;return n._run(()=>n._runToolAssistantStream(e,t,s,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createToolAssistantStream(e,t,s,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a={...s,stream:!0},i=await e.submitToolOutputs(t,a,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))s6(this,eU,"m",e1).call(this,e);if(i.controller.signal?.aborted)throw new rt;return this._addRun(s6(this,eU,"m",e2).call(this))}static createThreadAssistantStream(e,t,s){let r=new eH;return r._run(()=>r._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}static createAssistantStream(e,t,s,r){let n=new eH;return n._run(()=>n._runAssistantStream(e,t,s,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}currentEvent(){return s6(this,eY,"f")}currentRun(){return s6(this,eZ,"f")}currentMessageSnapshot(){return s6(this,eK,"f")}currentRunStepSnapshot(){return s6(this,e0,"f")}async finalRunSteps(){return await this.done(),Object.values(s6(this,eF,"f"))}async finalMessages(){return await this.done(),Object.values(s6(this,ez,"f"))}async finalRun(){if(await this.done(),!s6(this,eX,"f"))throw Error("Final run was not received.");return s6(this,eX,"f")}async _createThreadAssistantStream(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let n={...t,stream:!0},a=await e.createAndRun(n,{...s,signal:this.controller.signal});for await(let e of(this._connected(),a))s6(this,eU,"m",e1).call(this,e);if(a.controller.signal?.aborted)throw new rt;return this._addRun(s6(this,eU,"m",e2).call(this))}async _createAssistantStream(e,t,s,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a={...s,stream:!0},i=await e.create(t,a,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))s6(this,eU,"m",e1).call(this,e);if(i.controller.signal?.aborted)throw new rt;return this._addRun(s6(this,eU,"m",e2).call(this))}static accumulateDelta(e,t){for(let[s,r]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=r;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=r;continue}if("string"==typeof t&&"string"==typeof r)t+=r;else if("number"==typeof t&&"number"==typeof r)t+=r;else if(rm(t)&&rm(r))t=this.accumulateDelta(t,r);else if(Array.isArray(t)&&Array.isArray(r)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...r);continue}for(let e of r){if(!rm(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let r=t[s];null==r?t.push(e):t[s]=this.accumulateDelta(r,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${r}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,r){return await this._createAssistantStream(t,e,s,r)}async _runToolAssistantStream(e,t,s,r){return await this._createToolAssistantStream(t,e,s,r)}}eH=n4,e1=function(e){if(!this.ended)switch(s8(this,eY,e,"f"),s6(this,eU,"m",e6).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":s6(this,eU,"m",e7).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":s6(this,eU,"m",e8).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":s6(this,eU,"m",e4).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},e2=function(){if(this.ended)throw new s7("stream has ended, this shouldn't happen");if(!s6(this,eX,"f"))throw Error("Final run has not been received");return s6(this,eX,"f")},e4=function(e){let[t,s]=s6(this,eU,"m",e3).call(this,e,s6(this,eK,"f"));for(let e of(s8(this,eK,t,"f"),s6(this,ez,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,r=t.content[s.index];if(r&&"text"==r.type)this._emit("textDelta",e,r.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=s6(this,eJ,"f")){if(s6(this,eV,"f"))switch(s6(this,eV,"f").type){case"text":this._emit("textDone",s6(this,eV,"f").text,s6(this,eK,"f"));break;case"image_file":this._emit("imageFileDone",s6(this,eV,"f").image_file,s6(this,eK,"f"))}s8(this,eJ,s.index,"f")}s8(this,eV,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==s6(this,eJ,"f")){let t=e.data.content[s6(this,eJ,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,s6(this,eK,"f"));break;case"text":this._emit("textDone",t.text,s6(this,eK,"f"))}}s6(this,eK,"f")&&this._emit("messageDone",e.data),s8(this,eK,void 0,"f")}},e8=function(e){let t=s6(this,eU,"m",e5).call(this,e);switch(s8(this,e0,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==s6(this,eG,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(s6(this,eQ,"f")&&this._emit("toolCallDone",s6(this,eQ,"f")),s8(this,eG,e.index,"f"),s8(this,eQ,t.step_details.tool_calls[e.index],"f"),s6(this,eQ,"f")&&this._emit("toolCallCreated",s6(this,eQ,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":s8(this,e0,void 0,"f"),"tool_calls"==e.data.step_details.type&&s6(this,eQ,"f")&&(this._emit("toolCallDone",s6(this,eQ,"f")),s8(this,eQ,void 0,"f")),this._emit("runStepDone",e.data,t)}},e6=function(e){s6(this,eq,"f").push(e),this._emit("event",e)},e5=function(e){switch(e.event){case"thread.run.step.created":return s6(this,eF,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=s6(this,eF,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let r=eH.accumulateDelta(t,s.delta);s6(this,eF,"f")[e.data.id]=r}return s6(this,eF,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":s6(this,eF,"f")[e.data.id]=e.data}if(s6(this,eF,"f")[e.data.id])return s6(this,eF,"f")[e.data.id];throw Error("No snapshot available")},e3=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let r=e.data;if(r.delta.content)for(let e of r.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=s6(this,eU,"m",e9).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},e9=function(e,t){return eH.accumulateDelta(t,e)},e7=function(e){switch(s8(this,eZ,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":s8(this,eX,e.data,"f"),s6(this,eQ,"f")&&(this._emit("toolCallDone",s6(this,eQ,"f")),s8(this,eQ,void 0,"f"))}};class n8 extends nb{constructor(){super(...arguments),this.steps=new n0(this._client)}create(e,t,s){let{include:r,...n}=t;return this._client.post(nw`/threads/${e}/runs`,{query:{include:r},body:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:r}=t;return this._client.get(nw`/threads/${r}/runs/${e}`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:r,...n}=t;return this._client.post(nw`/threads/${r}/runs/${e}`,{body:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(nw`/threads/${e}/runs`,nt,{query:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:r}=t;return this._client.post(nw`/threads/${r}/runs/${e}/cancel`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t,s);return await this.poll(r.id,{thread_id:e},s)}createAndStream(e,t,s){return n4.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let r=nq([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:a}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...r}}).withResponse();switch(n.status){case"queued":case"in_progress":case"cancelling":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await ry(i);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return n}}}stream(e,t,s){return n4.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:r,...n}=t;return this._client.post(nw`/threads/${r}/runs/${e}/submit_tool_outputs`,{body:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let r=await this.submitToolOutputs(e,t,s);return await this.poll(r.id,t,s)}submitToolOutputsStream(e,t,s){return n4.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}n8.Steps=n0;class n6 extends nb{constructor(){super(...arguments),this.runs=new n8(this._client),this.messages=new nZ(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(nw`/threads/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(nw`/threads/${e}`,{body:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(nw`/threads/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return n4.createThreadAssistantStream(e,this._client.beta.threads,t)}}n6.Runs=n8,n6.Messages=nZ;class n5 extends nb{constructor(){super(...arguments),this.realtime=new nY(this._client),this.assistants=new nV(this._client),this.threads=new n6(this._client)}}n5.Realtime=nY,n5.Assistants=nV,n5.Threads=n6;class n3 extends nb{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class n9 extends nb{retrieve(e,t,s){let{container_id:r}=t;return this._client.get(nw`/containers/${r}/files/${e}/content`,{...s,headers:nq([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class n7 extends nb{constructor(){super(...arguments),this.content=new n9(this._client)}create(e,t,s){return this._client.post(nw`/containers/${e}/files`,ni({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:r}=t;return this._client.get(nw`/containers/${r}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(nw`/containers/${e}/files`,nt,{query:t,...s})}delete(e,t,s){let{container_id:r}=t;return this._client.delete(nw`/containers/${r}/files/${e}`,{...s,headers:nq([{Accept:"*/*"},s?.headers])})}}n7.Content=n9;class ae extends nb{constructor(){super(...arguments),this.files=new n7(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/containers/${e}`,{...t,headers:nq([{Accept:"*/*"},t?.headers])})}}ae.Files=n7;class at extends nb{create(e,t){let s=!!e.encoding_format,r=s?e.encoding_format:"base64";s&&rR(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let n=this._client.post("/embeddings",{body:{...e,encoding_format:r},...t});return s?n:(rR(this._client).debug("embeddings/decoding base64 embeddings from base64"),n._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=n1(t)}),e)))}}class as extends nb{retrieve(e,t,s){let{eval_id:r,run_id:n}=t;return this._client.get(nw`/evals/${r}/runs/${n}/output_items/${e}`,s)}list(e,t,s){let{eval_id:r,...n}=t;return this._client.getAPIList(nw`/evals/${r}/runs/${e}/output_items`,nt,{query:n,...s})}}class ar extends nb{constructor(){super(...arguments),this.outputItems=new as(this._client)}create(e,t,s){return this._client.post(nw`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:r}=t;return this._client.get(nw`/evals/${r}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(nw`/evals/${e}/runs`,nt,{query:t,...s})}delete(e,t,s){let{eval_id:r}=t;return this._client.delete(nw`/evals/${r}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:r}=t;return this._client.post(nw`/evals/${r}/runs/${e}`,s)}}ar.OutputItems=as;class an extends nb{constructor(){super(...arguments),this.runs=new ar(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/evals/${e}`,t)}update(e,t,s){return this._client.post(nw`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/evals/${e}`,t)}}an.Runs=ar;class aa extends nb{create(e,t){return this._client.post("/files",ni({body:e,...t},this._client))}retrieve(e,t){return this._client.get(nw`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/files/${e}`,t)}content(e,t){return this._client.get(nw`/files/${e}/content`,{...t,headers:nq([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let r=new Set(["processed","error","deleted"]),n=Date.now(),a=await this.retrieve(e);for(;!a.status||!r.has(a.status);)if(await ry(t),a=await this.retrieve(e),Date.now()-n>s)throw new rr({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return a}}class ai extends nb{}class ao extends nb{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class al extends nb{constructor(){super(...arguments),this.graders=new ao(this._client)}}al.Graders=ao;class ac extends nb{create(e,t,s){return this._client.getAPIList(nw`/fine_tuning/checkpoints/${e}/permissions`,ne,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(nw`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:r}=t;return this._client.delete(nw`/fine_tuning/checkpoints/${r}/permissions/${e}`,s)}}class ad extends nb{constructor(){super(...arguments),this.permissions=new ac(this._client)}}ad.Permissions=ac;class ah extends nb{list(e,t={},s){return this._client.getAPIList(nw`/fine_tuning/jobs/${e}/checkpoints`,nt,{query:t,...s})}}class au extends nb{constructor(){super(...arguments),this.checkpoints=new ah(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",nt,{query:e,...t})}cancel(e,t){return this._client.post(nw`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(nw`/fine_tuning/jobs/${e}/events`,nt,{query:t,...s})}pause(e,t){return this._client.post(nw`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(nw`/fine_tuning/jobs/${e}/resume`,t)}}au.Checkpoints=ah;class af extends nb{constructor(){super(...arguments),this.methods=new ai(this._client),this.jobs=new au(this._client),this.checkpoints=new ad(this._client),this.alpha=new al(this._client)}}af.Methods=ai,af.Jobs=au,af.Checkpoints=ad,af.Alpha=al;class ap extends nb{}class ax extends nb{constructor(){super(...arguments),this.graderModels=new ap(this._client)}}ax.GraderModels=ap;class am extends nb{createVariation(e,t){return this._client.post("/images/variations",ni({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",ni({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class ag extends nb{retrieve(e,t){return this._client.get(nw`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",ne,e)}delete(e,t){return this._client.delete(nw`/models/${e}`,t)}}class ab extends nb{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function ay(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,r;return"output_text"===e.type?{...e,parsed:(s=t,r=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(r):JSON.parse(r))}:e});return{...e,content:s}}return e}),r=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||aw(r),Object.defineProperty(r,"output_parsed",{enumerable:!0,get(){for(let e of r.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),r}function aw(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class a_ extends nS{constructor(e){super(),te.add(this),tt.set(this,void 0),ts.set(this,void 0),tr.set(this,void 0),s8(this,tt,e,"f")}static createResponse(e,t,s){let r=new a_(t);return r._run(()=>r._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createOrRetrieveResponse(e,t,s){let r,n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),s6(this,te,"m",tn).call(this);let a=null;for await(let n of("response_id"in t?(r=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),a=t.starting_after??null):r=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),r))s6(this,te,"m",ta).call(this,n,a);if(r.controller.signal?.aborted)throw new rt;return s6(this,te,"m",ti).call(this)}[(tt=new WeakMap,ts=new WeakMap,tr=new WeakMap,te=new WeakSet,tn=function(){this.ended||s8(this,ts,void 0,"f")},ta=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},r=s6(this,te,"m",to).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=r.output[e.output_index];if(!t)throw new s7(`missing output at index ${e.output_index}`);if("message"===t.type){let r=t.content[e.content_index];if(!r)throw new s7(`missing content at index ${e.content_index}`);if("output_text"!==r.type)throw new s7(`expected content to be 'output_text', got ${r.type}`);s("response.output_text.delta",{...e,snapshot:r.text})}break}case"response.function_call_arguments.delta":{let t=r.output[e.output_index];if(!t)throw new s7(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},ti=function(){if(this.ended)throw new s7("stream has ended, this shouldn't happen");let e=s6(this,ts,"f");if(!e)throw new s7("request ended without sending any events");s8(this,ts,void 0,"f");let t=function(e,t){var s;return t&&(s=t,nA(s.text?.format))?ay(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,s6(this,tt,"f"));return s8(this,tr,t,"f"),t},to=function(e){let t=s6(this,ts,"f");if(!t){if("response.created"!==e.type)throw new s7(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return s8(this,ts,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new s7(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new s7(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new s7(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new s7(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new s7(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":s8(this,ts,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=s6(this,tr,"f");if(!e)throw new s7("stream ended without producing a ChatCompletion");return e}}class av extends nb{list(e,t={},s){return this._client.getAPIList(nw`/responses/${e}/input_items`,nt,{query:t,...s})}}class ak extends nb{constructor(){super(...arguments),this.inputItems=new av(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&aw(e),e))}retrieve(e,t={},s){return this._client.get(nw`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})}delete(e,t){return this._client.delete(nw`/responses/${e}`,{...t,headers:nq([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>ay(t,e))}stream(e,t){return a_.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(nw`/responses/${e}/cancel`,{...t,headers:nq([{Accept:"*/*"},t?.headers])})}}ak.InputItems=av;class aS extends nb{create(e,t,s){return this._client.post(nw`/uploads/${e}/parts`,ni({body:t,...s},this._client))}}class aA extends nb{constructor(){super(...arguments),this.parts=new aS(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(nw`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(nw`/uploads/${e}/complete`,{body:t,...s})}}aA.Parts=aS;let aR=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let r=[];for(let e of t)"fulfilled"===e.status&&r.push(e.value);return r};class aI extends nb{create(e,t,s){return this._client.post(nw`/vector_stores/${e}/file_batches`,{body:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:r}=t;return this._client.get(nw`/vector_stores/${r}/file_batches/${e}`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:r}=t;return this._client.post(nw`/vector_stores/${r}/file_batches/${e}/cancel`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t);return await this.poll(e,r.id,s)}listFiles(e,t,s){let{vector_store_id:r,...n}=t;return this._client.getAPIList(nw`/vector_stores/${r}/file_batches/${e}/files`,nt,{query:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let r=nq([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:a}=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse();switch(n.status){case"in_progress":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await ry(i);break;case"failed":case"cancelled":case"completed":return n}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},r){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let n=Math.min(r?.maxConcurrency??5,t.length),a=this._client,i=t.values(),o=[...s];async function l(e){for(let t of e){let e=await a.files.create({file:t,purpose:"assistants"},r);o.push(e.id)}}let c=Array(n).fill(i).map(l);return await aR(c),await this.createAndPoll(e,{file_ids:o})}}class aP extends nb{create(e,t,s){return this._client.post(nw`/vector_stores/${e}/files`,{body:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:r}=t;return this._client.get(nw`/vector_stores/${r}/files/${e}`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:r,...n}=t;return this._client.post(nw`/vector_stores/${r}/files/${e}`,{body:n,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(nw`/vector_stores/${e}/files`,nt,{query:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:r}=t;return this._client.delete(nw`/vector_stores/${r}/files/${e}`,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t,s);return await this.poll(e,r.id,s)}async poll(e,t,s){let r=nq([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let n=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse(),a=n.data;switch(a.status){case"in_progress":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=n.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await ry(i);break;case"failed":case"completed":return a}}}async upload(e,t,s){let r=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:r.id},s)}async uploadAndPoll(e,t,s){let r=await this.upload(e,t,s);return await this.poll(e,r.id,s)}content(e,t,s){let{vector_store_id:r}=t;return this._client.getAPIList(nw`/vector_stores/${r}/files/${e}/content`,ne,{...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class a$ extends nb{constructor(){super(...arguments),this.files=new aP(this._client),this.fileBatches=new aI(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(nw`/vector_stores/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(nw`/vector_stores/${e}`,{body:t,...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",nt,{query:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(nw`/vector_stores/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(nw`/vector_stores/${e}/search`,ne,{body:t,method:"post",...s,headers:nq([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}a$.Files=aP,a$.FileBatches=aI;class aC{constructor({baseURL:e=n2("OPENAI_BASE_URL"),apiKey:t=n2("OPENAI_API_KEY"),organization:s=n2("OPENAI_ORG_ID")??null,project:r=n2("OPENAI_PROJECT_ID")??null,...n}={}){if(tc.set(this,void 0),this.completions=new n3(this),this.chat=new nW(this),this.embeddings=new at(this),this.files=new aa(this),this.images=new am(this),this.audio=new nX(this),this.moderations=new ab(this),this.models=new ag(this),this.fineTuning=new af(this),this.graders=new ax(this),this.vectorStores=new a$(this),this.beta=new n5(this),this.batches=new nJ(this),this.uploads=new aA(this),this.responses=new ak(this),this.evals=new an(this),this.containers=new ae(this),void 0===t)throw new s7("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let a={apiKey:t,organization:s,project:r,...n,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&r$())throw new s7("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=a.baseURL,this.timeout=a.timeout??tl.DEFAULT_TIMEOUT,this.logger=a.logger??console;let i="warn";this.logLevel=i,this.logLevel=r_(a.logLevel,"ClientOptions.logLevel",this)??r_(n2("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??i,this.fetchOptions=a.fetchOptions,this.maxRetries=a.maxRetries??2,this.fetch=a.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),s8(this,tc,rD,"f"),this._options=a,this.apiKey=t,this.organization=s,this.project=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return nq([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,r,n=e,a=function(e=rG){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||rG.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=rL;if(void 0!==e.format){if(!rF.call(rW,e.format))throw TypeError("Unknown format option provided.");r=e.format}let n=rW[r],a=rG.filter;if(("function"==typeof e.filter||rK(e.filter))&&(a=e.filter),t=e.arrayFormat&&e.arrayFormat in rz?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":rG.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let i=void 0===e.allowDots?!0==!!e.encodeDotInKeys||rG.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:rG.addQueryPrefix,allowDots:i,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:rG.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:rG.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?rG.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:rG.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:rG.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:rG.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:rG.encodeValuesOnly,filter:a,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:rG.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:rG.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:rG.strictNullHandling}}(t);"function"==typeof a.filter?n=(0,a.filter)("",n):rK(a.filter)&&(s=a.filter);let i=[];if("object"!=typeof n||null===n)return"";let o=rz[a.arrayFormat],l="comma"===o&&a.commaRoundTrip;s||(s=Object.keys(n)),a.sort&&s.sort(a.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];a.skipNulls&&null===n[t]||rJ(i,function e(t,s,r,n,a,i,o,l,c,d,h,u,f,p,x,m,g,b){var y,w;let _,v=t,k=b,S=0,A=!1;for(;void 0!==(k=k.get(rQ))&&!A;){let e=k.get(t);if(S+=1,void 0!==e)if(e===S)throw RangeError("Cyclic object value");else A=!0;void 0===k.get(rQ)&&(S=0)}if("function"==typeof d?v=d(s,v):v instanceof Date?v=f?.(v):"comma"===r&&rK(v)&&(v=rq(v,function(e){return e instanceof Date?f?.(e):e})),null===v){if(i)return c&&!m?c(s,rG.encoder,g,"key",p):s;v=""}if("string"==typeof(y=v)||"number"==typeof y||"boolean"==typeof y||"symbol"==typeof y||"bigint"==typeof y||(w=v)&&"object"==typeof w&&w.constructor&&w.constructor.isBuffer&&w.constructor.isBuffer(w)){if(c){let e=m?s:c(s,rG.encoder,g,"key",p);return[x?.(e)+"="+x?.(c(v,rG.encoder,g,"value",p))]}return[x?.(s)+"="+x?.(String(v))]}let R=[];if(void 0===v)return R;if("comma"===r&&rK(v))m&&c&&(v=rq(v,c)),_=[{value:v.length>0?v.join(",")||null:void 0}];else if(rK(d))_=d;else{let e=Object.keys(v);_=h?e.sort(h):e}let I=l?String(s).replace(/\./g,"%2E"):String(s),P=n&&rK(v)&&1===v.length?I+"[]":I;if(a&&rK(v)&&0===v.length)return P+"[]";for(let s=0;s<_.length;++s){let y=_[s],w="object"==typeof y&&void 0!==y.value?y.value:v[y];if(o&&null===w)continue;let k=u&&l?y.replace(/\./g,"%2E"):y,A=rK(v)?"function"==typeof r?r(P,k):P:P+(u?"."+k:"["+k+"]");b.set(t,S);let I=new WeakMap;I.set(rQ,b),rJ(R,e(w,A,r,n,a,i,o,l,"comma"===r&&m&&rK(v)?null:c,d,h,u,f,p,x,m,g,I))}return R}(n[t],t,o,l,a.allowEmptyArrays,a.strictNullHandling,a.skipNulls,a.encodeDotInKeys,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,c))}let d=i.join(a.delimiter),h=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),d.length>0?h+d:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${rP}`}defaultIdempotencyKey(){return`stainless-node-retry-${s5()}`}makeStatusError(e,t,s,r){return re.generate(e,t,s,r)}buildURL(e,t){let s=new URL(rx(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new r3(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:a,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,d=Date.now();if(rR(this).debug(`[${l}] sending request`,rI({retryOfRequestLogID:s,method:r.method,url:i,options:r,headers:a.headers})),r.signal?.aborted)throw new rt;let h=new AbortController,u=await this.fetchWithTimeout(i,a,o,h).catch(s9),f=Date.now();if(u instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new rt;let n=s3(u)||/timed? ?out/i.test(String(u)+("cause"in u?String(u.cause):""));if(t)return rR(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),rR(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,rI({retryOfRequestLogID:s,url:i,durationMs:f-d,message:u.message})),this.retryRequest(r,t,s??l);if(rR(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),rR(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,rI({retryOfRequestLogID:s,url:i,durationMs:f-d,message:u.message})),n)throw new rr;throw new rs({cause:u})}let p=[...u.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),x=`[${l}${c}${p}] ${a.method} ${i} ${u.ok?"succeeded":"failed"} with status ${u.status} in ${f-d}ms`;if(!u.ok){let e=this.shouldRetry(u);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await rT(u.body),rR(this).info(`${x} - ${e}`),rR(this).debug(`[${l}] response error (${e})`,rI({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),this.retryRequest(r,t,s??l,u.headers)}let n=e?"error; no more retries left":"error; not retryable";rR(this).info(`${x} - ${n}`);let a=await u.text().catch(e=>s9(e).message),i=rb(a),o=i?void 0:a;throw rR(this).debug(`[${l}] response error (${n})`,rI({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,message:o,durationMs:Date.now()-d})),this.makeStatusError(u.status,i,o,u.headers)}return rR(this).info(x),rR(this).debug(`[${l}] response start`,rI({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),{response:u,options:r,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:d}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new r7(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,r){let n,a=r?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=r?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await ry(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:a}=s,i=this.buildURL(n,a);"timeout"in s&&rg("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:i,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=nq([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...rB(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=nq([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:rj(e)}:s6(this,tc,"f").call(this,{body:e,headers:s})}}tl=aC,tc=new WeakMap,aC.OpenAI=tl,aC.DEFAULT_TIMEOUT=6e5,aC.OpenAIError=s7,aC.APIError=re,aC.APIConnectionError=rs,aC.APIConnectionTimeoutError=rr,aC.APIUserAbortError=rt,aC.NotFoundError=ro,aC.ConflictError=rl,aC.RateLimitError=rd,aC.BadRequestError=rn,aC.AuthenticationError=ra,aC.InternalServerError=rh,aC.PermissionDeniedError=ri,aC.UnprocessableEntityError=rc,aC.toFile=nm,aC.Completions=n3,aC.Chat=nW,aC.Embeddings=at,aC.Files=aa,aC.Images=am,aC.Audio=nX,aC.Moderations=ab,aC.Models=ag,aC.FineTuning=af,aC.Graders=ax,aC.VectorStores=a$,aC.Beta=n5,aC.Batches=nJ,aC.Uploads=aA,aC.Responses=ak,aC.Evals=an,aC.Containers=ae;class aE{static{this.claudeClient=null}static{this.openaiClient=null}static initializeClaude(e){this.claudeClient=new s1({apiKey:e})}static initializeOpenAI(e){this.openaiClient=new aC({apiKey:e})}static async testClaudeConnection(e){try{let t=new s1({apiKey:e}),s=await t.messages.create({model:"claude-3-haiku-20240307",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]});return"text"===s.content[0].type&&s.content[0].text.includes("OK")}catch(e){return console.error("Claude connection test failed:",e),!1}}static async testOpenAIConnection(e){try{let t=new aC({apiKey:e}),s=await t.chat.completions.create({model:"gpt-3.5-turbo",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]});return s.choices[0]?.message?.content?.includes("OK")||!1}catch(e){return console.error("OpenAI connection test failed:",e),!1}}static async generateWithClaude(e,t,s,r){if(!this.claudeClient)throw Error("Claude client not initialized");try{let n=this.buildFullPrompt(e,t,s,r),a=await this.claudeClient.messages.create({model:"claude-3-sonnet-20240229",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:n}]});if("text"!==a.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(a.content[0].text)}catch(e){throw console.error("Claude generation failed:",e),Error(`Claude API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static async generateWithOpenAI(e,t,s,r){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{let n=this.buildFullPrompt(e,t,s,r),a=await this.openaiClient.chat.completions.create({model:"gpt-4",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts."},{role:"user",content:n}]}),i=a.choices[0]?.message?.content;if(!i)throw Error("No content received from OpenAI");return this.parseAIResponse(i)}catch(e){throw console.error("OpenAI generation failed:",e),Error(`OpenAI API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static buildFullPrompt(e,t,s,r){return`${e}

DOCUMENT CONTENTS:

Enterprise Need Document:
${t.enterpriseNeed}

Solution Description Document:
${t.solution}

Risk of No Investment Document:
${t.risk}

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).`}static parseAIResponse(e){let t=e.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i),s="",r="",n="";for(let e of t){let t=e.trim();t&&(t.toLowerCase().includes("enterprise need")?s=t:t.toLowerCase().includes("proposed solution")?r=t:t.toLowerCase().includes("risk of no investment")&&(n=t))}if(!s||!r||!n){let t=e.split(/\n\s*\n/);t.length>=3?(s=t[0]||"Enterprise Need artifact could not be parsed.",r=t[1]||"Solution artifact could not be parsed.",n=t[2]||"Risk artifact could not be parsed."):(s=`# Enterprise Need Artifact

${e}`,r=`# Proposed Solution Artifact

${e}`,n=`# Risk of No Investment Artifact

${e}`)}return{enterpriseNeedArtifact:s.trim(),solutionArtifact:r.trim(),riskArtifact:n.trim()}}static async generateWithClaudeFiles(e,t,s,r){if(!this.claudeClient)throw Error("Claude client not initialized");try{let s=[];s.push({type:"document",source:{type:"base64",media_type:this.getMimeType(t.enterpriseNeedFile.fileType),data:t.enterpriseNeedFile.base64Data}}),s.push({type:"document",source:{type:"base64",media_type:this.getMimeType(t.solutionFile.fileType),data:t.solutionFile.base64Data}}),s.push({type:"document",source:{type:"base64",media_type:this.getMimeType(t.riskFile.fileType),data:t.riskFile.base64Data}}),s.push({type:"text",text:`${e}

Please analyze the three attached documents and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

The first document contains the Enterprise Need information.
The second document contains the Solution Description information.
The third document contains the Risk of No Investment information.

Use only the content from these attached documents and do not add any other information other than the items requested in this prompt.`});let r=await this.claudeClient.messages.create({model:"claude-3-5-sonnet-20241022",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:s}]});if("text"!==r.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(r.content[0].text)}catch(e){throw console.error("Claude file generation failed:",e),Error(`Claude API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static async generateWithOpenAIFiles(e,t,s,r){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{let s=`${e}

I have three documents that I need you to analyze:

1. Enterprise Need Document (${t.enterpriseNeedFile.fileName})
2. Solution Description Document (${t.solutionFile.fileName})
3. Risk of No Investment Document (${t.riskFile.fileName})

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Note: The documents are provided as attachments to this request. Please analyze their content to create the artifacts.`,r=await this.openaiClient.chat.completions.create({model:"gpt-4",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts. You have access to document attachments that contain the source material for your analysis."},{role:"user",content:s}]}),n=r.choices[0]?.message?.content;if(!n)throw Error("No content received from OpenAI");return this.parseAIResponse(n)}catch(e){throw console.error("OpenAI file generation failed:",e),Error(`OpenAI API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static async generateArtifactsWithFilesRetry(e,t,s,r,n,a=3){let i=null;for(let o=1;o<=a;o++)try{if("claude"===e)return await this.generateWithClaudeFiles(t,s,r,n);return await this.generateWithOpenAIFiles(t,s,r,n)}catch(e){i=e instanceof Error?e:Error("Unknown error"),console.error(`AI file generation attempt ${o} failed:`,i),o<a&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o)))}throw Error(`AI file generation failed after ${a} attempts: ${i?.message}`)}static getMimeType(e){return({pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",doc:"application/msword",txt:"text/plain",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg"})[e.toLowerCase()]||"application/octet-stream"}static async generateArtifactsWithRetry(e,t,s,r,n,a=3){let i=null;for(let o=1;o<=a;o++)try{if("claude"===e)return await this.generateWithClaude(t,s,r,n);return await this.generateWithOpenAI(t,s,r,n)}catch(e){i=e instanceof Error?e:Error("Unknown error"),console.error(`AI generation attempt ${o} failed:`,i),o<a&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o)))}throw Error(`AI generation failed after ${a} attempts: ${i?.message}`)}}var aO=s(4765),aB=s.n(aO);let aN="main",aj="raviteja";class aM{static encryptApiKey(e){return aB().AES.encrypt(e,aj).toString()}static decryptApiKey(e){return aB().AES.decrypt(e,aj).toString(aB().enc.Utf8)}static async getAPIConfiguration(){try{let e=await (0,tx.x7)((0,tx.H9)(tm.db,"apiConfig",aN));if(!e.exists())return null;let t=e.data();return{id:e.id,claudeApiKey:t.claudeApiKey?this.decryptApiKey(t.claudeApiKey):void 0,openaiApiKey:t.openaiApiKey?this.decryptApiKey(t.openaiApiKey):void 0,createdAt:t.createdAt?.toDate()||new Date,updatedAt:t.updatedAt?.toDate()||new Date,claudeStatus:t.claudeStatus||"untested",openaiStatus:t.openaiStatus||"untested",lastTestedAt:t.lastTestedAt?.toDate()}}catch(e){throw console.error("Error fetching API configuration:",e),Error("Failed to fetch API configuration")}}static async saveAPIConfiguration(e){try{let t=(0,tx.H9)(tm.db,"apiConfig",aN),s=await (0,tx.x7)(t),r={updatedAt:tx.Dc.now()};e.claudeApiKey&&(r.claudeApiKey=this.encryptApiKey(e.claudeApiKey),r.claudeStatus="untested"),e.openaiApiKey&&(r.openaiApiKey=this.encryptApiKey(e.openaiApiKey),r.openaiStatus="untested"),s.exists()?await (0,tx.mZ)(t,r):await (0,tx.BN)(t,{...r,createdAt:tx.Dc.now()})}catch(e){throw console.error("Error saving API configuration:",e),Error("Failed to save API configuration")}}static async testAPIConnections(){try{let{getFunctions:e,httpsCallable:t}=await s.e(890).then(s.bind(s,24890)),r=e(),n=t(r,"testAPIConnectionsFunction");return(await n()).data}catch(e){return console.error("Error testing API connections:",e),{claudeStatus:"error",openaiStatus:"error",claudeError:e instanceof Error?e.message:"Failed to test connections",openaiError:e instanceof Error?e.message:"Failed to test connections"}}}static async getDecryptedAPIKeys(){try{let e=await this.getAPIConfiguration();return{claudeApiKey:e?.claudeApiKey,openaiApiKey:e?.openaiApiKey}}catch(e){throw console.error("Error getting decrypted API keys:",e),Error("Failed to get API keys")}}static async areAPIKeysConfigured(){try{let e=await this.getAPIConfiguration(),t=!!e?.claudeApiKey,s=!!e?.openaiApiKey;return{claudeConfigured:t,openaiConfigured:s,anyConfigured:t||s}}catch(e){return console.error("Error checking API key configuration:",e),{claudeConfigured:!1,openaiConfigured:!1,anyConfigured:!1}}}static async initializeAIServices(){try{let e=await this.getDecryptedAPIKeys();e.claudeApiKey&&aE.initializeClaude(e.claudeApiKey),e.openaiApiKey&&aE.initializeOpenAI(e.openaiApiKey)}catch(e){throw console.error("Error initializing AI services:",e),Error("Failed to initialize AI services")}}static validateAPIKey(e,t){if(!t||0===t.trim().length)return{isValid:!1,error:"API key cannot be empty"};if("claude"===e){if(!t.startsWith("sk-ant-"))return{isValid:!1,error:'Claude API key should start with "sk-ant-"'}}else if("openai"===e&&!t.startsWith("sk-"))return{isValid:!1,error:'OpenAI API key should start with "sk-"'};return t.length<20?{isValid:!1,error:"API key appears to be too short"}:{isValid:!0}}}function aT(){let{user:e,loading:t,isAdmin:s}=(0,tu.A)();(0,tf.useRouter)();let[r,n]=(0,th.useState)(null),[a,i]=(0,th.useState)(!0),[o,l]=(0,th.useState)(!1),[c,d]=(0,th.useState)(!1),[h,u]=(0,th.useState)(!1),[f,p]=(0,th.useState)({claudeApiKey:"",openaiApiKey:""}),[x,m]=(0,th.useState)({}),g=async()=>{try{i(!0);let e=await aM.getAPIConfiguration();n(e),e&&(p({claudeApiKey:e.claudeApiKey||"",openaiApiKey:e.openaiApiKey||""}),m({claudeStatus:e.claudeStatus,openaiStatus:e.openaiStatus}))}catch(e){console.error("Error fetching API config:",e),alert("Error loading API configuration")}finally{i(!1)}},b=async()=>{try{if(l(!0),f.claudeApiKey){let e=aM.validateAPIKey("claude",f.claudeApiKey);if(!e.isValid)return void alert(`Claude API Key Error: ${e.error}`)}if(f.openaiApiKey){let e=aM.validateAPIKey("openai",f.openaiApiKey);if(!e.isValid)return void alert(`OpenAI API Key Error: ${e.error}`)}await aM.saveAPIConfiguration({claudeApiKey:f.claudeApiKey||void 0,openaiApiKey:f.openaiApiKey||void 0}),alert("API configuration saved successfully!"),await g()}catch(e){console.error("Error saving API config:",e),alert("Error saving API configuration")}finally{l(!1)}},y=async()=>{try{d(!0);let e=await aM.testAPIConnections();m(e);let t=[];"connected"===e.claudeStatus?t.push("✅ Claude API: Connected successfully"):"error"===e.claudeStatus?t.push(`❌ Claude API: ${e.claudeError||"Connection failed"}`):t.push("⚪ Claude API: Not configured"),"connected"===e.openaiStatus?t.push("✅ OpenAI API: Connected successfully"):"error"===e.openaiStatus?t.push(`❌ OpenAI API: ${e.openaiError||"Connection failed"}`):t.push("⚪ OpenAI API: Not configured"),alert("API Connection Test Results:\n\n"+t.join("\n"))}catch(e){console.error("Error testing connections:",e),alert("Error testing API connections. Please try again.")}finally{d(!1)}},w=e=>{switch(e){case"connected":return(0,td.jsx)("span",{className:"text-green-500",children:"✓ Connected"});case"error":return(0,td.jsx)("span",{className:"text-red-500",children:"✗ Error"});default:return(0,td.jsx)("span",{className:"text-gray-500",children:"○ Untested"})}};return t||a?(0,td.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,td.jsx)("div",{className:"text-xl",children:"Loading..."})}):s?(0,td.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,td.jsx)(tp.A,{}),(0,td.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,td.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,td.jsxs)("div",{className:"mb-8",children:[(0,td.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Settings"}),(0,td.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Configure API keys and system settings for VALTICS AI."})]}),(0,td.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,td.jsxs)("div",{className:"p-6",children:[(0,td.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,td.jsxs)("div",{children:[(0,td.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"AI API Configuration"}),(0,td.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Configure API keys for Claude AI and OpenAI to enable AI artifact generation."})]}),(0,td.jsxs)("button",{onClick:()=>u(!h),className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:[h?"Hide":"Show"," API Keys"]})]}),(0,td.jsxs)("div",{className:"space-y-6",children:[(0,td.jsxs)("div",{children:[(0,td.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Claude AI API Key"}),w(x.claudeStatus)]}),(0,td.jsx)("input",{type:h?"text":"password",value:f.claudeApiKey,onChange:e=>p(t=>({...t,claudeApiKey:e.target.value})),placeholder:"sk-ant-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,td.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,td.jsx)("a",{href:"https://console.anthropic.com/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"Anthropic Console"})]})]}),(0,td.jsxs)("div",{children:[(0,td.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"OpenAI API Key"}),w(x.openaiStatus)]}),(0,td.jsx)("input",{type:h?"text":"password",value:f.openaiApiKey,onChange:e=>p(t=>({...t,openaiApiKey:e.target.value})),placeholder:"sk-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,td.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,td.jsx)("a",{href:"https://platform.openai.com/api-keys",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"OpenAI Platform"})]})]}),(0,td.jsxs)("div",{className:"flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,td.jsxs)("button",{onClick:b,disabled:o,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[o&&(0,td.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,td.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,td.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,td.jsx)("span",{children:o?"Saving...":"Save Configuration"})]}),(0,td.jsxs)("button",{onClick:y,disabled:c||!f.claudeApiKey&&!f.openaiApiKey,className:"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[c&&(0,td.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,td.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,td.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,td.jsx)("span",{children:c?"Testing...":"Test Connections"})]})]}),r?.lastTestedAt&&(0,td.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Last tested: ",r.lastTestedAt.toLocaleString()]})]})]})}),(0,td.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md",children:(0,td.jsxs)("div",{className:"flex",children:[(0,td.jsx)("div",{className:"flex-shrink-0",children:(0,td.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,td.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,td.jsxs)("div",{className:"ml-3",children:[(0,td.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Security Notice"}),(0,td.jsx)("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:(0,td.jsx)("p",{children:"API keys are encrypted before storage and are only accessible to admin users. Keep your API keys secure and rotate them regularly. Never share your API keys with unauthorized users."})})]})]})})]})})]}):null}},76910:function(e,t,s){e.exports=function(e){var t=e.lib.WordArray,s=e.enc;function r(e){return e<<8&0xff00ff00|e>>>8&0xff00ff}return s.Utf16=s.Utf16BE={stringify:function(e){for(var t=e.words,s=e.sigBytes,r=[],n=0;n<s;n+=2){var a=t[n>>>2]>>>16-n%4*8&65535;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var s=e.length,r=[],n=0;n<s;n++)r[n>>>1]|=e.charCodeAt(n)<<16-n%2*16;return t.create(r,2*s)}},s.Utf16LE={stringify:function(e){for(var t=e.words,s=e.sigBytes,n=[],a=0;a<s;a+=2){var i=r(t[a>>>2]>>>16-a%4*8&65535);n.push(String.fromCharCode(i))}return n.join("")},parse:function(e){for(var s=e.length,n=[],a=0;a<s;a++)n[a>>>1]|=r(e.charCodeAt(a)<<16-a%2*16);return t.create(n,2*s)}},e.enc.Utf16}(s(98846))},77026:function(e,t,s){var r;r=s(98846),s(86348),r.mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();function t(e,t,s,r){var n,a=this._iv;a?(n=a.slice(0),this._iv=void 0):n=this._prevBlock,r.encryptBlock(n,0);for(var i=0;i<s;i++)e[t+i]^=n[i]}return e.Encryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize;t.call(this,e,s,n,r),this._prevBlock=e.slice(s,s+n)}}),e.Decryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize,a=e.slice(s,s+n);t.call(this,e,s,n,r),this._prevBlock=a}}),e}(),e.exports=r.mode.CFB},77089:function(e,t,s){var r;r=s(98846),s(28165),s(40465),s(24613),s(86348),function(){var e=r.lib.StreamCipher,t=r.algo,s=[],n=[],a=[],i=t.RabbitLegacy=e.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,s=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,0xffff0000&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,0xffff0000&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,0xffff0000&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,0xffff0000&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)o.call(this);for(var n=0;n<8;n++)r[n]^=s[n+4&7];if(t){var a=t.words,i=a[0],l=a[1],c=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,d=(l<<8|l>>>24)&0xff00ff|(l<<24|l>>>8)&0xff00ff00,h=c>>>16|0xffff0000&d,u=d<<16|65535&c;r[0]^=c,r[1]^=h,r[2]^=d,r[3]^=u,r[4]^=c,r[5]^=h,r[6]^=d,r[7]^=u;for(var n=0;n<4;n++)o.call(this)}},_doProcessBlock:function(e,t){var r=this._X;o.call(this),s[0]=r[0]^r[5]>>>16^r[3]<<16,s[1]=r[2]^r[7]>>>16^r[5]<<16,s[2]=r[4]^r[1]>>>16^r[7]<<16,s[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)s[n]=(s[n]<<8|s[n]>>>24)&0xff00ff|(s[n]<<24|s[n]>>>8)&0xff00ff00,e[t+n]^=s[n]},blockSize:4,ivSize:2});function o(){for(var e=this._X,t=this._C,s=0;s<8;s++)n[s]=t[s];t[0]=t[0]+0x4d34d34d+this._b|0,t[1]=t[1]+0xd34d34d3+ +(t[0]>>>0<n[0]>>>0)|0,t[2]=t[2]+0x34d34d34+ +(t[1]>>>0<n[1]>>>0)|0,t[3]=t[3]+0x4d34d34d+ +(t[2]>>>0<n[2]>>>0)|0,t[4]=t[4]+0xd34d34d3+ +(t[3]>>>0<n[3]>>>0)|0,t[5]=t[5]+0x34d34d34+ +(t[4]>>>0<n[4]>>>0)|0,t[6]=t[6]+0x4d34d34d+ +(t[5]>>>0<n[5]>>>0)|0,t[7]=t[7]+0xd34d34d3+ +(t[6]>>>0<n[6]>>>0)|0,this._b=+(t[7]>>>0<n[7]>>>0);for(var s=0;s<8;s++){var r=e[s]+t[s],i=65535&r,o=r>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((0xffff0000&r)*r|0)+((65535&r)*r|0);a[s]=l^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}r.RabbitLegacy=e._createHelper(i)}(),e.exports=r.RabbitLegacy},78978:function(e,t,s){var r;r=s(98846),s(23135),function(e){var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.x64.Word,i=r.algo,o=[],l=[],c=[];!function(){for(var e=1,t=0,s=0;s<24;s++){o[e+5*t]=(s+1)*(s+2)/2%64;var r=t%5,n=(2*e+3*t)%5;e=r,t=n}for(var e=0;e<5;e++)for(var t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,d=0;d<24;d++){for(var h=0,u=0,f=0;f<7;f++){if(1&i){var p=(1<<f)-1;p<32?u^=1<<p:h^=1<<p-32}128&i?i=i<<1^113:i<<=1}c[d]=a.create(h,u)}}();for(var d=[],h=0;h<25;h++)d[h]=a.create();var u=i.SHA3=n.extend({cfg:n.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var s=this._state,r=this.blockSize/2,n=0;n<r;n++){var a=e[t+2*n],i=e[t+2*n+1];a=(a<<8|a>>>24)&0xff00ff|(a<<24|a>>>8)&0xff00ff00,i=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00;var h=s[n];h.high^=i,h.low^=a}for(var u=0;u<24;u++){for(var f=0;f<5;f++){for(var p=0,x=0,m=0;m<5;m++){var h=s[f+5*m];p^=h.high,x^=h.low}var g=d[f];g.high=p,g.low=x}for(var f=0;f<5;f++)for(var b=d[(f+4)%5],y=d[(f+1)%5],w=y.high,_=y.low,p=b.high^(w<<1|_>>>31),x=b.low^(_<<1|w>>>31),m=0;m<5;m++){var h=s[f+5*m];h.high^=p,h.low^=x}for(var v=1;v<25;v++){var p,x,h=s[v],k=h.high,S=h.low,A=o[v];A<32?(p=k<<A|S>>>32-A,x=S<<A|k>>>32-A):(p=S<<A-32|k>>>64-A,x=k<<A-32|S>>>64-A);var R=d[l[v]];R.high=p,R.low=x}var I=d[0],P=s[0];I.high=P.high,I.low=P.low;for(var f=0;f<5;f++)for(var m=0;m<5;m++){var v=f+5*m,h=s[v],$=d[v],C=d[(f+1)%5+5*m],E=d[(f+2)%5+5*m];h.high=$.high^~C.high&E.high,h.low=$.low^~C.low&E.low}var h=s[0],O=c[u];h.high^=O.high,h.low^=O.low}},_doFinalize:function(){var t=this._data,r=t.words;this._nDataBytes;var n=8*t.sigBytes,a=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((n+1)/a)*a>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var i=this._state,o=this.cfg.outputLength/8,l=o/8,c=[],d=0;d<l;d++){var h=i[d],u=h.high,f=h.low;u=(u<<8|u>>>24)&0xff00ff|(u<<24|u>>>8)&0xff00ff00,f=(f<<8|f>>>24)&0xff00ff|(f<<24|f>>>8)&0xff00ff00,c.push(f),c.push(u)}return new s.init(c,o)},clone:function(){for(var e=n.clone.call(this),t=e._state=this._state.slice(0),s=0;s<25;s++)t[s]=t[s].clone();return e}});r.SHA3=n._createHelper(u),r.HmacSHA3=n._createHmacHelper(u)}(Math),e.exports=r.SHA3},79551:e=>{"use strict";e.exports=require("url")},81381:function(e,t,s){var r,n;r=s(98846),s(86348),r.mode.ECB=((n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),n.Decryptor=n.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),n),e.exports=r.mode.ECB},81630:e=>{"use strict";e.exports=require("http")},81881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\settings\\page.tsx","default")},86348:function(e,t,s){var r,n,a,i,o,l,c,d,h,u,f,p,x,m,g,b,y,w;r=s(98846),s(24613),e.exports=void(r.lib.Cipher||(a=(n=r.lib).Base,i=n.WordArray,o=n.BufferedBlockAlgorithm,(l=r.enc).Utf8,c=l.Base64,d=r.algo.EvpKDF,h=n.Cipher=o.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,s){this.cfg=this.cfg.extend(s),this._xformMode=e,this._key=t,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?w:b}return function(t){return{encrypt:function(s,r,n){return e(r).encrypt(t,s,r,n)},decrypt:function(s,r,n){return e(r).decrypt(t,s,r,n)}}}}()}),n.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),u=r.mode={},f=n.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=u.CBC=function(){var e=f.extend();function t(e,t,s){var r,n=this._iv;n?(r=n,this._iv=void 0):r=this._prevBlock;for(var a=0;a<s;a++)e[t+a]^=r[a]}return e.Encryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize;t.call(this,e,s,n),r.encryptBlock(e,s),this._prevBlock=e.slice(s,s+n)}}),e.Decryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize,a=e.slice(s,s+n);r.decryptBlock(e,s),t.call(this,e,s,n),this._prevBlock=a}}),e}(),x=(r.pad={}).Pkcs7={pad:function(e,t){for(var s=4*t,r=s-e.sigBytes%s,n=r<<24|r<<16|r<<8|r,a=[],o=0;o<r;o+=4)a.push(n);var l=i.create(a,r);e.concat(l)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},n.BlockCipher=h.extend({cfg:h.cfg.extend({mode:p,padding:x}),reset:function(){h.reset.call(this);var e,t=this.cfg,s=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,s&&s.words):(this._mode=e.call(r,this,s&&s.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),m=n.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),g=(r.format={}).OpenSSL={stringify:function(e){var t,s=e.ciphertext,r=e.salt;return(r?i.create([0x53616c74,0x65645f5f]).concat(r).concat(s):s).toString(c)},parse:function(e){var t,s=c.parse(e),r=s.words;return 0x53616c74==r[0]&&0x65645f5f==r[1]&&(t=i.create(r.slice(2,4)),r.splice(0,4),s.sigBytes-=16),m.create({ciphertext:s,salt:t})}},b=n.SerializableCipher=a.extend({cfg:a.extend({format:g}),encrypt:function(e,t,s,r){r=this.cfg.extend(r);var n=e.createEncryptor(s,r),a=n.finalize(t),i=n.cfg;return m.create({ciphertext:a,key:s,iv:i.iv,algorithm:e,mode:i.mode,padding:i.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,s,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(s,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(r.kdf={}).OpenSSL={execute:function(e,t,s,r,n){if(r||(r=i.random(8)),n)var a=d.create({keySize:t+s,hasher:n}).compute(e,r);else var a=d.create({keySize:t+s}).compute(e,r);var o=i.create(a.words.slice(t),4*s);return a.sigBytes=4*t,m.create({key:a,iv:o,salt:r})}},w=n.PasswordBasedCipher=b.extend({cfg:b.cfg.extend({kdf:y}),encrypt:function(e,t,s,r){var n=(r=this.cfg.extend(r)).kdf.execute(s,e.keySize,e.ivSize,r.salt,r.hasher);r.iv=n.iv;var a=b.encrypt.call(this,e,t,n.key,r);return a.mixIn(n),a},decrypt:function(e,t,s,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var n=r.kdf.execute(s,e.keySize,e.ivSize,t.salt,r.hasher);return r.iv=n.iv,b.decrypt.call(this,e,t,n.key,r)}})))},88072:(e,t,s)=>{Promise.resolve().then(s.bind(s,81881))},88822:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),n=s(48088),a=s(88170),i=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,81881)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\settings\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\settings\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},91824:function(e,t,s){var r;r=s(98846),s(28165),s(40465),s(24613),s(86348),function(){var e=r.lib.StreamCipher,t=r.algo,s=t.RC4=e.extend({_doReset:function(){for(var e=this._key,t=e.words,s=e.sigBytes,r=this._S=[],n=0;n<256;n++)r[n]=n;for(var n=0,a=0;n<256;n++){var i=n%s,o=t[i>>>2]>>>24-i%4*8&255;a=(a+r[n]+o)%256;var l=r[n];r[n]=r[a],r[a]=l}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=n.call(this)},keySize:8,ivSize:0});function n(){for(var e=this._S,t=this._i,s=this._j,r=0,n=0;n<4;n++){s=(s+e[t=(t+1)%256])%256;var a=e[t];e[t]=e[s],e[s]=a,r|=e[(e[t]+e[s])%256]<<24-8*n}return this._i=t,this._j=s,r}r.RC4=e._createHelper(s);var a=t.RC4Drop=s.extend({cfg:s.cfg.extend({drop:192}),_doReset:function(){s._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)n.call(this)}});r.RC4Drop=e._createHelper(a)}(),e.exports=r.RC4},94326:function(e,t,s){var r,n,a;r=s(98846),s(86348),r.mode.OFB=(a=(n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){var s=this._cipher,r=s.blockSize,n=this._iv,a=this._keystream;n&&(a=this._keystream=n.slice(0),this._iv=void 0),s.encryptBlock(a,0);for(var i=0;i<r;i++)e[t+i]^=a[i]}}),n.Decryptor=a,n),e.exports=r.mode.OFB},94735:e=>{"use strict";e.exports=require("events")},98846:function(e,t,s){var r;e.exports=r||function(e,t){if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&"undefined"!=typeof global&&global.crypto&&(r=global.crypto),!r)try{r=s(55511)}catch(e){}var r,n=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var s;return e.prototype=t,s=new e,e.prototype=null,s}}(),i={},o=i.lib={},l=o.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=o.WordArray=l.extend({init:function(e,s){e=this.words=e||[],t!=s?this.sigBytes=s:this.sigBytes=4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,s=e.words,r=this.sigBytes,n=e.sigBytes;if(this.clamp(),r%4)for(var a=0;a<n;a++){var i=s[a>>>2]>>>24-a%4*8&255;t[r+a>>>2]|=i<<24-(r+a)%4*8}else for(var o=0;o<n;o+=4)t[r+o>>>2]=s[o>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,s=this.sigBytes;t[s>>>2]&=0xffffffff<<32-s%4*8,t.length=e.ceil(s/4)},clone:function(){var e=l.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],s=0;s<e;s+=4)t.push(n());return new c.init(t,e)}}),d=i.enc={},h=d.Hex={stringify:function(e){for(var t=e.words,s=e.sigBytes,r=[],n=0;n<s;n++){var a=t[n>>>2]>>>24-n%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,s=[],r=0;r<t;r+=2)s[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new c.init(s,t/2)}},u=d.Latin1={stringify:function(e){for(var t=e.words,s=e.sigBytes,r=[],n=0;n<s;n++){var a=t[n>>>2]>>>24-n%4*8&255;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var t=e.length,s=[],r=0;r<t;r++)s[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new c.init(s,t)}},f=d.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=l.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var s,r=this._data,n=r.words,a=r.sigBytes,i=this.blockSize,o=a/(4*i),l=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*i,d=e.min(4*l,a);if(l){for(var h=0;h<l;h+=i)this._doProcessBlock(n,h);s=n.splice(0,l),r.sigBytes-=d}return new c.init(s,d)},clone:function(){var e=l.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=p.extend({cfg:l.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,s){return new e.init(s).finalize(t)}},_createHmacHelper:function(e){return function(t,s){return new x.HMAC.init(e,s).finalize(t)}}});var x=i.algo={};return i}(Math)},99305:function(e,t,s){var r;r=s(98846),s(86348),r.pad.NoPadding={pad:function(){},unpad:function(){}},e.exports=r.pad.NoPadding}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,823,567,533,77],()=>s(88822));module.exports=r})();