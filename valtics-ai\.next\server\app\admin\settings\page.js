(()=>{var e={};e.id=122,e.ids=[122],e.modules={2101:function(e,t,r){var s;s=r(98846),r(28165),r(40465),r(24613),r(86348),function(){var e=s.lib.StreamCipher,t=s.algo,r=[],n=[],a=[],i=t.Rabbit=e.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=(e[r]<<8|e[r]>>>24)&0xff00ff|(e[r]<<24|e[r]>>>8)&0xff00ff00;var s=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,0xffff0000&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,0xffff0000&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,0xffff0000&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,0xffff0000&e[3]|65535&e[0]];this._b=0;for(var r=0;r<4;r++)o.call(this);for(var r=0;r<8;r++)n[r]^=s[r+4&7];if(t){var a=t.words,i=a[0],l=a[1],c=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,d=(l<<8|l>>>24)&0xff00ff|(l<<24|l>>>8)&0xff00ff00,h=c>>>16|0xffff0000&d,u=d<<16|65535&c;n[0]^=c,n[1]^=h,n[2]^=d,n[3]^=u,n[4]^=c,n[5]^=h,n[6]^=d,n[7]^=u;for(var r=0;r<4;r++)o.call(this)}},_doProcessBlock:function(e,t){var s=this._X;o.call(this),r[0]=s[0]^s[5]>>>16^s[3]<<16,r[1]=s[2]^s[7]>>>16^s[5]<<16,r[2]=s[4]^s[1]>>>16^s[7]<<16,r[3]=s[6]^s[3]>>>16^s[1]<<16;for(var n=0;n<4;n++)r[n]=(r[n]<<8|r[n]>>>24)&0xff00ff|(r[n]<<24|r[n]>>>8)&0xff00ff00,e[t+n]^=r[n]},blockSize:4,ivSize:2});function o(){for(var e=this._X,t=this._C,r=0;r<8;r++)n[r]=t[r];t[0]=t[0]+0x4d34d34d+this._b|0,t[1]=t[1]+0xd34d34d3+ +(t[0]>>>0<n[0]>>>0)|0,t[2]=t[2]+0x34d34d34+ +(t[1]>>>0<n[1]>>>0)|0,t[3]=t[3]+0x4d34d34d+ +(t[2]>>>0<n[2]>>>0)|0,t[4]=t[4]+0xd34d34d3+ +(t[3]>>>0<n[3]>>>0)|0,t[5]=t[5]+0x34d34d34+ +(t[4]>>>0<n[4]>>>0)|0,t[6]=t[6]+0x4d34d34d+ +(t[5]>>>0<n[5]>>>0)|0,t[7]=t[7]+0xd34d34d3+ +(t[6]>>>0<n[6]>>>0)|0,this._b=+(t[7]>>>0<n[7]>>>0);for(var r=0;r<8;r++){var s=e[r]+t[r],i=65535&s,o=s>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((0xffff0000&s)*s|0)+((65535&s)*s|0);a[r]=l^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}s.Rabbit=e._createHelper(i)}(),e.exports=s.Rabbit},2613:function(e,t,r){var s;s=r(98846),r(28165),r(40465),r(24613),r(86348),function(){var e=s.lib,t=e.WordArray,r=e.BlockCipher,n=s.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],i=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],o=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,0x10000000:32768,0x20000000:8421378,0x30000000:2,0x40000000:512,0x50000000:8421890,0x60000000:8389122,0x70000000:8388608,0x80000000:514,0x90000000:8389120,0xa0000000:33280,0xb0000000:8421376,0xc0000000:32770,0xd0000000:8388610,0xe0000000:0,0xf0000000:33282,0x8000000:0,0x18000000:8421890,0x28000000:33282,0x38000000:32768,0x48000000:8421888,0x58000000:512,0x68000000:8421378,0x78000000:2,0x88000000:8389120,0x98000000:33280,0xa8000000:8421376,0xb8000000:8389122,0xc8000000:8388610,0xd8000000:32770,0xe8000000:514,0xf8000000:8388608,1:32768,0x10000001:2,0x20000001:8421888,0x30000001:8388608,0x40000001:8421378,0x50000001:33280,0x60000001:512,0x70000001:8389122,0x80000001:8421890,0x90000001:8421376,0xa0000001:8388610,0xb0000001:33282,0xc0000001:514,0xd0000001:8389120,0xe0000001:32770,0xf0000001:0,0x8000001:8421890,0x18000001:8421376,0x28000001:8388608,0x38000001:512,0x48000001:32768,0x58000001:8388610,0x68000001:2,0x78000001:33282,0x88000001:32770,0x98000001:8389122,0xa8000001:514,0xb8000001:8421888,0xc8000001:8389120,0xd8000001:0,0xe8000001:33280,0xf8000001:8421378},{0:0x40084010,0x1000000:16384,0x2000000:524288,0x3000000:0x40080010,0x4000000:0x40000010,0x5000000:0x40084000,0x6000000:0x40004000,0x7000000:16,0x8000000:540672,0x9000000:0x40004010,0xa000000:0x40000000,0xb000000:540688,0xc000000:524304,0xd000000:0,0xe000000:16400,0xf000000:0x40080000,8388608:0x40004000,0x1800000:540688,0x2800000:16,0x3800000:0x40004010,0x4800000:0x40084010,0x5800000:0x40000000,0x6800000:524288,0x7800000:0x40080010,0x8800000:524304,0x9800000:0,0xa800000:16384,0xb800000:0x40080000,0xc800000:0x40000010,0xd800000:540672,0xe800000:0x40084000,0xf800000:16400,0x10000000:0,0x11000000:0x40080010,0x12000000:0x40004010,0x13000000:0x40084000,0x14000000:0x40080000,0x15000000:16,0x16000000:540688,0x17000000:16384,0x18000000:16400,0x19000000:524288,0x1a000000:524304,0x1b000000:0x40000010,0x1c000000:540672,0x1d000000:0x40004000,0x1e000000:0x40000000,0x1f000000:0x40084010,0x10800000:540688,0x11800000:524288,0x12800000:0x40080000,0x13800000:16384,0x14800000:0x40004000,0x15800000:0x40084010,0x16800000:16,0x17800000:0x40000000,0x18800000:0x40084000,0x19800000:0x40000010,0x1a800000:0x40004010,0x1b800000:524304,0x1c800000:0,0x1d800000:16400,0x1e800000:0x40080010,0x1f800000:540672},{0:260,1048576:0,2097152:0x4000100,3145728:65796,4194304:65540,5242880:0x4000004,6291456:0x4010104,7340032:0x4010000,8388608:0x4000000,9437184:0x4010100,0xa00000:65792,0xb00000:0x4010004,0xc00000:0x4000104,0xd00000:65536,0xe00000:4,0xf00000:256,524288:0x4010100,1572864:0x4010004,2621440:0,3670016:0x4000100,4718592:0x4000004,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,0xa80000:0x4010000,0xb80000:65796,0xc80000:65792,0xd80000:0x4000104,0xe80000:0x4010104,0xf80000:0x4000000,0x1000000:0x4010100,0x1100000:65540,0x1200000:65536,0x1300000:0x4000100,0x1400000:256,0x1500000:0x4010104,0x1600000:0x4000004,0x1700000:0,0x1800000:0x4000104,0x1900000:0x4000000,0x1a00000:4,0x1b00000:65792,0x1c00000:0x4010000,0x1d00000:260,0x1e00000:65796,0x1f00000:0x4010004,0x1080000:0x4000000,0x1180000:260,0x1280000:0x4010100,0x1380000:0,0x1480000:65540,0x1580000:0x4000100,0x1680000:256,0x1780000:0x4010004,0x1880000:65536,0x1980000:0x4010104,0x1a80000:65796,0x1b80000:0x4000004,0x1c80000:0x4000104,0x1d80000:0x4010000,0x1e80000:4,0x1f80000:65792},{0:0x80401000,65536:0x80001040,131072:4198464,196608:0x80400000,262144:0,327680:4198400,393216:0x80000040,458752:4194368,524288:0x80000000,589824:4194304,655360:64,720896:0x80001000,786432:0x80400040,851968:4160,917504:4096,983040:0x80401040,32768:0x80001040,98304:64,163840:0x80400040,229376:0x80001000,294912:4198400,360448:0x80401040,425984:0,491520:0x80400000,557056:4096,622592:0x80401000,688128:4194304,753664:4160,819200:0x80000000,884736:4194368,950272:4198464,1015808:0x80000040,1048576:4194368,1114112:4198400,1179648:0x80000040,1245184:0,1310720:4160,1376256:0x80400040,1441792:0x80401000,1507328:0x80001040,1572864:0x80401040,1638400:0x80000000,1703936:0x80400000,1769472:4198464,1835008:0x80001000,1900544:4194304,1966080:64,2031616:4096,1081344:0x80400000,1146880:0x80401040,1212416:0,1277952:4198400,1343488:4194368,1409024:0x80000000,1474560:0x80001040,1540096:64,1605632:0x80000040,1671168:4096,1736704:0x80001000,1802240:0x80400040,1867776:4160,1933312:0x80401000,1998848:4194304,2064384:4198464},{0:128,4096:0x1040000,8192:262144,12288:0x20000000,16384:0x20040080,20480:0x1000080,24576:0x21000080,28672:262272,32768:0x1000000,36864:0x20040000,40960:0x20000080,45056:0x21040080,49152:0x21040000,53248:0,57344:0x1040080,61440:0x21000000,2048:0x1040080,6144:0x21000080,10240:128,14336:0x1040000,18432:262144,22528:0x20040080,26624:0x21040000,30720:0x20000000,34816:0x20040000,38912:0,43008:0x21040080,47104:0x1000080,51200:0x20000080,55296:0x21000000,59392:0x1000000,63488:262272,65536:262144,69632:128,73728:0x20000000,77824:0x21000080,81920:0x1000080,86016:0x21040000,90112:0x20040080,94208:0x1000000,98304:0x21040080,102400:0x21000000,106496:0x1040000,110592:0x20040000,114688:262272,118784:0x20000080,122880:0,126976:0x1040080,67584:0x21000080,71680:0x1000000,75776:0x1040000,79872:0x20040080,83968:0x20000000,88064:0x1040080,92160:128,96256:0x21040000,100352:262272,104448:0x21040080,108544:0,112640:0x21000000,116736:0x1000080,120832:262144,124928:0x20040000,129024:0x20000080},{0:0x10000008,256:8192,512:0x10200000,768:0x10202008,1024:0x10002000,1280:2097152,1536:2097160,1792:0x10000000,2048:0,2304:0x10002008,2560:2105344,2816:8,3072:0x10200008,3328:2105352,3584:8200,3840:0x10202000,128:0x10200000,384:0x10202008,640:8,896:2097152,1152:2105352,1408:0x10000008,1664:0x10002000,1920:8200,2176:2097160,2432:8192,2688:0x10002008,2944:0x10200008,3200:0,3456:0x10202000,3712:2105344,3968:0x10000000,4096:0x10002000,4352:0x10200008,4608:0x10202008,4864:8200,5120:2097152,5376:0x10000000,5632:0x10000008,5888:2105344,6144:2105352,6400:0,6656:8,6912:0x10200000,7168:8192,7424:0x10002008,7680:0x10202000,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:0x10000008,5248:0x10002000,5504:8200,5760:0x10202008,6016:0x10200000,6272:0x10202000,6528:0x10200008,6784:8192,7040:2105352,7296:2097160,7552:0,7808:0x10000000,8064:0x10002008},{0:1048576,16:0x2000401,32:1024,48:1049601,64:0x2100401,80:0,96:1,112:0x2100001,128:0x2000400,144:1048577,160:0x2000001,176:0x2100400,192:0x2100000,208:1025,224:1049600,240:0x2000000,8:0x2100001,24:0,40:0x2000401,56:0x2100400,72:1048576,88:0x2000001,104:0x2000000,120:1025,136:1049601,152:0x2000400,168:0x2100000,184:1048577,200:1024,216:0x2100401,232:1,248:1049600,256:0x2000000,272:1048576,288:0x2000401,304:0x2100001,320:1048577,336:0x2000400,352:0x2100400,368:1049601,384:1025,400:0x2100401,416:1049600,432:1,448:0,464:0x2100000,480:0x2000001,496:1024,264:1049600,280:0x2000401,296:0x2100001,312:1,328:0x2000000,344:1048576,360:1025,376:0x2100400,392:0x2000001,408:0x2100000,424:0,440:0x2100401,456:1049601,472:1024,488:0x2000400,504:1048577},{0:0x8000820,1:131072,2:0x8000000,3:32,4:131104,5:0x8020820,6:0x8020800,7:2048,8:0x8020000,9:0x8000800,10:133120,11:0x8020020,12:2080,13:0,14:0x8000020,15:133152,0x80000000:2048,0x80000001:0x8020820,0x80000002:0x8000820,0x80000003:0x8000000,0x80000004:0x8020000,0x80000005:133120,0x80000006:133152,0x80000007:32,0x80000008:0x8000020,0x80000009:2080,0x8000000a:131104,0x8000000b:0x8020800,0x8000000c:0,0x8000000d:0x8020020,0x8000000e:0x8000800,0x8000000f:131072,16:133152,17:0x8020800,18:32,19:2048,20:0x8000800,21:0x8000020,22:0x8020020,23:131072,24:0,25:131104,26:0x8020000,27:0x8000820,28:0x8020820,29:133120,30:2080,31:0x8000000,0x80000010:131072,0x80000011:2048,0x80000012:0x8020020,0x80000013:133152,0x80000014:32,0x80000015:0x8020000,0x80000016:0x8000000,0x80000017:0x8000820,0x80000018:0x8020820,0x80000019:0x8000020,0x8000001a:0x8000800,0x8000001b:0,0x8000001c:133120,0x8000001d:2080,0x8000001e:131104,0x8000001f:0x8020800}],c=[0xf8000001,0x1f800000,0x1f80000,2064384,129024,8064,504,0x8000001f],d=n.DES=r.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var s=a[r]-1;t[r]=e[s>>>5]>>>31-s%32&1}for(var n=this._subKeys=[],l=0;l<16;l++){for(var c=n[l]=[],d=o[l],r=0;r<24;r++)c[r/6|0]|=t[(i[r]-1+d)%28]<<31-r%6,c[4+(r/6|0)]|=t[28+(i[r+24]-1+d)%28]<<31-r%6;c[0]=c[0]<<1|c[0]>>>31;for(var r=1;r<7;r++)c[r]=c[r]>>>(r-1)*4+3;c[7]=c[7]<<5|c[7]>>>27}for(var h=this._invSubKeys=[],r=0;r<16;r++)h[r]=n[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,0xf0f0f0f),h.call(this,16,65535),u.call(this,2,0x33333333),u.call(this,8,0xff00ff),h.call(this,1,0x55555555);for(var s=0;s<16;s++){for(var n=r[s],a=this._lBlock,i=this._rBlock,o=0,d=0;d<8;d++)o|=l[d][((i^n[d])&c[d])>>>0];this._lBlock=i,this._rBlock=a^o}var f=this._lBlock;this._lBlock=this._rBlock,this._rBlock=f,h.call(this,1,0x55555555),u.call(this,8,0xff00ff),u.call(this,2,0x33333333),h.call(this,16,65535),h.call(this,4,0xf0f0f0f),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function u(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}s.DES=r._createHelper(d);var f=n.TripleDES=r.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var r=e.slice(0,2),s=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=d.createEncryptor(t.create(r)),this._des2=d.createEncryptor(t.create(s)),this._des3=d.createEncryptor(t.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});s.TripleDES=r._createHelper(f)}(),e.exports=s.TripleDES},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4765:function(e,t,r){var s;s=r(98846),r(23135),r(25353),r(76910),r(28165),r(50584),r(40465),r(18796),r(51398),r(16279),r(9095),r(45422),r(78978),r(36401),r(22938),r(25408),r(24613),r(86348),r(77026),r(65028),r(30671),r(94326),r(81381),r(51150),r(44174),r(37085),r(13926),r(99305),r(39914),r(7246),r(2613),r(91824),r(2101),r(77089),r(15619),e.exports=s},5481:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(60687),n=r(85814),a=r.n(n),i=r(30474),o=r(51108),l=r(16189),c=r(27436),d=r(31769);function h({title:e="VALTICS AI",showBackButton:t=!1,backUrl:r="/dashboard",backText:n="← Back to Dashboard"}){let{user:h,logOut:u,isAdmin:f}=(0,o.A)(),p=(0,l.useRouter)(),x=async()=>{try{await u(),p.push("/")}catch(e){console.error("Error logging out:",e)}};return h?(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)(a(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,s.jsx)(i.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,s.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&(0,s.jsx)(a(),{href:r,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:n}),!t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,s.jsx)(a(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),f&&(0,s.jsx)(a(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,s.jsx)(a(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,s.jsx)(d.I,{}),(0,s.jsx)(c.default,{}),(0,s.jsx)("button",{onClick:x,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},7246:function(e,t,r){var s,n,a,i,o,l,c,d,h,u,f,p,x,m,g;s=r(98846),r(28165),r(40465),r(24613),r(86348),n=s.lib.BlockCipher,a=s.algo,i=[],o=[],l=[],c=[],d=[],h=[],u=[],f=[],p=[],x=[],function(){for(var e=[],t=0;t<256;t++)t<128?e[t]=t<<1:e[t]=t<<1^283;for(var r=0,s=0,t=0;t<256;t++){var n=s^s<<1^s<<2^s<<3^s<<4;n=n>>>8^255&n^99,i[r]=n,o[n]=r;var a=e[r],m=e[a],g=e[m],b=257*e[n]^0x1010100*n;l[r]=b<<24|b>>>8,c[r]=b<<16|b>>>16,d[r]=b<<8|b>>>24,h[r]=b;var b=0x1010101*g^65537*m^257*a^0x1010100*r;u[n]=b<<24|b>>>8,f[n]=b<<16|b>>>16,p[n]=b<<8|b>>>24,x[n]=b,r?(r=a^e[e[e[g^a]]],s^=e[e[s]]):r=s=1}}(),m=[0,1,2,4,8,16,32,64,128,27,54],g=a.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e,t=this._keyPriorReset=this._key,r=t.words,s=t.sigBytes/4,n=((this._nRounds=s+6)+1)*4,a=this._keySchedule=[],o=0;o<n;o++)o<s?a[o]=r[o]:(e=a[o-1],o%s?s>6&&o%s==4&&(e=i[e>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e]):e=(i[(e=e<<8|e>>>24)>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e])^m[o/s|0]<<24,a[o]=a[o-s]^e);for(var l=this._invKeySchedule=[],c=0;c<n;c++){var o=n-c;if(c%4)var e=a[o];else var e=a[o-4];c<4||o<=4?l[c]=e:l[c]=u[i[e>>>24]]^f[i[e>>>16&255]]^p[i[e>>>8&255]]^x[i[255&e]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,l,c,d,h,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,u,f,p,x,o);var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,s,n,a,i,o){for(var l=this._nRounds,c=e[t]^r[0],d=e[t+1]^r[1],h=e[t+2]^r[2],u=e[t+3]^r[3],f=4,p=1;p<l;p++){var x=s[c>>>24]^n[d>>>16&255]^a[h>>>8&255]^i[255&u]^r[f++],m=s[d>>>24]^n[h>>>16&255]^a[u>>>8&255]^i[255&c]^r[f++],g=s[h>>>24]^n[u>>>16&255]^a[c>>>8&255]^i[255&d]^r[f++],b=s[u>>>24]^n[c>>>16&255]^a[d>>>8&255]^i[255&h]^r[f++];c=x,d=m,h=g,u=b}var x=(o[c>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&u])^r[f++],m=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[u>>>8&255]<<8|o[255&c])^r[f++],g=(o[h>>>24]<<24|o[u>>>16&255]<<16|o[c>>>8&255]<<8|o[255&d])^r[f++],b=(o[u>>>24]<<24|o[c>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[f++];e[t]=x,e[t+1]=m,e[t+2]=g,e[t+3]=b},keySize:8}),s.AES=n._createHelper(g),e.exports=s.AES},9095:function(e,t,r){var s;s=r(98846),r(23135),function(){var e=s.lib.Hasher,t=s.x64,r=t.Word,n=t.WordArray,a=s.algo;function i(){return r.create.apply(r,arguments)}for(var o=[i(0x428a2f98,0xd728ae22),i(0x71374491,0x23ef65cd),i(0xb5c0fbcf,0xec4d3b2f),i(0xe9b5dba5,0x8189dbbc),i(0x3956c25b,0xf348b538),i(0x59f111f1,0xb605d019),i(0x923f82a4,0xaf194f9b),i(0xab1c5ed5,0xda6d8118),i(0xd807aa98,0xa3030242),i(0x12835b01,0x45706fbe),i(0x243185be,0x4ee4b28c),i(0x550c7dc3,0xd5ffb4e2),i(0x72be5d74,0xf27b896f),i(0x80deb1fe,0x3b1696b1),i(0x9bdc06a7,0x25c71235),i(0xc19bf174,0xcf692694),i(0xe49b69c1,0x9ef14ad2),i(0xefbe4786,0x384f25e3),i(0xfc19dc6,0x8b8cd5b5),i(0x240ca1cc,0x77ac9c65),i(0x2de92c6f,0x592b0275),i(0x4a7484aa,0x6ea6e483),i(0x5cb0a9dc,0xbd41fbd4),i(0x76f988da,0x831153b5),i(0x983e5152,0xee66dfab),i(0xa831c66d,0x2db43210),i(0xb00327c8,0x98fb213f),i(0xbf597fc7,0xbeef0ee4),i(0xc6e00bf3,0x3da88fc2),i(0xd5a79147,0x930aa725),i(0x6ca6351,0xe003826f),i(0x14292967,0xa0e6e70),i(0x27b70a85,0x46d22ffc),i(0x2e1b2138,0x5c26c926),i(0x4d2c6dfc,0x5ac42aed),i(0x53380d13,0x9d95b3df),i(0x650a7354,0x8baf63de),i(0x766a0abb,0x3c77b2a8),i(0x81c2c92e,0x47edaee6),i(0x92722c85,0x1482353b),i(0xa2bfe8a1,0x4cf10364),i(0xa81a664b,0xbc423001),i(0xc24b8b70,0xd0f89791),i(0xc76c51a3,0x654be30),i(0xd192e819,0xd6ef5218),i(0xd6990624,0x5565a910),i(0xf40e3585,0x5771202a),i(0x106aa070,0x32bbd1b8),i(0x19a4c116,0xb8d2d0c8),i(0x1e376c08,0x5141ab53),i(0x2748774c,0xdf8eeb99),i(0x34b0bcb5,0xe19b48a8),i(0x391c0cb3,0xc5c95a63),i(0x4ed8aa4a,0xe3418acb),i(0x5b9cca4f,0x7763e373),i(0x682e6ff3,0xd6b2b8a3),i(0x748f82ee,0x5defb2fc),i(0x78a5636f,0x43172f60),i(0x84c87814,0xa1f0ab72),i(0x8cc70208,0x1a6439ec),i(0x90befffa,0x23631e28),i(0xa4506ceb,0xde82bde9),i(0xbef9a3f7,0xb2c67915),i(0xc67178f2,0xe372532b),i(0xca273ece,0xea26619c),i(0xd186b8c7,0x21c0c207),i(0xeada7dd6,0xcde0eb1e),i(0xf57d4f7f,0xee6ed178),i(0x6f067aa,0x72176fba),i(0xa637dc5,0xa2c898a6),i(0x113f9804,0xbef90dae),i(0x1b710b35,0x131c471b),i(0x28db77f5,0x23047d84),i(0x32caab7b,0x40c72493),i(0x3c9ebe0a,0x15c9bebc),i(0x431d67c4,0x9c100d4c),i(0x4cc5d4be,0xcb3e42b6),i(0x597f299c,0xfc657e2a),i(0x5fcb6fab,0x3ad6faec),i(0x6c44198c,0x4a475817)],l=[],c=0;c<80;c++)l[c]=i();var d=a.SHA512=e.extend({_doReset:function(){this._hash=new n.init([new r.init(0x6a09e667,0xf3bcc908),new r.init(0xbb67ae85,0x84caa73b),new r.init(0x3c6ef372,0xfe94f82b),new r.init(0xa54ff53a,0x5f1d36f1),new r.init(0x510e527f,0xade682d1),new r.init(0x9b05688c,0x2b3e6c1f),new r.init(0x1f83d9ab,0xfb41bd6b),new r.init(0x5be0cd19,0x137e2179)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,s=r[0],n=r[1],a=r[2],i=r[3],c=r[4],d=r[5],h=r[6],u=r[7],f=s.high,p=s.low,x=n.high,m=n.low,g=a.high,b=a.low,y=i.high,w=i.low,_=c.high,v=c.low,k=d.high,S=d.low,A=h.high,R=h.low,I=u.high,C=u.low,P=f,$=p,j=x,E=m,M=g,O=b,N=y,B=w,T=_,L=v,D=k,W=S,U=A,H=R,q=I,F=C,z=0;z<80;z++){var K,X,J=l[z];if(z<16)X=J.high=0|e[t+2*z],K=J.low=0|e[t+2*z+1];else{var V=l[z-15],G=V.high,Q=V.low,Y=(G>>>1|Q<<31)^(G>>>8|Q<<24)^G>>>7,Z=(Q>>>1|G<<31)^(Q>>>8|G<<24)^(Q>>>7|G<<25),ee=l[z-2],et=ee.high,er=ee.low,es=(et>>>19|er<<13)^(et<<3|er>>>29)^et>>>6,en=(er>>>19|et<<13)^(er<<3|et>>>29)^(er>>>6|et<<26),ea=l[z-7],ei=ea.high,eo=ea.low,el=l[z-16],ec=el.high,ed=el.low;X=Y+ei+ +((K=Z+eo)>>>0<Z>>>0),K+=en,X=X+es+ +(K>>>0<en>>>0),K+=ed,J.high=X=X+ec+ +(K>>>0<ed>>>0),J.low=K}var eh=T&D^~T&U,eu=L&W^~L&H,ef=P&j^P&M^j&M,ep=$&E^$&O^E&O,ex=(P>>>28|$<<4)^(P<<30|$>>>2)^(P<<25|$>>>7),em=($>>>28|P<<4)^($<<30|P>>>2)^($<<25|P>>>7),eg=(T>>>14|L<<18)^(T>>>18|L<<14)^(T<<23|L>>>9),eb=(L>>>14|T<<18)^(L>>>18|T<<14)^(L<<23|T>>>9),ey=o[z],ew=ey.high,e_=ey.low,ev=F+eb,ek=q+eg+ +(ev>>>0<F>>>0),ev=ev+eu,ek=ek+eh+ +(ev>>>0<eu>>>0),ev=ev+e_,ek=ek+ew+ +(ev>>>0<e_>>>0),ev=ev+K,ek=ek+X+ +(ev>>>0<K>>>0),eS=em+ep,eA=ex+ef+ +(eS>>>0<em>>>0);q=U,F=H,U=D,H=W,D=T,W=L,T=N+ek+ +((L=B+ev|0)>>>0<B>>>0)|0,N=M,B=O,M=j,O=E,j=P,E=$,P=ek+eA+ +(($=ev+eS|0)>>>0<ev>>>0)|0}p=s.low=p+$,s.high=f+P+ +(p>>>0<$>>>0),m=n.low=m+E,n.high=x+j+ +(m>>>0<E>>>0),b=a.low=b+O,a.high=g+M+ +(b>>>0<O>>>0),w=i.low=w+B,i.high=y+N+ +(w>>>0<B>>>0),v=c.low=v+L,c.high=_+T+ +(v>>>0<L>>>0),S=d.low=S+W,d.high=k+D+ +(S>>>0<W>>>0),R=h.low=R+H,h.high=A+U+ +(R>>>0<H>>>0),C=u.low=C+F,u.high=I+q+ +(C>>>0<F>>>0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[(s+128>>>10<<5)+30]=Math.floor(r/0x100000000),t[(s+128>>>10<<5)+31]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});s.SHA512=e._createHelper(d),s.HmacSHA512=e._createHmacHelper(d)}(),e.exports=s.SHA512},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13926:function(e,t,r){var s;s=r(98846),r(86348),s.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){for(var t=e.words,r=e.sigBytes-1,r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},e.exports=s.pad.ZeroPadding},14985:e=>{"use strict";e.exports=require("dns")},15619:function(e,t,r){var s;s=r(98846),r(28165),r(40465),r(24613),r(86348),function(){var e=s.lib.BlockCipher,t=s.algo;let r=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],n=[[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a],[0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7],[0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0],[0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6]];var a={pbox:[],sbox:[]};function i(e,t){let r=e.sbox[0][t>>24&255]+e.sbox[1][t>>16&255];return r^=e.sbox[2][t>>8&255],r+=e.sbox[3][255&t]}function o(e,t,r){let s,n=t,a=r;for(let t=0;t<16;++t)n^=e.pbox[t],a=i(e,n)^a,s=n,n=a,a=s;return s=n,n=a,a=s^e.pbox[16],{left:n^=e.pbox[17],right:a}}var l=t.Blowfish=e.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key;!function(e,t,s){for(let t=0;t<4;t++){e.sbox[t]=[];for(let r=0;r<256;r++)e.sbox[t][r]=n[t][r]}let a=0;for(let n=0;n<18;n++)e.pbox[n]=r[n]^t[a],++a>=s&&(a=0);let i=0,l=0,c=0;for(let t=0;t<18;t+=2)i=(c=o(e,i,l)).left,l=c.right,e.pbox[t]=i,e.pbox[t+1]=l;for(let t=0;t<4;t++)for(let r=0;r<256;r+=2)i=(c=o(e,i,l)).left,l=c.right,e.sbox[t][r]=i,e.sbox[t][r+1]=l}(a,e.words,e.sigBytes/4)}},encryptBlock:function(e,t){var r=o(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},decryptBlock:function(e,t){var r=function(e,t,r){let s,n=t,a=r;for(let t=17;t>1;--t)n^=e.pbox[t],a=i(e,n)^a,s=n,n=a,a=s;return s=n,n=a,a=s^e.pbox[1],{left:n^=e.pbox[0],right:a}}(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},blockSize:2,keySize:4,ivSize:2});s.Blowfish=e._createHelper(l)}(),e.exports=s.Blowfish},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},16279:function(e,t,r){var s,n,a,i,o;s=r(98846),r(51398),n=s.lib.WordArray,i=(a=s.algo).SHA256,o=a.SHA224=i.extend({_doReset:function(){this._hash=new n.init([0xc1059ed8,0x367cd507,0x3070dd17,0xf70e5939,0xffc00b31,0x68581511,0x64f98fa7,0xbefa4fa4])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=4,e}}),s.SHA224=i._createHelper(o),s.HmacSHA224=i._createHmacHelper(o),e.exports=s.SHA224},18796:function(e,t,r){var s,n,a,i,o,l,c;a=(n=(s=r(98846)).lib).WordArray,i=n.Hasher,o=s.algo,l=[],c=o.SHA1=i.extend({_doReset:function(){this._hash=new a.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var r=this._hash.words,s=r[0],n=r[1],a=r[2],i=r[3],o=r[4],c=0;c<80;c++){if(c<16)l[c]=0|e[t+c];else{var d=l[c-3]^l[c-8]^l[c-14]^l[c-16];l[c]=d<<1|d>>>31}var h=(s<<5|s>>>27)+o+l[c];c<20?h+=(n&a|~n&i)+0x5a827999:c<40?h+=(n^a^i)+0x6ed9eba1:c<60?h+=(n&a|n&i|a&i)-0x70e44324:h+=(n^a^i)-0x359d3e2a,o=i,i=a,a=n<<30|n>>>2,n=s,s=h}r[0]=r[0]+s|0,r[1]=r[1]+n|0,r[2]=r[2]+a|0,r[3]=r[3]+i|0,r[4]=r[4]+o|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;return t[s>>>5]|=128<<24-s%32,t[(s+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(s+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),s.SHA1=i._createHelper(c),s.HmacSHA1=i._createHmacHelper(c),e.exports=s.SHA1},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22938:function(e,t,r){var s,n,a;e.exports=void(n=(s=r(98846)).lib.Base,a=s.enc.Utf8,s.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var r=e.blockSize,s=4*r;t.sigBytes>s&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),i=this._iKey=t.clone(),o=n.words,l=i.words,c=0;c<r;c++)o[c]^=0x5c5c5c5c,l[c]^=0x36363636;n.sigBytes=i.sigBytes=s,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}}))},23135:function(e,t,r){var s,n,a,i,o;a=(n=(s=r(98846)).lib).Base,i=n.WordArray,(o=s.x64={}).Word=a.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=a.extend({init:function(e,t){e=this.words=e||[],void 0!=t?this.sigBytes=t:this.sigBytes=8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],s=0;s<t;s++){var n=e[s];r.push(n.high),r.push(n.low)}return i.create(r,this.sigBytes)},clone:function(){for(var e=a.clone.call(this),t=e.words=this.words.slice(0),r=t.length,s=0;s<r;s++)t[s]=t[s].clone();return e}}),e.exports=s},24613:function(e,t,r){var s,n,a,i,o,l,c;s=r(98846),r(18796),r(22938),a=(n=s.lib).Base,i=n.WordArray,l=(o=s.algo).MD5,c=o.EvpKDF=a.extend({cfg:a.extend({keySize:4,hasher:l,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,s=this.cfg,n=s.hasher.create(),a=i.create(),o=a.words,l=s.keySize,c=s.iterations;o.length<l;){r&&n.update(r),r=n.update(e).finalize(t),n.reset();for(var d=1;d<c;d++)r=n.finalize(r),n.reset();a.concat(r)}return a.sigBytes=4*l,a}}),s.EvpKDF=function(e,t,r){return c.create(r).compute(e,t)},e.exports=s.EvpKDF},25353:function(e,t,r){e.exports=function(e){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,r=t.init;(t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,s=[],n=0;n<t;n++)s[n>>>2]|=e[n]<<24-n%4*8;r.call(this,s,t)}else r.apply(this,arguments)}).prototype=t}return e.lib.WordArray}(r(98846))},25408:function(e,t,r){var s,n,a,i,o,l,c,d;s=r(98846),r(51398),r(22938),a=(n=s.lib).Base,i=n.WordArray,l=(o=s.algo).SHA256,c=o.HMAC,d=o.PBKDF2=a.extend({cfg:a.extend({keySize:4,hasher:l,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,s=c.create(r.hasher,e),n=i.create(),a=i.create([1]),o=n.words,l=a.words,d=r.keySize,h=r.iterations;o.length<d;){var u=s.update(t).finalize(a);s.reset();for(var f=u.words,p=f.length,x=u,m=1;m<h;m++){x=s.finalize(x),s.reset();for(var g=x.words,b=0;b<p;b++)f[b]^=g[b]}n.concat(u),l[0]++}return n.sigBytes=4*d,n}}),s.PBKDF2=function(e,t,r){return d.create(r).compute(e,t)},e.exports=s.PBKDF2},27910:e=>{"use strict";e.exports=require("stream")},28165:function(e,t,r){var s,n;n=(s=r(98846)).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,s=this._map;e.clamp();for(var n=[],a=0;a<r;a+=3)for(var i=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,o=0;o<4&&a+.75*o<r;o++)n.push(s.charAt(i>>>6*(3-o)&63));var l=s.charAt(64);if(l)for(;n.length%4;)n.push(l);return n.join("")},parse:function(e){var t=e.length,r=this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var a=0;a<r.length;a++)s[r.charCodeAt(a)]=a}var i=r.charAt(64);if(i){var o=e.indexOf(i);-1!==o&&(t=o)}for(var l=e,c=t,d=s,h=[],u=0,f=0;f<c;f++)if(f%4){var p=d[l.charCodeAt(f-1)]<<f%4*2|d[l.charCodeAt(f)]>>>6-f%4*2;h[u>>>2]|=p<<24-u%4*8,u++}return n.create(h,u)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.exports=s.enc.Base64},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var s=r(31261),n=r.n(s)},30671:function(e,t,r){var s;s=r(98846),r(86348),s.mode.CTRGladman=function(){var e=s.lib.BlockCipherMode.extend();function t(e){if((e>>24&255)==255){var t=e>>16&255,r=e>>8&255,s=255&e;255===t?(t=0,255===r?(r=0,255===s?s=0:++s):++r):++t,e=0+(t<<16)+(r<<8)+s}else e+=0x1000000;return e}var r=e.Encryptor=e.extend({processBlock:function(e,r){var s,n=this._cipher,a=n.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0),0===((s=o)[0]=t(s[0]))&&(s[1]=t(s[1]));var l=o.slice(0);n.encryptBlock(l,0);for(var c=0;c<a;c++)e[r+c]^=l[c]}});return e.Decryptor=r,e}(),e.exports=s.mode.CTRGladman},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let s=r(37366),n=r(44953),a=r(46533),i=s._(r(1933));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},31769:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,I:()=>d});var s=r(60687),n=r(43210),a=r(85814),i=r.n(a),o=r(51108),l=r(53836);function c(){let{user:e}=(0,o.A)(),[t,r]=(0,n.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||t)return null;let{message:a,type:c,daysRemaining:d}=(0,l.Mo)(e);if(!a)return null;let h=()=>{switch(c){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,s.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(c){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:"error"===c?(0,s.jsx)("svg",{className:`h-5 w-5 ${h()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===c?(0,s.jsx)("svg",{className:`h-5 w-5 ${h()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{className:`h-5 w-5 ${h()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:a})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(i(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(c){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,s.jsx)("button",{onClick:()=>r(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,s.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function d(){let{user:e}=(0,o.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:t}=(0,l.Mo)(e);return t<=0?(0,s.jsx)(i(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,s.jsxs)(i(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${t<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[t," day",1===t?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36401:function(e,t,r){var s;s=r(98846),function(e){var t=s.lib,r=t.WordArray,n=t.Hasher,a=s.algo,i=r.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=r.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=r.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=r.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),d=r.create([0,0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xa953fd4e]),h=r.create([0x50a28be6,0x5c4dd124,0x6d703ef3,0x7a6d76e9,0]),u=a.RIPEMD160=n.extend({_doReset:function(){this._hash=r.create([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var r,s,n,a,u,p,x,m,g,b,y,w,_,v,k,S,A,R,I,C=0;C<16;C++){var P=t+C,$=e[P];e[P]=($<<8|$>>>24)&0xff00ff|($<<24|$>>>8)&0xff00ff00}var j=this._hash.words,E=d.words,M=h.words,O=i.words,N=o.words,B=l.words,T=c.words;v=g=j[0],k=b=j[1],S=y=j[2],A=w=j[3],R=_=j[4];for(var C=0;C<80;C+=1){I=g+e[t+O[C]]|0,C<16?I+=(b^y^w)+E[0]:C<32?I+=((r=b)&y|~r&w)+E[1]:C<48?I+=((b|~y)^w)+E[2]:C<64?I+=(s=b,n=y,(s&(a=w)|n&~a)+E[3]):I+=(b^(y|~w))+E[4],I|=0,I=(I=f(I,B[C]))+_|0,g=_,_=w,w=f(y,10),y=b,b=I,I=v+e[t+N[C]]|0,C<16?I+=(k^(S|~A))+M[0]:C<32?I+=(u=k,p=S,(u&(x=A)|p&~x)+M[1]):C<48?I+=((k|~S)^A)+M[2]:C<64?I+=((m=k)&S|~m&A)+M[3]:I+=(k^S^A)+M[4],I|=0,I=(I=f(I,T[C]))+R|0,v=R,R=A,A=f(S,10),S=k,k=I}I=j[1]+y+A|0,j[1]=j[2]+w+R|0,j[2]=j[3]+_+v|0,j[3]=j[4]+g+k|0,j[4]=j[0]+b+S|0,j[0]=I},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,s=8*e.sigBytes;t[s>>>5]|=128<<24-s%32,t[(s+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();for(var n=this._hash,a=n.words,i=0;i<5;i++){var o=a[i];a[i]=(o<<8|o>>>24)&0xff00ff|(o<<24|o>>>8)&0xff00ff00}return n},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function f(e,t){return e<<t|e>>>32-t}s.RIPEMD160=n._createHelper(u),s.HmacRIPEMD160=n._createHmacHelper(u)}(Math),e.exports=s.RIPEMD160},37085:function(e,t,r){var s;s=r(98846),r(86348),s.pad.Iso97971={pad:function(e,t){e.concat(s.lib.WordArray.create([0x80000000],1)),s.pad.ZeroPadding.pad(e,t)},unpad:function(e){s.pad.ZeroPadding.unpad(e),e.sigBytes--}},e.exports=s.pad.Iso97971},39914:function(e,t,r){var s,n,a;s=r(98846),r(86348),n=s.lib.CipherParams,a=s.enc.Hex,s.format.Hex={stringify:function(e){return e.ciphertext.toString(a)},parse:function(e){var t=a.parse(e);return n.create({ciphertext:t})}},e.exports=s.format.Hex},40465:function(e,t,r){var s;s=r(98846),function(e){for(var t=s.lib,r=t.WordArray,n=t.Hasher,a=s.algo,i=[],o=0;o<64;o++)i[o]=0x100000000*e.abs(e.sin(o+1))|0;var l=a.MD5=n.extend({_doReset:function(){this._hash=new r.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var s=t+r,n=e[s];e[s]=(n<<8|n>>>24)&0xff00ff|(n<<24|n>>>8)&0xff00ff00}var a=this._hash.words,o=e[t+0],l=e[t+1],f=e[t+2],p=e[t+3],x=e[t+4],m=e[t+5],g=e[t+6],b=e[t+7],y=e[t+8],w=e[t+9],_=e[t+10],v=e[t+11],k=e[t+12],S=e[t+13],A=e[t+14],R=e[t+15],I=a[0],C=a[1],P=a[2],$=a[3];I=c(I,C,P,$,o,7,i[0]),$=c($,I,C,P,l,12,i[1]),P=c(P,$,I,C,f,17,i[2]),C=c(C,P,$,I,p,22,i[3]),I=c(I,C,P,$,x,7,i[4]),$=c($,I,C,P,m,12,i[5]),P=c(P,$,I,C,g,17,i[6]),C=c(C,P,$,I,b,22,i[7]),I=c(I,C,P,$,y,7,i[8]),$=c($,I,C,P,w,12,i[9]),P=c(P,$,I,C,_,17,i[10]),C=c(C,P,$,I,v,22,i[11]),I=c(I,C,P,$,k,7,i[12]),$=c($,I,C,P,S,12,i[13]),P=c(P,$,I,C,A,17,i[14]),C=c(C,P,$,I,R,22,i[15]),I=d(I,C,P,$,l,5,i[16]),$=d($,I,C,P,g,9,i[17]),P=d(P,$,I,C,v,14,i[18]),C=d(C,P,$,I,o,20,i[19]),I=d(I,C,P,$,m,5,i[20]),$=d($,I,C,P,_,9,i[21]),P=d(P,$,I,C,R,14,i[22]),C=d(C,P,$,I,x,20,i[23]),I=d(I,C,P,$,w,5,i[24]),$=d($,I,C,P,A,9,i[25]),P=d(P,$,I,C,p,14,i[26]),C=d(C,P,$,I,y,20,i[27]),I=d(I,C,P,$,S,5,i[28]),$=d($,I,C,P,f,9,i[29]),P=d(P,$,I,C,b,14,i[30]),C=d(C,P,$,I,k,20,i[31]),I=h(I,C,P,$,m,4,i[32]),$=h($,I,C,P,y,11,i[33]),P=h(P,$,I,C,v,16,i[34]),C=h(C,P,$,I,A,23,i[35]),I=h(I,C,P,$,l,4,i[36]),$=h($,I,C,P,x,11,i[37]),P=h(P,$,I,C,b,16,i[38]),C=h(C,P,$,I,_,23,i[39]),I=h(I,C,P,$,S,4,i[40]),$=h($,I,C,P,o,11,i[41]),P=h(P,$,I,C,p,16,i[42]),C=h(C,P,$,I,g,23,i[43]),I=h(I,C,P,$,w,4,i[44]),$=h($,I,C,P,k,11,i[45]),P=h(P,$,I,C,R,16,i[46]),C=h(C,P,$,I,f,23,i[47]),I=u(I,C,P,$,o,6,i[48]),$=u($,I,C,P,b,10,i[49]),P=u(P,$,I,C,A,15,i[50]),C=u(C,P,$,I,m,21,i[51]),I=u(I,C,P,$,k,6,i[52]),$=u($,I,C,P,p,10,i[53]),P=u(P,$,I,C,_,15,i[54]),C=u(C,P,$,I,l,21,i[55]),I=u(I,C,P,$,y,6,i[56]),$=u($,I,C,P,R,10,i[57]),P=u(P,$,I,C,g,15,i[58]),C=u(C,P,$,I,S,21,i[59]),I=u(I,C,P,$,x,6,i[60]),$=u($,I,C,P,v,10,i[61]),P=u(P,$,I,C,f,15,i[62]),C=u(C,P,$,I,w,21,i[63]),a[0]=a[0]+I|0,a[1]=a[1]+C|0,a[2]=a[2]+P|0,a[3]=a[3]+$|0},_doFinalize:function(){var t=this._data,r=t.words,s=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var a=e.floor(s/0x100000000);r[(n+64>>>9<<4)+15]=(a<<8|a>>>24)&0xff00ff|(a<<24|a>>>8)&0xff00ff00,r[(n+64>>>9<<4)+14]=(s<<8|s>>>24)&0xff00ff|(s<<24|s>>>8)&0xff00ff00,t.sigBytes=(r.length+1)*4,this._process();for(var i=this._hash,o=i.words,l=0;l<4;l++){var c=o[l];o[l]=(c<<8|c>>>24)&0xff00ff|(c<<24|c>>>8)&0xff00ff00}return i},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,r,s,n,a,i){var o=e+(t&r|~t&s)+n+i;return(o<<a|o>>>32-a)+t}function d(e,t,r,s,n,a,i){var o=e+(t&s|r&~s)+n+i;return(o<<a|o>>>32-a)+t}function h(e,t,r,s,n,a,i){var o=e+(t^r^s)+n+i;return(o<<a|o>>>32-a)+t}function u(e,t,r,s,n,a,i){var o=e+(r^(t|~s))+n+i;return(o<<a|o>>>32-a)+t}s.MD5=n._createHelper(l),s.HmacMD5=n._createHmacHelper(l)}(Math),e.exports=s.MD5},44174:function(e,t,r){var s;s=r(98846),r(86348),s.pad.Iso10126={pad:function(e,t){var r=4*t,n=r-e.sigBytes%r;e.concat(s.lib.WordArray.random(n-1)).concat(s.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.exports=s.pad.Iso10126},45422:function(e,t,r){var s,n,a,i,o,l,c;s=r(98846),r(23135),r(9095),a=(n=s.x64).Word,i=n.WordArray,l=(o=s.algo).SHA512,c=o.SHA384=l.extend({_doReset:function(){this._hash=new i.init([new a.init(0xcbbb9d5d,0xc1059ed8),new a.init(0x629a292a,0x367cd507),new a.init(0x9159015a,0x3070dd17),new a.init(0x152fecd8,0xf70e5939),new a.init(0x67332667,0xffc00b31),new a.init(0x8eb44a87,0x68581511),new a.init(0xdb0c2e0d,0x64f98fa7),new a.init(0x47b5481d,0xbefa4fa4)])},_doFinalize:function(){var e=l._doFinalize.call(this);return e.sigBytes-=16,e}}),s.SHA384=l._createHelper(c),s.HmacSHA384=l._createHmacHelper(c),e.exports=s.SHA384},50584:function(e,t,r){var s,n;n=(s=r(98846)).lib.WordArray,s.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var r=e.words,s=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var a=[],i=0;i<s;i+=3)for(var o=(r[i>>>2]>>>24-i%4*8&255)<<16|(r[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|r[i+2>>>2]>>>24-(i+2)%4*8&255,l=0;l<4&&i+.75*l<s;l++)a.push(n.charAt(o>>>6*(3-l)&63));var c=n.charAt(64);if(c)for(;a.length%4;)a.push(c);return a.join("")},parse:function(e,t){void 0===t&&(t=!0);var r=e.length,s=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<s.length;i++)a[s.charCodeAt(i)]=i}var o=s.charAt(64);if(o){var l=e.indexOf(o);-1!==l&&(r=l)}for(var c=e,d=r,h=a,u=[],f=0,p=0;p<d;p++)if(p%4){var x=h[c.charCodeAt(p-1)]<<p%4*2|h[c.charCodeAt(p)]>>>6-p%4*2;u[f>>>2]|=x<<24-f%4*8,f++}return n.create(u,f)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},e.exports=s.enc.Base64url},51150:function(e,t,r){var s;s=r(98846),r(86348),s.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,s=4*t,n=s-r%s,a=r+n-1;e.clamp(),e.words[a>>>2]|=n<<24-a%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.exports=s.pad.Ansix923},51398:function(e,t,r){var s;s=r(98846),function(e){var t=s.lib,r=t.WordArray,n=t.Hasher,a=s.algo,i=[],o=[];function l(e){return(e-(0|e))*0x100000000|0}for(var c=2,d=0;d<64;)(function(t){for(var r=e.sqrt(t),s=2;s<=r;s++)if(!(t%s))return!1;return!0})(c)&&(d<8&&(i[d]=l(e.pow(c,.5))),o[d]=l(e.pow(c,1/3)),d++),c++;var h=[],u=a.SHA256=n.extend({_doReset:function(){this._hash=new r.init(i.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,s=r[0],n=r[1],a=r[2],i=r[3],l=r[4],c=r[5],d=r[6],u=r[7],f=0;f<64;f++){if(f<16)h[f]=0|e[t+f];else{var p=h[f-15],x=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,m=h[f-2],g=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;h[f]=x+h[f-7]+g+h[f-16]}var b=l&c^~l&d,y=s&n^s&a^n&a,w=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),_=u+((l<<26|l>>>6)^(l<<21|l>>>11)^(l<<7|l>>>25))+b+o[f]+h[f],v=w+y;u=d,d=c,c=l,l=i+_|0,i=a,a=n,n=s,s=_+v|0}r[0]=r[0]+s|0,r[1]=r[1]+n|0,r[2]=r[2]+a|0,r[3]=r[3]+i|0,r[4]=r[4]+l|0,r[5]=r[5]+c|0,r[6]=r[6]+d|0,r[7]=r[7]+u|0},_doFinalize:function(){var t=this._data,r=t.words,s=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[(n+64>>>9<<4)+14]=e.floor(s/0x100000000),r[(n+64>>>9<<4)+15]=s,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});s.SHA256=n._createHelper(u),s.HmacSHA256=n._createHmacHelper(u)}(Math),e.exports=s.SHA256},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65028:function(e,t,r){var s,n,a;s=r(98846),r(86348),s.mode.CTR=(a=(n=s.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,n=this._iv,a=this._counter;n&&(a=this._counter=n.slice(0),this._iv=void 0);var i=a.slice(0);r.encryptBlock(i,0),a[s-1]=a[s-1]+1|0;for(var o=0;o<s;o++)e[t+o]^=i[o]}}),n.Decryptor=a,n),e.exports=s.mode.CTR},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74920:(e,t,r)=>{Promise.resolve().then(r.bind(r,74959))},74959:(e,t,r)=>{"use strict";let s,n,a,i,o,l;r.r(t),r.d(t,{default:()=>aT});var c,d,h,u,f,p,x,m,g,b,y,w,_,v,k,S,A,R,I,C,P,$,j,E,M,O,N,B,T,L,D,W,U,H,q,F,z,K,X,J,V,G,Q,Y,Z,ee,et,er,es,en,ea,ei,eo,el,ec,ed,eh,eu,ef,ep,ex,em,eg,eb,ey,ew,e_,ev,ek,eS,eA,eR,eI,eC,eP,e$,ej,eE,eM,eO,eN,eB,eT,eL,eD,eW,eU,eH,eq,eF,ez,eK,eX,eJ,eV,eG,eQ,eY,eZ,e0,e1,e2,e4,e8,e6,e3,e5,e9,e7,te,tt,tr,ts,tn,ta,ti,to,tl,tc,td=r(60687),th=r(43210),tu=r(51108),tf=r(16189),tp=r(5481),tx=r(75535),tm=r(56304);function tg(e,t,r,s,n){if("m"===s)throw TypeError("Private method is not writable");if("a"===s&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?n.call(e,r):n?n.value=r:t.set(e,r),r}function tb(e,t,r,s){if("a"===r&&!s)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?s:"a"===r?s.call(e):s?s.value:t.get(e)}let ty=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return ty=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),r=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^r()&15>>e/4).toString(16))};function tw(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let t_=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class tv extends Error{}class tk extends tv{constructor(e,t,r,s){super(`${tk.makeMessage(e,t,r)}`),this.status=e,this.headers=s,this.requestID=s?.get("request-id"),this.error=t}static makeMessage(e,t,r){let s=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,t,r,s){return e&&s?400===e?new tI(e,t,r,s):401===e?new tC(e,t,r,s):403===e?new tP(e,t,r,s):404===e?new t$(e,t,r,s):409===e?new tj(e,t,r,s):422===e?new tE(e,t,r,s):429===e?new tM(e,t,r,s):e>=500?new tO(e,t,r,s):new tk(e,t,r,s):new tA({message:r,cause:t_(t)})}}class tS extends tk{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class tA extends tk{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class tR extends tA{constructor({message:e}={}){super({message:e??"Request timed out."})}}class tI extends tk{}class tC extends tk{}class tP extends tk{}class t$ extends tk{}class tj extends tk{}class tE extends tk{}class tM extends tk{}class tO extends tk{}let tN=/^[a-z][a-z0-9+.-]*:/i,tB=e=>tN.test(e);function tT(e){return"object"!=typeof e?{}:e??{}}let tL=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new tv(`${e} must be an integer`);if(t<0)throw new tv(`${e} must be a positive integer`);return t},tD=e=>{try{return JSON.parse(e)}catch(e){return}},tW=e=>new Promise(t=>setTimeout(t,e)),tU={off:0,error:200,warn:300,info:400,debug:500},tH=(e,t,r)=>{if(e){if(Object.prototype.hasOwnProperty.call(tU,e))return e;tX(r).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(tU))}`)}};function tq(){}function tF(e,t,r){return!t||tU[e]>tU[r]?tq:t[e].bind(t)}let tz={error:tq,warn:tq,info:tq,debug:tq},tK=new WeakMap;function tX(e){let t=e.logger,r=e.logLevel??"off";if(!t)return tz;let s=tK.get(t);if(s&&s[0]===r)return s[1];let n={error:tF("error",t,r),warn:tF("warn",t,r),info:tF("info",t,r),debug:tF("debug",t,r)};return tK.set(t,[r,n]),n}let tJ=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"x-api-key"===e.toLowerCase()||"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),tV="0.52.0",tG=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,tQ=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":tZ(Deno.build.os),"X-Stainless-Arch":tY(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":tZ(globalThis.process.platform),"X-Stainless-Arch":tY(globalThis.process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,s=r[2]||0,n=r[3]||0;return{browser:e,version:`${t}.${s}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tV,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},tY=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tZ=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",t0=()=>s??(s=tQ());function t1(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function t2(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return t1({start(){},async pull(e){let{done:r,value:s}=await t.next();r?e.close():e.enqueue(s)},async cancel(){await t.return?.()}})}function t4(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function t8(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),r=t.cancel();t.releaseLock(),await r}let t6=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)});function t3(e){let t;return(n??(n=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function t5(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class t9{constructor(){c.set(this,void 0),d.set(this,void 0),tg(this,c,new Uint8Array,"f"),tg(this,d,null,"f")}decode(e){let t;if(null==e)return[];let r=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?t3(e):e;tg(this,c,function(e){let t=0;for(let r of e)t+=r.length;let r=new Uint8Array(t),s=0;for(let t of e)r.set(t,s),s+=t.length;return r}([tb(this,c,"f"),r]),"f");let s=[];for(;null!=(t=function(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}(tb(this,c,"f"),tb(this,d,"f")));){if(t.carriage&&null==tb(this,d,"f")){tg(this,d,t.index,"f");continue}if(null!=tb(this,d,"f")&&(t.index!==tb(this,d,"f")+1||t.carriage)){s.push(t5(tb(this,c,"f").subarray(0,tb(this,d,"f")-1))),tg(this,c,tb(this,c,"f").subarray(tb(this,d,"f")),"f"),tg(this,d,null,"f");continue}let e=null!==tb(this,d,"f")?t.preceding-1:t.preceding,r=t5(tb(this,c,"f").subarray(0,e));s.push(r),tg(this,c,tb(this,c,"f").subarray(t.index),"f"),tg(this,d,null,"f")}return s}flush(){return tb(this,c,"f").length?this.decode("\n"):[]}}c=new WeakMap,d=new WeakMap,t9.NEWLINE_CHARS=new Set(["\n","\r"]),t9.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class t7{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;async function*s(){if(r)throw new tv("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let s=!1;try{for await(let r of re(e,t)){if("completion"===r.event)try{yield JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("message_start"===r.event||"message_delta"===r.event||"message_stop"===r.event||"content_block_start"===r.event||"content_block_delta"===r.event||"content_block_stop"===r.event)try{yield JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("ping"!==r.event&&"error"===r.event)throw new tk(void 0,tD(r.data)??r.data,void 0,e.headers)}s=!0}catch(e){if(tw(e))return;throw e}finally{s||t.abort()}}return new t7(s,t)}static fromReadableStream(e,t){let r=!1;async function*s(){let t=new t9;for await(let r of t4(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new t7(async function*(){if(r)throw new tv("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of s())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(tw(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),s=s=>({next:()=>{if(0===s.length){let s=r.next();e.push(s),t.push(s)}return s.shift()}});return[new t7(()=>s(e),this.controller),new t7(()=>s(t),this.controller)]}toReadableStream(){let e,t=this;return t1({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:r,done:s}=await e.next();if(s)return t.close();let n=t3(JSON.stringify(r)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*re(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new tv("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new tv("Attempted to iterate over a response with no body")}let r=new rr,s=new t9;for await(let t of rt(t4(e.body)))for(let e of s.decode(t)){let t=r.decode(e);t&&(yield t)}for(let e of s.flush()){let t=r.decode(e);t&&(yield t)}}async function*rt(e){let t=new Uint8Array;for await(let r of e){let e;if(null==r)continue;let s=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?t3(r):r,n=new Uint8Array(t.length+s.length);for(n.set(t),n.set(s,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class rr{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,s]=function(e,t){let r=e.indexOf(":");return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return s.startsWith(" ")&&(s=s.substring(1)),"event"===t?this.event=s:"data"===t&&this.data.push(s),null}}async function rs(e,t){let{response:r,requestLogID:s,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(tX(e).debug("response",r.status,r.url,r.headers,r.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(r,t.controller):t7.fromSSEResponse(r,t.controller);if(204===r.status)return null;if(t.options.__binaryResponse)return r;let s=r.headers.get("content-type"),n=s?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?rn(await r.json(),r):await r.text()})();return tX(e).debug(`[${s}] response parsed`,tJ({retryOfRequestLogID:n,url:r.url,status:r.status,body:i,durationMs:Date.now()-a})),i}function rn(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("request-id"),enumerable:!1})}class ra extends Promise{constructor(e,t,r=rs){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=r,h.set(this,void 0),tg(this,h,e,"f")}_thenUnwrap(e){return new ra(tb(this,h,"f"),this.responsePromise,async(t,r)=>rn(e(await this.parseResponse(t,r),r),r.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(tb(this,h,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}h=new WeakMap;class ri{constructor(e,t,r,s){u.set(this,void 0),tg(this,u,e,"f"),this.options=s,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new tv("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await tb(this,u,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(u=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class ro extends ra{constructor(e,t,r){super(e,t,async(e,t)=>new r(e,t.response,await rs(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class rl extends ri{constructor(e,t,r,s){super(e,t,r,s),this.data=r.data||[],this.has_more=r.has_more||!1,this.first_id=r.first_id||null,this.last_id=r.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){if(this.options.query?.before_id){let e=this.first_id;return e?{...this.options,query:{...tT(this.options.query),before_id:e}}:null}let e=this.last_id;return e?{...this.options,query:{...tT(this.options.query),after_id:e}}:null}}let rc=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function rd(e,t,r){return rc(),new File(e,t??"unknown_file",r)}function rh(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let ru=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],rf=async(e,t)=>({...e,body:await rx(e.body,t)}),rp=new WeakMap,rx=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,r=rp.get(t);if(r)return r;let s=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,r=new FormData;if(r.toString()===await new e(r).text())return!1;return!0}catch{return!0}})();return rp.set(t,s),s}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let r=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>ry(r,e,t))),r},rm=e=>e instanceof Blob&&"name"in e,rg=e=>"object"==typeof e&&null!==e&&(e instanceof Response||ru(e)||rm(e)),rb=e=>{if(rg(e))return!0;if(Array.isArray(e))return e.some(rb);if(e&&"object"==typeof e){for(let t in e)if(rb(e[t]))return!0}return!1},ry=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(r instanceof Response){let s={},n=r.headers.get("Content-Type");n&&(s={type:n}),e.append(t,rd([await r.blob()],rh(r),s))}else if(ru(r))e.append(t,rd([await new Response(t2(r)).blob()],rh(r)));else if(rm(r))e.append(t,rd([r],rh(r),{type:r.type}));else if(Array.isArray(r))await Promise.all(r.map(r=>ry(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,s])=>ry(e,`${t}[${r}]`,s)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}},rw=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,r_=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&rw(e),rv=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function rk(e,t,r){if(rc(),e=await e,t||(t=rh(e)),r_(e))return e instanceof File&&null==t&&null==r?e:rd([await e.arrayBuffer()],t??e.name,{type:e.type,lastModified:e.lastModified,...r});if(rv(e)){let s=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),rd(await rS(s),t,r)}let s=await rS(e);if(!r?.type){let e=s.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(r={...r,type:e})}return rd(s,t,r)}async function rS(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(rw(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(ru(e))for await(let r of e)t.push(...await rS(r));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class rA{constructor(e){this._client=e}}let rR=Symbol.for("brand.privateNullableHeaders"),rI=Array.isArray,rC=e=>{let t=new Headers,r=new Set;for(let s of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(rR in e){let{values:t,nulls:r}=e;for(let e of(yield*t.entries(),r))yield[e,null];return}let r=!1;for(let s of(e instanceof Headers?t=e.entries():rI(e)?t=e:(r=!0,t=Object.entries(e??{})),t)){let e=s[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=rI(s[1])?s[1]:[s[1]],n=!1;for(let s of t)void 0!==s&&(r&&!n&&(n=!0,yield[e,null]),yield[e,s])}}(s)){let s=n.toLowerCase();e.has(s)||(t.delete(n),e.add(s)),null===a?(t.delete(n),r.add(s)):(t.append(n,a),r.delete(s))}}return{[rR]:!0,values:t,nulls:r}};function rP(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let r$=((e=rP)=>function(t,...r){let s;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,s,a)=>(/[?#]/.test(s)&&(n=!0),t+s+(a===r.length?"":(n?encodeURIComponent:e)(String(r[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(s=l.exec(i));)o.push({start:s.index,length:s[0].length});if(o.length>0){let e=0,t=o.reduce((t,r)=>{let s=" ".repeat(r.start-e),n="^".repeat(r.length);return e=r.start+r.length,t+s+n},"");throw new tv(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(rP);class rj extends rA{list(e={},t){let{betas:r,...s}=e??{};return this._client.getAPIList("/v1/files",rl,{query:s,...t,headers:rC([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},t?.headers])})}delete(e,t={},r){let{betas:s}=t??{};return this._client.delete(r$`/v1/files/${e}`,{...r,headers:rC([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},r?.headers])})}download(e,t={},r){let{betas:s}=t??{};return this._client.get(r$`/v1/files/${e}/content`,{...r,headers:rC([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},r?.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},r){let{betas:s}=t??{};return this._client.get(r$`/v1/files/${e}`,{...r,headers:rC([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},r?.headers])})}upload(e,t){let{betas:r,...s}=e;return this._client.post("/v1/files",rf({body:s,...t,headers:rC([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},t?.headers])},this._client))}}class rE extends rA{retrieve(e,t={},r){let{betas:s}=t??{};return this._client.get(r$`/v1/models/${e}?beta=true`,{...r,headers:rC([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},r?.headers])})}list(e={},t){let{betas:r,...s}=e??{};return this._client.getAPIList("/v1/models?beta=true",rl,{query:s,...t,headers:rC([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},t?.headers])})}}class rM{constructor(e,t){this.iterator=e,this.controller=t}async *decoder(){let e=new t9;for await(let t of this.iterator)for(let r of e.decode(t))yield JSON.parse(r);for(let t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new tv("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new tv("Attempted to iterate over a response with no body")}return new rM(t4(e.body),t)}}class rO extends rA{create(e,t){let{betas:r,...s}=e;return this._client.post("/v1/messages/batches?beta=true",{body:s,...t,headers:rC([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},t?.headers])})}retrieve(e,t={},r){let{betas:s}=t??{};return this._client.get(r$`/v1/messages/batches/${e}?beta=true`,{...r,headers:rC([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},r?.headers])})}list(e={},t){let{betas:r,...s}=e??{};return this._client.getAPIList("/v1/messages/batches?beta=true",rl,{query:s,...t,headers:rC([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},t?.headers])})}delete(e,t={},r){let{betas:s}=t??{};return this._client.delete(r$`/v1/messages/batches/${e}?beta=true`,{...r,headers:rC([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},r?.headers])})}cancel(e,t={},r){let{betas:s}=t??{};return this._client.post(r$`/v1/messages/batches/${e}/cancel?beta=true`,{...r,headers:rC([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},r?.headers])})}async results(e,t={},r){let s=await this.retrieve(e);if(!s.results_url)throw new tv(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);let{betas:n}=t??{};return this._client.get(s.results_url,{...r,headers:rC([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},r?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>rM.fromResponse(t.response,t.controller))}}let rN=e=>{let t=0,r=[];for(;t<e.length;){let s=e[t];if("\\"===s){t++;continue}if("{"===s){r.push({type:"brace",value:"{"}),t++;continue}if("}"===s){r.push({type:"brace",value:"}"}),t++;continue}if("["===s){r.push({type:"paren",value:"["}),t++;continue}if("]"===s){r.push({type:"paren",value:"]"}),t++;continue}if(":"===s){r.push({type:"separator",value:":"}),t++;continue}if(","===s){r.push({type:"delimiter",value:","}),t++;continue}if('"'===s){let n="",a=!1;for(s=e[++t];'"'!==s;){if(t===e.length){a=!0;break}if("\\"===s){if(++t===e.length){a=!0;break}n+=s+e[t],s=e[++t]}else n+=s,s=e[++t]}s=e[++t],a||r.push({type:"string",value:n});continue}let n=/\s/;if(s&&n.test(s)){t++;continue}let a=/[0-9]/;if(s&&a.test(s)||"-"===s||"."===s){let n="";for("-"===s&&(n+=s,s=e[++t]);s&&a.test(s)||"."===s;)n+=s,s=e[++t];r.push({type:"number",value:n});continue}let i=/[a-z]/i;if(s&&i.test(s)){let n="";for(;s&&i.test(s)&&t!==e.length;)n+=s,s=e[++t];"true"==n||"false"==n||"null"===n?r.push({type:"name",value:n}):t++;continue}t++}return r},rB=e=>{if(0===e.length)return e;let t=e[e.length-1];switch(t.type){case"separator":return rB(e=e.slice(0,e.length-1));case"number":let r=t.value[t.value.length-1];if("."===r||"-"===r)return rB(e=e.slice(0,e.length-1));case"string":let s=e[e.length-2];if(s?.type==="delimiter"||s?.type==="brace"&&"{"===s.value)return rB(e=e.slice(0,e.length-1));break;case"delimiter":return rB(e=e.slice(0,e.length-1))}return e},rT=e=>{let t=[];return e.map(e=>{"brace"===e.type&&("{"===e.value?t.push("}"):t.splice(t.lastIndexOf("}"),1)),"paren"===e.type&&("["===e.value?t.push("]"):t.splice(t.lastIndexOf("]"),1))}),t.length>0&&t.reverse().map(t=>{"}"===t?e.push({type:"brace",value:"}"}):"]"===t&&e.push({type:"paren",value:"]"})}),e},rL=e=>{let t="";return e.map(e=>{"string"===e.type?t+='"'+e.value+'"':t+=e.value}),t},rD=e=>JSON.parse(rL(rT(rB(rN(e))))),rW="__json_buf";class rU{constructor(){f.add(this),this.messages=[],this.receivedMessages=[],p.set(this,void 0),this.controller=new AbortController,x.set(this,void 0),m.set(this,()=>{}),g.set(this,()=>{}),b.set(this,void 0),y.set(this,()=>{}),w.set(this,()=>{}),_.set(this,{}),v.set(this,!1),k.set(this,!1),S.set(this,!1),A.set(this,!1),R.set(this,void 0),I.set(this,void 0),$.set(this,e=>{if(tg(this,k,!0,"f"),tw(e)&&(e=new tS),e instanceof tS)return tg(this,S,!0,"f"),this._emit("abort",e);if(e instanceof tv)return this._emit("error",e);if(e instanceof Error){let t=new tv(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new tv(String(e)))}),tg(this,x,new Promise((e,t)=>{tg(this,m,e,"f"),tg(this,g,t,"f")}),"f"),tg(this,b,new Promise((e,t)=>{tg(this,y,e,"f"),tg(this,w,t,"f")}),"f"),tb(this,x,"f").catch(()=>{}),tb(this,b,"f").catch(()=>{})}get response(){return tb(this,R,"f")}get request_id(){return tb(this,I,"f")}async withResponse(){let e=await tb(this,x,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new rU;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,r){let s=new rU;for(let e of t.messages)s._addMessageParam(e);return s._run(()=>s._createMessage(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},tb(this,$,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),tb(this,f,"m",j).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...r,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))tb(this,f,"m",E).call(this,e);if(a.controller.signal?.aborted)throw new tS;tb(this,f,"m",M).call(this)}_connected(e){this.ended||(tg(this,R,e,"f"),tg(this,I,e?.headers.get("request-id"),"f"),tb(this,m,"f").call(this,e),this._emit("connect"))}get ended(){return tb(this,v,"f")}get errored(){return tb(this,k,"f")}get aborted(){return tb(this,S,"f")}abort(){this.controller.abort()}on(e,t){return(tb(this,_,"f")[e]||(tb(this,_,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=tb(this,_,"f")[e];if(!r)return this;let s=r.findIndex(e=>e.listener===t);return s>=0&&r.splice(s,1),this}once(e,t){return(tb(this,_,"f")[e]||(tb(this,_,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{tg(this,A,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){tg(this,A,!0,"f"),await tb(this,b,"f")}get currentMessage(){return tb(this,p,"f")}async finalMessage(){return await this.done(),tb(this,f,"m",C).call(this)}async finalText(){return await this.done(),tb(this,f,"m",P).call(this)}_emit(e,...t){if(tb(this,v,"f"))return;"end"===e&&(tg(this,v,!0,"f"),tb(this,y,"f").call(this));let r=tb(this,_,"f")[e];if(r&&(tb(this,_,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];tb(this,A,"f")||r?.length||Promise.reject(e),tb(this,g,"f").call(this,e),tb(this,w,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];tb(this,A,"f")||r?.length||Promise.reject(e),tb(this,g,"f").call(this,e),tb(this,w,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",tb(this,f,"m",C).call(this))}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),tb(this,f,"m",j).call(this),this._connected(null);let s=t7.fromReadableStream(e,this.controller);for await(let e of s)tb(this,f,"m",E).call(this,e);if(s.controller.signal?.aborted)throw new tS;tb(this,f,"m",M).call(this)}[(p=new WeakMap,x=new WeakMap,m=new WeakMap,g=new WeakMap,b=new WeakMap,y=new WeakMap,w=new WeakMap,_=new WeakMap,v=new WeakMap,k=new WeakMap,S=new WeakMap,A=new WeakMap,R=new WeakMap,I=new WeakMap,$=new WeakMap,f=new WeakSet,C=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},P=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new tv("stream ended without producing a content block with type=text");return e.join(" ")},j=function(){this.ended||tg(this,p,void 0,"f")},E=function(e){if(this.ended)return;let t=tb(this,f,"m",O).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let r=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===r.type&&this._emit("text",e.delta.text,r.text||"");break;case"citations_delta":"text"===r.type&&this._emit("citation",e.delta.citation,r.citations??[]);break;case"input_json_delta":("tool_use"===r.type||"mcp_tool_use"===r.type)&&r.input&&this._emit("inputJson",e.delta.partial_json,r.input);break;case"thinking_delta":"thinking"===r.type&&this._emit("thinking",e.delta.thinking,r.thinking);break;case"signature_delta":"thinking"===r.type&&this._emit("signature",r.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":tg(this,p,t,"f")}},M=function(){if(this.ended)throw new tv("stream has ended, this shouldn't happen");let e=tb(this,p,"f");if(!e)throw new tv("request ended without sending any chunks");return tg(this,p,void 0,"f"),e},O=function(e){let t=tb(this,p,"f");if("message_start"===e.type){if(t)throw new tv(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new tv(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.container=e.delta.container,t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let r=t.content.at(e.index);switch(e.delta.type){case"text_delta":r?.type==="text"&&(r.text+=e.delta.text);break;case"citations_delta":r?.type==="text"&&(r.citations??(r.citations=[]),r.citations.push(e.delta.citation));break;case"input_json_delta":if(r?.type==="tool_use"||r?.type==="mcp_tool_use"){let t=r[rW]||"";Object.defineProperty(r,rW,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(r.input=rD(t))}break;case"thinking_delta":r?.type==="thinking"&&(r.thinking+=e.delta.thinking);break;case"signature_delta":r?.type==="thinking"&&(r.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("streamEvent",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new t7(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}let rH={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},rq={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};class rF extends rA{constructor(){super(...arguments),this.batches=new rO(this._client)}create(e,t){let{betas:r,...s}=e;s.model in rq&&console.warn(`The model '${s.model}' is deprecated and will reach end-of-life on ${rq[s.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let n=this._client._options.timeout;if(!s.stream&&null==n){let e=rH[s.model]??void 0;n=this._client.calculateNonstreamingTimeout(s.max_tokens,e)}return this._client.post("/v1/messages?beta=true",{body:s,timeout:n??6e5,...t,headers:rC([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}stream(e,t){return rU.createMessage(this,e,t)}countTokens(e,t){let{betas:r,...s}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:s,...t,headers:rC([{"anthropic-beta":[...r??[],"token-counting-2024-11-01"].toString()},t?.headers])})}}rF.Batches=rO;class rz extends rA{constructor(){super(...arguments),this.models=new rE(this._client),this.messages=new rF(this._client),this.files=new rj(this._client)}}rz.Models=rE,rz.Messages=rF,rz.Files=rj;class rK extends rA{create(e,t){let{betas:r,...s}=e;return this._client.post("/v1/complete",{body:s,timeout:this._client._options.timeout??6e5,...t,headers:rC([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}}let rX="__json_buf";class rJ{constructor(){N.add(this),this.messages=[],this.receivedMessages=[],B.set(this,void 0),this.controller=new AbortController,T.set(this,void 0),L.set(this,()=>{}),D.set(this,()=>{}),W.set(this,void 0),U.set(this,()=>{}),H.set(this,()=>{}),q.set(this,{}),F.set(this,!1),z.set(this,!1),K.set(this,!1),X.set(this,!1),J.set(this,void 0),V.set(this,void 0),Y.set(this,e=>{if(tg(this,z,!0,"f"),tw(e)&&(e=new tS),e instanceof tS)return tg(this,K,!0,"f"),this._emit("abort",e);if(e instanceof tv)return this._emit("error",e);if(e instanceof Error){let t=new tv(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new tv(String(e)))}),tg(this,T,new Promise((e,t)=>{tg(this,L,e,"f"),tg(this,D,t,"f")}),"f"),tg(this,W,new Promise((e,t)=>{tg(this,U,e,"f"),tg(this,H,t,"f")}),"f"),tb(this,T,"f").catch(()=>{}),tb(this,W,"f").catch(()=>{})}get response(){return tb(this,J,"f")}get request_id(){return tb(this,V,"f")}async withResponse(){let e=await tb(this,T,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new rJ;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,r){let s=new rJ;for(let e of t.messages)s._addMessageParam(e);return s._run(()=>s._createMessage(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},tb(this,Y,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),tb(this,N,"m",Z).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...r,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))tb(this,N,"m",ee).call(this,e);if(a.controller.signal?.aborted)throw new tS;tb(this,N,"m",et).call(this)}_connected(e){this.ended||(tg(this,J,e,"f"),tg(this,V,e?.headers.get("request-id"),"f"),tb(this,L,"f").call(this,e),this._emit("connect"))}get ended(){return tb(this,F,"f")}get errored(){return tb(this,z,"f")}get aborted(){return tb(this,K,"f")}abort(){this.controller.abort()}on(e,t){return(tb(this,q,"f")[e]||(tb(this,q,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=tb(this,q,"f")[e];if(!r)return this;let s=r.findIndex(e=>e.listener===t);return s>=0&&r.splice(s,1),this}once(e,t){return(tb(this,q,"f")[e]||(tb(this,q,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{tg(this,X,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){tg(this,X,!0,"f"),await tb(this,W,"f")}get currentMessage(){return tb(this,B,"f")}async finalMessage(){return await this.done(),tb(this,N,"m",G).call(this)}async finalText(){return await this.done(),tb(this,N,"m",Q).call(this)}_emit(e,...t){if(tb(this,F,"f"))return;"end"===e&&(tg(this,F,!0,"f"),tb(this,U,"f").call(this));let r=tb(this,q,"f")[e];if(r&&(tb(this,q,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];tb(this,X,"f")||r?.length||Promise.reject(e),tb(this,D,"f").call(this,e),tb(this,H,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];tb(this,X,"f")||r?.length||Promise.reject(e),tb(this,D,"f").call(this,e),tb(this,H,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",tb(this,N,"m",G).call(this))}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),tb(this,N,"m",Z).call(this),this._connected(null);let s=t7.fromReadableStream(e,this.controller);for await(let e of s)tb(this,N,"m",ee).call(this,e);if(s.controller.signal?.aborted)throw new tS;tb(this,N,"m",et).call(this)}[(B=new WeakMap,T=new WeakMap,L=new WeakMap,D=new WeakMap,W=new WeakMap,U=new WeakMap,H=new WeakMap,q=new WeakMap,F=new WeakMap,z=new WeakMap,K=new WeakMap,X=new WeakMap,J=new WeakMap,V=new WeakMap,Y=new WeakMap,N=new WeakSet,G=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},Q=function(){if(0===this.receivedMessages.length)throw new tv("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new tv("stream ended without producing a content block with type=text");return e.join(" ")},Z=function(){this.ended||tg(this,B,void 0,"f")},ee=function(e){if(this.ended)return;let t=tb(this,N,"m",er).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let r=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===r.type&&this._emit("text",e.delta.text,r.text||"");break;case"citations_delta":"text"===r.type&&this._emit("citation",e.delta.citation,r.citations??[]);break;case"input_json_delta":"tool_use"===r.type&&r.input&&this._emit("inputJson",e.delta.partial_json,r.input);break;case"thinking_delta":"thinking"===r.type&&this._emit("thinking",e.delta.thinking,r.thinking);break;case"signature_delta":"thinking"===r.type&&this._emit("signature",r.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":tg(this,B,t,"f")}},et=function(){if(this.ended)throw new tv("stream has ended, this shouldn't happen");let e=tb(this,B,"f");if(!e)throw new tv("request ended without sending any chunks");return tg(this,B,void 0,"f"),e},er=function(e){let t=tb(this,B,"f");if("message_start"===e.type){if(t)throw new tv(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new tv(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let r=t.content.at(e.index);switch(e.delta.type){case"text_delta":r?.type==="text"&&(r.text+=e.delta.text);break;case"citations_delta":r?.type==="text"&&(r.citations??(r.citations=[]),r.citations.push(e.delta.citation));break;case"input_json_delta":if(r?.type==="tool_use"){let t=r[rX]||"";Object.defineProperty(r,rX,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(r.input=rD(t))}break;case"thinking_delta":r?.type==="thinking"&&(r.thinking+=e.delta.thinking);break;case"signature_delta":r?.type==="thinking"&&(r.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("streamEvent",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new t7(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class rV extends rA{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(r$`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",rl,{query:e,...t})}delete(e,t){return this._client.delete(r$`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(r$`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){let r=await this.retrieve(e);if(!r.results_url)throw new tv(`No batch \`results_url\`; Has it finished processing? ${r.processing_status} - ${r.id}`);return this._client.get(r.results_url,{...t,headers:rC([{Accept:"application/binary"},t?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>rM.fromResponse(t.response,t.controller))}}class rG extends rA{constructor(){super(...arguments),this.batches=new rV(this._client)}create(e,t){e.model in rQ&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${rQ[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let r=this._client._options.timeout;if(!e.stream&&null==r){let t=rH[e.model]??void 0;r=this._client.calculateNonstreamingTimeout(e.max_tokens,t)}return this._client.post("/v1/messages",{body:e,timeout:r??6e5,...t,stream:e.stream??!1})}stream(e,t){return rJ.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}}let rQ={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};rG.Batches=rV;class rY extends rA{retrieve(e,t={},r){let{betas:s}=t??{};return this._client.get(r$`/v1/models/${e}`,{...r,headers:rC([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},r?.headers])})}list(e={},t){let{betas:r,...s}=e??{};return this._client.getAPIList("/v1/models",rl,{query:s,...t,headers:rC([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},t?.headers])})}}let rZ=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class r0{constructor({baseURL:e=rZ("ANTHROPIC_BASE_URL"),apiKey:t=rZ("ANTHROPIC_API_KEY")??null,authToken:r=rZ("ANTHROPIC_AUTH_TOKEN")??null,...s}={}){es.set(this,void 0);let n={apiKey:t,authToken:r,...s,baseURL:e||"https://api.anthropic.com"};if(!n.dangerouslyAllowBrowser&&tG())throw new tv("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\n");this.baseURL=n.baseURL,this.timeout=n.timeout??r1.DEFAULT_TIMEOUT,this.logger=n.logger??console;let a="warn";this.logLevel=a,this.logLevel=tH(n.logLevel,"ClientOptions.logLevel",this)??tH(rZ("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??a,this.fetchOptions=n.fetchOptions,this.maxRetries=n.maxRetries??2,this.fetch=n.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),tg(this,es,t6,"f"),this._options=n,this.apiKey=t,this.authToken=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key")||t.has("x-api-key")||this.authToken&&e.get("authorization"))&&!t.has("authorization"))throw Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(e){return rC([this.apiKeyAuth(e),this.bearerAuth(e)])}apiKeyAuth(e){if(null!=this.apiKey)return rC([{"X-Api-Key":this.apiKey}])}bearerAuth(e){if(null!=this.authToken)return rC([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new tv(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${tV}`}defaultIdempotencyKey(){return`stainless-node-retry-${ty()}`}makeStatusError(e,t,r,s){return tk.generate(e,t,r,s)}buildURL(e,t){let r=new URL(tB(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),s=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(s)&&(t={...s,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new tv("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details");return 6e5}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(r=>({method:e,path:t,...r})))}request(e,t=null){return new ra(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,r){let s=await e,n=s.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(s);let{req:a,url:i,timeout:o}=this.buildRequest(s,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:s});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===r?"":`, retryOf: ${r}`,d=Date.now();if(tX(this).debug(`[${l}] sending request`,tJ({retryOfRequestLogID:r,method:s.method,url:i,options:s,headers:a.headers})),s.signal?.aborted)throw new tS;let h=new AbortController,u=await this.fetchWithTimeout(i,a,o,h).catch(t_),f=Date.now();if(u instanceof Error){let e=`retrying, ${t} attempts remaining`;if(s.signal?.aborted)throw new tS;let n=tw(u)||/timed? ?out/i.test(String(u)+("cause"in u?String(u.cause):""));if(t)return tX(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),tX(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,tJ({retryOfRequestLogID:r,url:i,durationMs:f-d,message:u.message})),this.retryRequest(s,t,r??l);if(tX(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),tX(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,tJ({retryOfRequestLogID:r,url:i,durationMs:f-d,message:u.message})),n)throw new tR;throw new tA({cause:u})}let p=[...u.headers.entries()].filter(([e])=>"request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),x=`[${l}${c}${p}] ${a.method} ${i} ${u.ok?"succeeded":"failed"} with status ${u.status} in ${f-d}ms`;if(!u.ok){let e=this.shouldRetry(u);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await t8(u.body),tX(this).info(`${x} - ${e}`),tX(this).debug(`[${l}] response error (${e})`,tJ({retryOfRequestLogID:r,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),this.retryRequest(s,t,r??l,u.headers)}let n=e?"error; no more retries left":"error; not retryable";tX(this).info(`${x} - ${n}`);let a=await u.text().catch(e=>t_(e).message),i=tD(a),o=i?void 0:a;throw tX(this).debug(`[${l}] response error (${n})`,tJ({retryOfRequestLogID:r,url:u.url,status:u.status,headers:u.headers,message:o,durationMs:Date.now()-d})),this.makeStatusError(u.status,i,o,u.headers)}return tX(this).info(x),tX(this).debug(`[${l}] response start`,tJ({retryOfRequestLogID:r,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),{response:u,options:s,controller:h,requestLogID:l,retryOfRequestLogID:r,startTime:d}}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}requestAPIList(e,t){return new ro(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,r,s){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>s.abort());let o=setTimeout(()=>s.abort(),r),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:s.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,r,s){let n,a=s?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=s?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let r=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,r)}return await tW(n),this.makeRequest(e,t-1,r)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||null!=t&&e>t)throw new tv("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(e,{retryCount:t=0}={}){let r={...e},{method:s,path:n,query:a}=r,i=this.buildURL(n,a);"timeout"in r&&tL("timeout",r.timeout),r.timeout=r.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:r}),c=this.buildHeaders({options:e,method:s,bodyHeaders:o,retryCount:t});return{req:{method:s,headers:c,...r.signal&&{signal:r.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...r.fetchOptions??{}},url:i,timeout:r.timeout}}buildHeaders({options:e,method:t,bodyHeaders:r,retryCount:s}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=rC([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(s),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...t0(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(e),this._options.defaultHeaders,r,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let r=rC([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&r.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:t2(e)}:tb(this,es,"f").call(this,{body:e,headers:r})}}es=new WeakMap,r0.Anthropic=r0,r0.HUMAN_PROMPT="\n\nHuman:",r0.AI_PROMPT="\n\nAssistant:",r0.DEFAULT_TIMEOUT=6e5,r0.AnthropicError=tv,r0.APIError=tk,r0.APIConnectionError=tA,r0.APIConnectionTimeoutError=tR,r0.APIUserAbortError=tS,r0.NotFoundError=t$,r0.ConflictError=tj,r0.RateLimitError=tM,r0.BadRequestError=tI,r0.AuthenticationError=tC,r0.InternalServerError=tO,r0.PermissionDeniedError=tP,r0.UnprocessableEntityError=tE,r0.toFile=rk;class r1 extends r0{constructor(){super(...arguments),this.completions=new rK(this),this.messages=new rG(this),this.models=new rY(this),this.beta=new rz(this)}}r1.Completions=rK,r1.Messages=rG,r1.Models=rY,r1.Beta=rz;let{HUMAN_PROMPT:r2,AI_PROMPT:r4}=r1;function r8(e,t,r,s,n){if("m"===s)throw TypeError("Private method is not writable");if("a"===s&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?n.call(e,r):n?n.value=r:t.set(e,r),r}function r6(e,t,r,s){if("a"===r&&!s)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?s:"a"===r?s.call(e):s?s.value:t.get(e)}let r3=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return r3=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),r=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^r()&15>>e/4).toString(16))};function r5(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let r9=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class r7 extends Error{}class se extends r7{constructor(e,t,r,s){super(`${se.makeMessage(e,t,r)}`),this.status=e,this.headers=s,this.requestID=s?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,r){let s=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,t,r,s){if(!e||!s)return new sr({message:r,cause:r9(t)});let n=t?.error;return 400===e?new sn(e,n,r,s):401===e?new sa(e,n,r,s):403===e?new si(e,n,r,s):404===e?new so(e,n,r,s):409===e?new sl(e,n,r,s):422===e?new sc(e,n,r,s):429===e?new sd(e,n,r,s):e>=500?new sh(e,n,r,s):new se(e,n,r,s)}}class st extends se{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class sr extends se{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class ss extends sr{constructor({message:e}={}){super({message:e??"Request timed out."})}}class sn extends se{}class sa extends se{}class si extends se{}class so extends se{}class sl extends se{}class sc extends se{}class sd extends se{}class sh extends se{}class su extends r7{constructor(){super("Could not parse response content as the length limit was reached")}}class sf extends r7{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let sp=/^[a-z][a-z0-9+.-]*:/i,sx=e=>sp.test(e);function sm(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let sg=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new r7(`${e} must be an integer`);if(t<0)throw new r7(`${e} must be a positive integer`);return t},sb=e=>{try{return JSON.parse(e)}catch(e){return}},sy=e=>new Promise(t=>setTimeout(t,e)),sw={off:0,error:200,warn:300,info:400,debug:500},s_=(e,t,r)=>{if(e){if(Object.prototype.hasOwnProperty.call(sw,e))return e;sR(r).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(sw))}`)}};function sv(){}function sk(e,t,r){return!t||sw[e]>sw[r]?sv:t[e].bind(t)}let sS={error:sv,warn:sv,info:sv,debug:sv},sA=new WeakMap;function sR(e){let t=e.logger,r=e.logLevel??"off";if(!t)return sS;let s=sA.get(t);if(s&&s[0]===r)return s[1];let n={error:sk("error",t,r),warn:sk("warn",t,r),info:sk("info",t,r),debug:sk("debug",t,r)};return sA.set(t,[r,n]),n}let sI=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),sC="5.0.1",sP=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,s$=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sC,"X-Stainless-OS":sE(Deno.build.os),"X-Stainless-Arch":sj(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sC,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sC,"X-Stainless-OS":sE(globalThis.process.platform??"unknown"),"X-Stainless-Arch":sj(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,s=r[2]||0,n=r[3]||0;return{browser:e,version:`${t}.${s}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sC,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":sC,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},sj=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",sE=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",sM=()=>i??(i=s$());function sO(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function sN(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return sO({start(){},async pull(e){let{done:r,value:s}=await t.next();r?e.close():e.enqueue(s)},async cancel(){await t.return?.()}})}function sB(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function sT(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),r=t.cancel();t.releaseLock(),await r}let sL=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),sD="RFC3986",sW={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},sU=(Object.prototype.hasOwnProperty,Array.isArray),sH=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function sq(e,t){if(sU(e)){let r=[];for(let s=0;s<e.length;s+=1)r.push(t(e[s]));return r}return t(e)}let sF=Object.prototype.hasOwnProperty,sz={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},sK=Array.isArray,sX=Array.prototype.push,sJ=function(e,t){sX.apply(e,sK(t)?t:[t])},sV=Date.prototype.toISOString,sG={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,r,s,n)=>{if(0===e.length)return e;let a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let i="";for(let e=0;e<a.length;e+=1024){let t=a.length>=1024?a.slice(e,e+1024):a,r=[];for(let e=0;e<t.length;++e){let s=t.charCodeAt(e);if(45===s||46===s||95===s||126===s||s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||"RFC1738"===n&&(40===s||41===s)){r[r.length]=t.charAt(e);continue}if(s<128){r[r.length]=sH[s];continue}if(s<2048){r[r.length]=sH[192|s>>6]+sH[128|63&s];continue}if(s<55296||s>=57344){r[r.length]=sH[224|s>>12]+sH[128|s>>6&63]+sH[128|63&s];continue}e+=1,s=65536+((1023&s)<<10|1023&t.charCodeAt(e)),r[r.length]=sH[240|s>>18]+sH[128|s>>12&63]+sH[128|s>>6&63]+sH[128|63&s]}i+=r.join("")}return i},encodeValuesOnly:!1,format:sD,formatter:sW[sD],indices:!1,serializeDate:e=>sV.call(e),skipNulls:!1,strictNullHandling:!1},sQ={};function sY(e){let t;return(o??(o=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function sZ(e){let t;return(l??(l=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class s0{constructor(){en.set(this,void 0),ea.set(this,void 0),r8(this,en,new Uint8Array,"f"),r8(this,ea,null,"f")}decode(e){let t;if(null==e)return[];let r=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?sY(e):e;r8(this,en,function(e){let t=0;for(let r of e)t+=r.length;let r=new Uint8Array(t),s=0;for(let t of e)r.set(t,s),s+=t.length;return r}([r6(this,en,"f"),r]),"f");let s=[];for(;null!=(t=function(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}(r6(this,en,"f"),r6(this,ea,"f")));){if(t.carriage&&null==r6(this,ea,"f")){r8(this,ea,t.index,"f");continue}if(null!=r6(this,ea,"f")&&(t.index!==r6(this,ea,"f")+1||t.carriage)){s.push(sZ(r6(this,en,"f").subarray(0,r6(this,ea,"f")-1))),r8(this,en,r6(this,en,"f").subarray(r6(this,ea,"f")),"f"),r8(this,ea,null,"f");continue}let e=null!==r6(this,ea,"f")?t.preceding-1:t.preceding,r=sZ(r6(this,en,"f").subarray(0,e));s.push(r),r8(this,en,r6(this,en,"f").subarray(t.index),"f"),r8(this,ea,null,"f")}return s}flush(){return r6(this,en,"f").length?this.decode("\n"):[]}}en=new WeakMap,ea=new WeakMap,s0.NEWLINE_CHARS=new Set(["\n","\r"]),s0.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class s1{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;async function*s(){if(r)throw new r7("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let s=!1;try{for await(let r of s2(e,t))if(!s){if(r.data.startsWith("[DONE]")){s=!0;continue}if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if(t&&t.error)throw new se(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new se(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}}s=!0}catch(e){if(r5(e))return;throw e}finally{s||t.abort()}}return new s1(s,t)}static fromReadableStream(e,t){let r=!1;async function*s(){let t=new s0;for await(let r of sB(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new s1(async function*(){if(r)throw new r7("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of s())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(r5(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),s=s=>({next:()=>{if(0===s.length){let s=r.next();e.push(s),t.push(s)}return s.shift()}});return[new s1(()=>s(e),this.controller),new s1(()=>s(t),this.controller)]}toReadableStream(){let e,t=this;return sO({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:r,done:s}=await e.next();if(s)return t.close();let n=sY(JSON.stringify(r)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*s2(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new r7("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new r7("Attempted to iterate over a response with no body")}let r=new s8,s=new s0;for await(let t of s4(sB(e.body)))for(let e of s.decode(t)){let t=r.decode(e);t&&(yield t)}for(let e of s.flush()){let t=r.decode(e);t&&(yield t)}}async function*s4(e){let t=new Uint8Array;for await(let r of e){let e;if(null==r)continue;let s=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?sY(r):r,n=new Uint8Array(t.length+s.length);for(n.set(t),n.set(s,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class s8{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,s]=function(e,t){let r=e.indexOf(":");return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return s.startsWith(" ")&&(s=s.substring(1)),"event"===t?this.event=s:"data"===t&&this.data.push(s),null}}async function s6(e,t){let{response:r,requestLogID:s,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(sR(e).debug("response",r.status,r.url,r.headers,r.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(r,t.controller):s1.fromSSEResponse(r,t.controller);if(204===r.status)return null;if(t.options.__binaryResponse)return r;let s=r.headers.get("content-type"),n=s?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?s3(await r.json(),r):await r.text()})();return sR(e).debug(`[${s}] response parsed`,sI({retryOfRequestLogID:n,url:r.url,status:r.status,body:i,durationMs:Date.now()-a})),i}function s3(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class s5 extends Promise{constructor(e,t,r=s6){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=r,ei.set(this,void 0),r8(this,ei,e,"f")}_thenUnwrap(e){return new s5(r6(this,ei,"f"),this.responsePromise,async(t,r)=>s3(e(await this.parseResponse(t,r),r),r.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(r6(this,ei,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}ei=new WeakMap;class s9{constructor(e,t,r,s){eo.set(this,void 0),r8(this,eo,e,"f"),this.options=s,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new r7("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await r6(this,eo,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(eo=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class s7 extends s5{constructor(e,t,r){super(e,t,async(e,t)=>new r(e,t.response,await s6(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class ne extends s9{constructor(e,t,r,s){super(e,t,r,s),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class nt extends s9{constructor(e,t,r,s){super(e,t,r,s),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),r=t[t.length-1]?.id;return r?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:r}}:null}}let nr=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function ns(e,t,r){return nr(),new File(e,t??"unknown_file",r)}function nn(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let na=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],ni=async(e,t)=>({...e,body:await nl(e.body,t)}),no=new WeakMap,nl=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,r=no.get(t);if(r)return r;let s=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,r=new FormData;if(r.toString()===await new e(r).text())return!1;return!0}catch{return!0}})();return no.set(t,s),s}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let r=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>nu(r,e,t))),r},nc=e=>e instanceof Blob&&"name"in e,nd=e=>"object"==typeof e&&null!==e&&(e instanceof Response||na(e)||nc(e)),nh=e=>{if(nd(e))return!0;if(Array.isArray(e))return e.some(nh);if(e&&"object"==typeof e){for(let t in e)if(nh(e[t]))return!0}return!1},nu=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(r instanceof Response)e.append(t,ns([await r.blob()],nn(r)));else if(na(r))e.append(t,ns([await new Response(sN(r)).blob()],nn(r)));else if(nc(r))e.append(t,r,nn(r));else if(Array.isArray(r))await Promise.all(r.map(r=>nu(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,s])=>nu(e,`${t}[${r}]`,s)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}},nf=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,np=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&nf(e),nx=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function nm(e,t,r){if(nr(),np(e=await e))return e instanceof File?e:ns([await e.arrayBuffer()],e.name);if(nx(e)){let s=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),ns(await ng(s),t,r)}let s=await ng(e);if(t||(t=nn(e)),!r?.type){let e=s.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(r={...r,type:e})}return ns(s,t,r)}async function ng(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(nf(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(na(e))for await(let r of e)t.push(...await ng(r));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class nb{constructor(e){this._client=e}}function ny(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let nw=((e=ny)=>function(t,...r){let s;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,s,a)=>(/[?#]/.test(s)&&(n=!0),t+s+(a===r.length?"":(n?encodeURIComponent:e)(String(r[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(s=l.exec(i));)o.push({start:s.index,length:s[0].length});if(o.length>0){let e=0,t=o.reduce((t,r)=>{let s=" ".repeat(r.start-e),n="^".repeat(r.length);return e=r.start+r.length,t+s+n},"");throw new r7(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(ny);class n_ extends nb{list(e,t={},r){return this._client.getAPIList(nw`/chat/completions/${e}/messages`,nt,{query:t,...r})}}let nv=e=>e?.role==="assistant",nk=e=>e?.role==="tool";class nS{constructor(){el.add(this),this.controller=new AbortController,ec.set(this,void 0),ed.set(this,()=>{}),eh.set(this,()=>{}),eu.set(this,void 0),ef.set(this,()=>{}),ep.set(this,()=>{}),ex.set(this,{}),em.set(this,!1),eg.set(this,!1),eb.set(this,!1),ey.set(this,!1),r8(this,ec,new Promise((e,t)=>{r8(this,ed,e,"f"),r8(this,eh,t,"f")}),"f"),r8(this,eu,new Promise((e,t)=>{r8(this,ef,e,"f"),r8(this,ep,t,"f")}),"f"),r6(this,ec,"f").catch(()=>{}),r6(this,eu,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},r6(this,el,"m",ew).bind(this))},0)}_connected(){this.ended||(r6(this,ed,"f").call(this),this._emit("connect"))}get ended(){return r6(this,em,"f")}get errored(){return r6(this,eg,"f")}get aborted(){return r6(this,eb,"f")}abort(){this.controller.abort()}on(e,t){return(r6(this,ex,"f")[e]||(r6(this,ex,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=r6(this,ex,"f")[e];if(!r)return this;let s=r.findIndex(e=>e.listener===t);return s>=0&&r.splice(s,1),this}once(e,t){return(r6(this,ex,"f")[e]||(r6(this,ex,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{r8(this,ey,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){r8(this,ey,!0,"f"),await r6(this,eu,"f")}_emit(e,...t){if(r6(this,em,"f"))return;"end"===e&&(r8(this,em,!0,"f"),r6(this,ef,"f").call(this));let r=r6(this,ex,"f")[e];if(r&&(r6(this,ex,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];r6(this,ey,"f")||r?.length||Promise.reject(e),r6(this,eh,"f").call(this,e),r6(this,ep,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];r6(this,ey,"f")||r?.length||Promise.reject(e),r6(this,eh,"f").call(this,e),r6(this,ep,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function nA(e){return e?.$brand==="auto-parseable-response-format"}function nR(e){return e?.$brand==="auto-parseable-tool"}function nI(e,t){let r=e.choices.map(e=>{var r,s;if("length"===e.finish_reason)throw new su;if("content_filter"===e.finish_reason)throw new sf;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let r=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:nR(r)?r.$parseRaw(t.function.arguments):r?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(r=t,s=e.message.content,r.response_format?.type!=="json_schema"?null:r.response_format?.type==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(s):JSON.parse(s):null):null}}});return{...e,choices:r}}function nC(e){return!!nA(e.response_format)||(e.tools?.some(e=>nR(e)||"function"===e.type&&!0===e.function.strict)??!1)}ec=new WeakMap,ed=new WeakMap,eh=new WeakMap,eu=new WeakMap,ef=new WeakMap,ep=new WeakMap,ex=new WeakMap,em=new WeakMap,eg=new WeakMap,eb=new WeakMap,ey=new WeakMap,el=new WeakSet,ew=function(e){if(r8(this,eg,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new st),e instanceof st)return r8(this,eb,!0,"f"),this._emit("abort",e);if(e instanceof r7)return this._emit("error",e);if(e instanceof Error){let t=new r7(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new r7(String(e)))};class nP extends nS{constructor(){super(...arguments),e_.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),nk(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(nv(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new r7("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),r6(this,e_,"m",ev).call(this)}async finalMessage(){return await this.done(),r6(this,e_,"m",ek).call(this)}async finalFunctionToolCall(){return await this.done(),r6(this,e_,"m",eS).call(this)}async finalFunctionToolCallResult(){return await this.done(),r6(this,e_,"m",eA).call(this)}async totalUsage(){return await this.done(),r6(this,e_,"m",eR).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=r6(this,e_,"m",ek).call(this);t&&this._emit("finalMessage",t);let r=r6(this,e_,"m",ev).call(this);r&&this._emit("finalContent",r);let s=r6(this,e_,"m",eS).call(this);s&&this._emit("finalFunctionToolCall",s);let n=r6(this,e_,"m",eA).call(this);null!=n&&this._emit("finalFunctionToolCallResult",n),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",r6(this,e_,"m",eR).call(this))}async _createChatCompletion(e,t,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),r6(this,e_,"m",eI).call(this,t);let n=await e.chat.completions.create({...t,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(nI(n,t))}async _runChatCompletion(e,t,r){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,r)}async _runTools(e,t,r){let s="tool",{tool_choice:n="auto",stream:a,...i}=t,o="string"!=typeof n&&n?.function?.name,{maxChatCompletions:l=10}=r||{},c=t.tools.map(e=>{if(nR(e)){if(!e.$callback)throw new r7("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),d={};for(let e of c)"function"===e.type&&(d[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...i,tool_choice:n,tools:h,messages:[...this.messages]},r),a=t.choices[0]?.message;if(!a)throw new r7("missing message in ChatCompletion response");if(!a.tool_calls?.length)break;for(let e of a.tool_calls){let t;if("function"!==e.type)continue;let r=e.id,{name:n,arguments:a}=e.function,i=d[n];if(i){if(o&&o!==n){let e=`Invalid tool_call: ${JSON.stringify(n)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:s,tool_call_id:r,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(n)}. Available options are: ${Object.keys(d).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:s,tool_call_id:r,content:e});continue}try{t="function"==typeof i.parse?await i.parse(a):a}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:s,tool_call_id:r,content:e});continue}let l=await i.function(t,this),c=r6(this,e_,"m",eC).call(this,l);if(this._addMessage({role:s,tool_call_id:r,content:c}),o)return}}}}e_=new WeakSet,ev=function(){return r6(this,e_,"m",ek).call(this).content??null},ek=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(nv(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new r7("stream ended without producing a ChatCompletionMessage with role=assistant")},eS=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(nv(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},eA=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(nk(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},eR=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},eI=function(e){if(null!=e.n&&e.n>1)throw new r7("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},eC=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class n$ extends nP{static runTools(e,t,r){let s=new n$,n={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,n)),s}_addMessage(e,t=!0){super._addMessage(e,t),nv(e)&&e.content&&this._emit("content",e.content)}}let nj={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class nE extends Error{}class nM extends Error{}let nO=(e,t)=>{let r=e.length,s=0,n=e=>{throw new nE(`${e} at position ${s}`)},a=e=>{throw new nM(`${e} at position ${s}`)},i=()=>(h(),s>=r&&n("Unexpected end of input"),'"'===e[s])?o():"{"===e[s]?l():"["===e[s]?c():"null"===e.substring(s,s+4)||nj.NULL&t&&r-s<4&&"null".startsWith(e.substring(s))?(s+=4,null):"true"===e.substring(s,s+4)||nj.BOOL&t&&r-s<4&&"true".startsWith(e.substring(s))?(s+=4,!0):"false"===e.substring(s,s+5)||nj.BOOL&t&&r-s<5&&"false".startsWith(e.substring(s))?(s+=5,!1):"Infinity"===e.substring(s,s+8)||nj.INFINITY&t&&r-s<8&&"Infinity".startsWith(e.substring(s))?(s+=8,1/0):"-Infinity"===e.substring(s,s+9)||nj.MINUS_INFINITY&t&&1<r-s&&r-s<9&&"-Infinity".startsWith(e.substring(s))?(s+=9,-1/0):"NaN"===e.substring(s,s+3)||nj.NAN&t&&r-s<3&&"NaN".startsWith(e.substring(s))?(s+=3,NaN):d(),o=()=>{let i=s,o=!1;for(s++;s<r&&('"'!==e[s]||o&&"\\"===e[s-1]);)o="\\"===e[s]&&!o,s++;if('"'==e.charAt(s))try{return JSON.parse(e.substring(i,++s-Number(o)))}catch(e){a(String(e))}else if(nj.STR&t)try{return JSON.parse(e.substring(i,s-Number(o))+'"')}catch(t){return JSON.parse(e.substring(i,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{s++,h();let a={};try{for(;"}"!==e[s];){if(h(),s>=r&&nj.OBJ&t)return a;let n=o();h(),s++;try{let e=i();Object.defineProperty(a,n,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(nj.OBJ&t)return a;throw e}h(),","===e[s]&&s++}}catch(e){if(nj.OBJ&t)return a;n("Expected '}' at end of object")}return s++,a},c=()=>{s++;let r=[];try{for(;"]"!==e[s];)r.push(i()),h(),","===e[s]&&s++}catch(e){if(nj.ARR&t)return r;n("Expected ']' at end of array")}return s++,r},d=()=>{if(0===s){"-"===e&&nj.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(r){if(nj.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}a(String(r))}}let i=s;for("-"===e[s]&&s++;e[s]&&!",]}".includes(e[s]);)s++;s!=r||nj.NUM&t||n("Unterminated number literal");try{return JSON.parse(e.substring(i,s))}catch(r){"-"===e.substring(i,s)&&nj.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(i,e.lastIndexOf("e")))}catch(e){a(String(e))}}},h=()=>{for(;s<r&&" \n\r	".includes(e[s]);)s++};return i()},nN=e=>(function(e,t=nj.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return nO(e.trim(),t)})(e,nj.ALL^nj.NUM);class nB extends nP{constructor(e){super(),eP.add(this),e$.set(this,void 0),ej.set(this,void 0),eE.set(this,void 0),r8(this,e$,e,"f"),r8(this,ej,[],"f")}get currentChatCompletionSnapshot(){return r6(this,eE,"f")}static fromReadableStream(e){let t=new nB(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){let s=new nB(t);return s._run(()=>s._runChatCompletion(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createChatCompletion(e,t,r){super._createChatCompletion;let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),r6(this,eP,"m",eM).call(this);let n=await e.chat.completions.create({...t,stream:!0},{...r,signal:this.controller.signal});for await(let e of(this._connected(),n))r6(this,eP,"m",eN).call(this,e);if(n.controller.signal?.aborted)throw new st;return this._addChatCompletion(r6(this,eP,"m",eL).call(this))}async _fromReadableStream(e,t){let r,s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),r6(this,eP,"m",eM).call(this),this._connected();let n=s1.fromReadableStream(e,this.controller);for await(let e of n)r&&r!==e.id&&this._addChatCompletion(r6(this,eP,"m",eL).call(this)),r6(this,eP,"m",eN).call(this,e),r=e.id;if(n.controller.signal?.aborted)throw new st;return this._addChatCompletion(r6(this,eP,"m",eL).call(this))}[(e$=new WeakMap,ej=new WeakMap,eE=new WeakMap,eP=new WeakSet,eM=function(){this.ended||r8(this,eE,void 0,"f")},eO=function(e){let t=r6(this,ej,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},r6(this,ej,"f")[e.index]=t),t},eN=function(e){if(this.ended)return;let t=r6(this,eP,"m",eW).call(this,e);for(let r of(this._emit("chunk",e,t),e.choices)){let e=t.choices[r.index];null!=r.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",r.delta.content,e.message.content),this._emit("content.delta",{delta:r.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=r.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:r.delta.refusal,snapshot:e.message.refusal}),r.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:r.logprobs?.content,snapshot:e.logprobs?.content??[]}),r.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:r.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let s=r6(this,eP,"m",eO).call(this,e);for(let t of(e.finish_reason&&(r6(this,eP,"m",eT).call(this,e),null!=s.current_tool_call_index&&r6(this,eP,"m",eB).call(this,e,s.current_tool_call_index)),r.delta.tool_calls??[]))s.current_tool_call_index!==t.index&&(r6(this,eP,"m",eT).call(this,e),null!=s.current_tool_call_index&&r6(this,eP,"m",eB).call(this,e,s.current_tool_call_index)),s.current_tool_call_index=t.index;for(let t of r.delta.tool_calls??[]){let r=e.message.tool_calls?.[t.index];r?.type&&(r?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:r.function?.name,index:t.index,arguments:r.function.arguments,parsed_arguments:r.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):r?.type)}}},eB=function(e,t){if(r6(this,eP,"m",eO).call(this,e).done_tool_calls.has(t))return;let r=e.message.tool_calls?.[t];if(!r)throw Error("no tool call snapshot");if(!r.type)throw Error("tool call snapshot missing `type`");if("function"===r.type){let e=r6(this,e$,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===r.function.name);this._emit("tool_calls.function.arguments.done",{name:r.function.name,index:t,arguments:r.function.arguments,parsed_arguments:nR(e)?e.$parseRaw(r.function.arguments):e?.function.strict?JSON.parse(r.function.arguments):null})}else r.type},eT=function(e){let t=r6(this,eP,"m",eO).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let r=r6(this,eP,"m",eD).call(this);this._emit("content.done",{content:e.message.content,parsed:r?r.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},eL=function(){if(this.ended)throw new r7("stream has ended, this shouldn't happen");let e=r6(this,eE,"f");if(!e)throw new r7("request ended without sending any chunks");return r8(this,eE,void 0,"f"),r8(this,ej,[],"f"),function(e,t){var r;let{id:s,choices:n,created:a,model:i,system_fingerprint:o,...l}=e;return r={...l,id:s,choices:n.map(({message:t,finish_reason:r,index:s,logprobs:n,...a})=>{if(!r)throw new r7(`missing finish_reason for choice ${s}`);let{content:i=null,function_call:o,tool_calls:l,...c}=t,d=t.role;if(!d)throw new r7(`missing role for choice ${s}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new r7(`missing function_call.arguments for choice ${s}`);if(!l)throw new r7(`missing function_call.name for choice ${s}`);return{...a,message:{content:i,function_call:{arguments:e,name:l},role:d,refusal:t.refusal??null},finish_reason:r,index:s,logprobs:n}}return l?{...a,index:s,finish_reason:r,logprobs:n,message:{...c,role:d,content:i,refusal:t.refusal??null,tool_calls:l.map((t,r)=>{let{function:n,type:a,id:i,...o}=t,{arguments:l,name:c,...d}=n||{};if(null==i)throw new r7(`missing choices[${s}].tool_calls[${r}].id
${nT(e)}`);if(null==a)throw new r7(`missing choices[${s}].tool_calls[${r}].type
${nT(e)}`);if(null==c)throw new r7(`missing choices[${s}].tool_calls[${r}].function.name
${nT(e)}`);if(null==l)throw new r7(`missing choices[${s}].tool_calls[${r}].function.arguments
${nT(e)}`);return{...o,id:i,type:a,function:{...d,name:c,arguments:l}}})}}:{...a,message:{...c,content:i,role:d,refusal:t.refusal??null},finish_reason:r,index:s,logprobs:n}}),created:a,model:i,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&nC(t)?nI(r,t):{...r,choices:r.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,r6(this,e$,"f"))},eD=function(){let e=r6(this,e$,"f")?.response_format;return nA(e)?e:null},eW=function(e){var t,r,s,n;let a=r6(this,eE,"f"),{choices:i,...o}=e;for(let{delta:i,finish_reason:l,index:c,logprobs:d=null,...h}of(a?Object.assign(a,o):a=r8(this,eE,{...o,choices:[]},"f"),e.choices)){let e=a.choices[c];if(e||(e=a.choices[c]={finish_reason:l,index:c,message:{},logprobs:d,...h}),d)if(e.logprobs){let{content:s,refusal:n,...a}=d;Object.assign(e.logprobs,a),s&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...s)),n&&((r=e.logprobs).refusal??(r.refusal=[]),e.logprobs.refusal.push(...n))}else e.logprobs=Object.assign({},d);if(l&&(e.finish_reason=l,r6(this,e$,"f")&&nC(r6(this,e$,"f")))){if("length"===l)throw new su;if("content_filter"===l)throw new sf}if(Object.assign(e,h),!i)continue;let{content:o,refusal:u,function_call:f,role:p,tool_calls:x,...m}=i;if(Object.assign(e.message,m),u&&(e.message.refusal=(e.message.refusal||"")+u),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((s=e.message.function_call).arguments??(s.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&r6(this,eP,"m",eD).call(this)&&(e.message.parsed=nN(e.message.content))),x)for(let{index:t,id:r,type:s,function:a,...i}of(e.message.tool_calls||(e.message.tool_calls=[]),x)){let o=(n=e.message.tool_calls)[t]??(n[t]={});Object.assign(o,i),r&&(o.id=r),s&&(o.type=s),a&&(o.function??(o.function={name:a.name??"",arguments:""})),a?.name&&(o.function.name=a.name),a?.arguments&&(o.function.arguments+=a.arguments,function(e,t){if(!e)return!1;let r=e.tools?.find(e=>e.function?.name===t.function.name);return nR(r)||r?.function.strict||!1}(r6(this,e$,"f"),o)&&(o.function.parsed_arguments=nN(o.function.arguments)))}}return a},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("chunk",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new s1(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function nT(e){return JSON.stringify(e)}class nL extends nB{static fromReadableStream(e){let t=new nL(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,r){let s=new nL(t),n={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,n)),s}}class nD extends nb{constructor(){super(...arguments),this.messages=new n_(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(nw`/chat/completions/${e}`,t)}update(e,t,r){return this._client.post(nw`/chat/completions/${e}`,{body:t,...r})}list(e={},t){return this._client.getAPIList("/chat/completions",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new r7(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new r7(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>nI(t,e))}runTools(e,t){return e.stream?nL.runTools(this._client,e,t):n$.runTools(this._client,e,t)}stream(e,t){return nB.createChatCompletion(this._client,e,t)}}nD.Messages=n_;class nW extends nb{constructor(){super(...arguments),this.completions=new nD(this._client)}}nW.Completions=nD;let nU=Symbol("brand.privateNullableHeaders"),nH=Array.isArray,nq=e=>{let t=new Headers,r=new Set;for(let s of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(nU in e){let{values:t,nulls:r}=e;for(let e of(yield*t.entries(),r))yield[e,null];return}let r=!1;for(let s of(e instanceof Headers?t=e.entries():nH(e)?t=e:(r=!0,t=Object.entries(e??{})),t)){let e=s[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=nH(s[1])?s[1]:[s[1]],n=!1;for(let s of t)void 0!==s&&(r&&!n&&(n=!0,yield[e,null]),yield[e,s])}}(s)){let s=n.toLowerCase();e.has(s)||(t.delete(n),e.add(s)),null===a?(t.delete(n),r.add(s)):(t.append(n,a),r.delete(s))}}return{[nU]:!0,values:t,nulls:r}};class nF extends nb{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:nq([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class nz extends nb{create(e,t){return this._client.post("/audio/transcriptions",ni({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class nK extends nb{create(e,t){return this._client.post("/audio/translations",ni({body:e,...t,__metadata:{model:e.model}},this._client))}}class nX extends nb{constructor(){super(...arguments),this.transcriptions=new nz(this._client),this.translations=new nK(this._client),this.speech=new nF(this._client)}}nX.Transcriptions=nz,nX.Translations=nK,nX.Speech=nF;class nJ extends nb{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",nt,{query:e,...t})}cancel(e,t){return this._client.post(nw`/batches/${e}/cancel`,t)}}class nV extends nb{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(nw`/assistants/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,r){return this._client.post(nw`/assistants/${e}`,{body:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",nt,{query:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(nw`/assistants/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class nG extends nb{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class nQ extends nb{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class nY extends nb{constructor(){super(...arguments),this.sessions=new nG(this._client),this.transcriptionSessions=new nQ(this._client)}}nY.Sessions=nG,nY.TranscriptionSessions=nQ;class nZ extends nb{create(e,t,r){return this._client.post(nw`/threads/${e}/messages`,{body:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}retrieve(e,t,r){let{thread_id:s}=t;return this._client.get(nw`/threads/${s}/messages/${e}`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}update(e,t,r){let{thread_id:s,...n}=t;return this._client.post(nw`/threads/${s}/messages/${e}`,{body:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t={},r){return this._client.getAPIList(nw`/threads/${e}/messages`,nt,{query:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}delete(e,t,r){let{thread_id:s}=t;return this._client.delete(nw`/threads/${s}/messages/${e}`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}class n0 extends nb{retrieve(e,t,r){let{thread_id:s,run_id:n,...a}=t;return this._client.get(nw`/threads/${s}/runs/${n}/steps/${e}`,{query:a,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t,r){let{thread_id:s,...n}=t;return this._client.getAPIList(nw`/threads/${s}/runs/${e}/steps`,nt,{query:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}let n1=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),r=t.length,s=new Uint8Array(r);for(let e=0;e<r;e++)s[e]=t.charCodeAt(e);return Array.from(new Float32Array(s.buffer))}},n2=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class n4 extends nS{constructor(){super(...arguments),eU.add(this),eq.set(this,[]),eF.set(this,{}),ez.set(this,{}),eK.set(this,void 0),eX.set(this,void 0),eJ.set(this,void 0),eV.set(this,void 0),eG.set(this,void 0),eQ.set(this,void 0),eY.set(this,void 0),eZ.set(this,void 0),e0.set(this,void 0)}[(eq=new WeakMap,eF=new WeakMap,ez=new WeakMap,eK=new WeakMap,eX=new WeakMap,eJ=new WeakMap,eV=new WeakMap,eG=new WeakMap,eQ=new WeakMap,eY=new WeakMap,eZ=new WeakMap,e0=new WeakMap,eU=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new eH;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();let s=s1.fromReadableStream(e,this.controller);for await(let e of s)r6(this,eU,"m",e1).call(this,e);if(s.controller.signal?.aborted)throw new st;return this._addRun(r6(this,eU,"m",e2).call(this))}toReadableStream(){return new s1(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,s){let n=new eH;return n._run(()=>n._runToolAssistantStream(e,t,r,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createToolAssistantStream(e,t,r,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a={...r,stream:!0},i=await e.submitToolOutputs(t,a,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))r6(this,eU,"m",e1).call(this,e);if(i.controller.signal?.aborted)throw new st;return this._addRun(r6(this,eU,"m",e2).call(this))}static createThreadAssistantStream(e,t,r){let s=new eH;return s._run(()=>s._threadAssistantStream(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}static createAssistantStream(e,t,r,s){let n=new eH;return n._run(()=>n._runAssistantStream(e,t,r,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}currentEvent(){return r6(this,eY,"f")}currentRun(){return r6(this,eZ,"f")}currentMessageSnapshot(){return r6(this,eK,"f")}currentRunStepSnapshot(){return r6(this,e0,"f")}async finalRunSteps(){return await this.done(),Object.values(r6(this,eF,"f"))}async finalMessages(){return await this.done(),Object.values(r6(this,ez,"f"))}async finalRun(){if(await this.done(),!r6(this,eX,"f"))throw Error("Final run was not received.");return r6(this,eX,"f")}async _createThreadAssistantStream(e,t,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));let n={...t,stream:!0},a=await e.createAndRun(n,{...r,signal:this.controller.signal});for await(let e of(this._connected(),a))r6(this,eU,"m",e1).call(this,e);if(a.controller.signal?.aborted)throw new st;return this._addRun(r6(this,eU,"m",e2).call(this))}async _createAssistantStream(e,t,r,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a={...r,stream:!0},i=await e.create(t,a,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))r6(this,eU,"m",e1).call(this,e);if(i.controller.signal?.aborted)throw new st;return this._addRun(r6(this,eU,"m",e2).call(this))}static accumulateDelta(e,t){for(let[r,s]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=s;continue}let t=e[r];if(null==t||"index"===r||"type"===r){e[r]=s;continue}if("string"==typeof t&&"string"==typeof s)t+=s;else if("number"==typeof t&&"number"==typeof s)t+=s;else if(sm(t)&&sm(s))t=this.accumulateDelta(t,s);else if(Array.isArray(t)&&Array.isArray(s)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...s);continue}for(let e of s){if(!sm(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let r=e.index;if(null==r)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof r)throw Error(`Expected array delta entry \`index\` property to be a number but got ${r}`);let s=t[r];null==s?t.push(e):t[r]=this.accumulateDelta(s,e)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${s}, accValue: ${t}`);e[r]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,s){return await this._createAssistantStream(t,e,r,s)}async _runToolAssistantStream(e,t,r,s){return await this._createToolAssistantStream(t,e,r,s)}}eH=n4,e1=function(e){if(!this.ended)switch(r8(this,eY,e,"f"),r6(this,eU,"m",e6).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":r6(this,eU,"m",e7).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":r6(this,eU,"m",e8).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":r6(this,eU,"m",e4).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},e2=function(){if(this.ended)throw new r7("stream has ended, this shouldn't happen");if(!r6(this,eX,"f"))throw Error("Final run has not been received");return r6(this,eX,"f")},e4=function(e){let[t,r]=r6(this,eU,"m",e5).call(this,e,r6(this,eK,"f"));for(let e of(r8(this,eK,t,"f"),r6(this,ez,"f")[t.id]=t,r)){let r=t.content[e.index];r?.type=="text"&&this._emit("textCreated",r.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let r of e.data.delta.content){if("text"==r.type&&r.text){let e=r.text,s=t.content[r.index];if(s&&"text"==s.type)this._emit("textDelta",e,s.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(r.index!=r6(this,eJ,"f")){if(r6(this,eV,"f"))switch(r6(this,eV,"f").type){case"text":this._emit("textDone",r6(this,eV,"f").text,r6(this,eK,"f"));break;case"image_file":this._emit("imageFileDone",r6(this,eV,"f").image_file,r6(this,eK,"f"))}r8(this,eJ,r.index,"f")}r8(this,eV,t.content[r.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==r6(this,eJ,"f")){let t=e.data.content[r6(this,eJ,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,r6(this,eK,"f"));break;case"text":this._emit("textDone",t.text,r6(this,eK,"f"))}}r6(this,eK,"f")&&this._emit("messageDone",e.data),r8(this,eK,void 0,"f")}},e8=function(e){let t=r6(this,eU,"m",e3).call(this,e);switch(r8(this,e0,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let r=e.data.delta;if(r.step_details&&"tool_calls"==r.step_details.type&&r.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of r.step_details.tool_calls)e.index==r6(this,eG,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(r6(this,eQ,"f")&&this._emit("toolCallDone",r6(this,eQ,"f")),r8(this,eG,e.index,"f"),r8(this,eQ,t.step_details.tool_calls[e.index],"f"),r6(this,eQ,"f")&&this._emit("toolCallCreated",r6(this,eQ,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":r8(this,e0,void 0,"f"),"tool_calls"==e.data.step_details.type&&r6(this,eQ,"f")&&(this._emit("toolCallDone",r6(this,eQ,"f")),r8(this,eQ,void 0,"f")),this._emit("runStepDone",e.data,t)}},e6=function(e){r6(this,eq,"f").push(e),this._emit("event",e)},e3=function(e){switch(e.event){case"thread.run.step.created":return r6(this,eF,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=r6(this,eF,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){let s=eH.accumulateDelta(t,r.delta);r6(this,eF,"f")[e.data.id]=s}return r6(this,eF,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":r6(this,eF,"f")[e.data.id]=e.data}if(r6(this,eF,"f")[e.data.id])return r6(this,eF,"f")[e.data.id];throw Error("No snapshot available")},e5=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let s=e.data;if(s.delta.content)for(let e of s.delta.content)if(e.index in t.content){let r=t.content[e.index];t.content[e.index]=r6(this,eU,"m",e9).call(this,e,r)}else t.content[e.index]=e,r.push(e);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},e9=function(e,t){return eH.accumulateDelta(t,e)},e7=function(e){switch(r8(this,eZ,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":r8(this,eX,e.data,"f"),r6(this,eQ,"f")&&(this._emit("toolCallDone",r6(this,eQ,"f")),r8(this,eQ,void 0,"f"))}};class n8 extends nb{constructor(){super(...arguments),this.steps=new n0(this._client)}create(e,t,r){let{include:s,...n}=t;return this._client.post(nw`/threads/${e}/runs`,{query:{include:s},body:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers]),stream:t.stream??!1})}retrieve(e,t,r){let{thread_id:s}=t;return this._client.get(nw`/threads/${s}/runs/${e}`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}update(e,t,r){let{thread_id:s,...n}=t;return this._client.post(nw`/threads/${s}/runs/${e}`,{body:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t={},r){return this._client.getAPIList(nw`/threads/${e}/runs`,nt,{query:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}cancel(e,t,r){let{thread_id:s}=t;return this._client.post(nw`/threads/${s}/runs/${e}/cancel`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async createAndPoll(e,t,r){let s=await this.create(e,t,r);return await this.poll(s.id,{thread_id:e},r)}createAndStream(e,t,r){return n4.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){let s=nq([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:a}=await this.retrieve(e,t,{...r,headers:{...r?.headers,...s}}).withResponse();switch(n.status){case"queued":case"in_progress":case"cancelling":let i=5e3;if(r?.pollIntervalMs)i=r.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await sy(i);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return n}}}stream(e,t,r){return n4.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r){let{thread_id:s,...n}=t;return this._client.post(nw`/threads/${s}/runs/${e}/submit_tool_outputs`,{body:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,r){let s=await this.submitToolOutputs(e,t,r);return await this.poll(s.id,t,r)}submitToolOutputsStream(e,t,r){return n4.createToolAssistantStream(e,this._client.beta.threads.runs,t,r)}}n8.Steps=n0;class n6 extends nb{constructor(){super(...arguments),this.runs=new n8(this._client),this.messages=new nZ(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(nw`/threads/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,r){return this._client.post(nw`/threads/${e}`,{body:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}delete(e,t){return this._client.delete(nw`/threads/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let r=await this.createAndRun(e,t);return await this.runs.poll(r.id,{thread_id:r.thread_id},t)}createAndRunStream(e,t){return n4.createThreadAssistantStream(e,this._client.beta.threads,t)}}n6.Runs=n8,n6.Messages=nZ;class n3 extends nb{constructor(){super(...arguments),this.realtime=new nY(this._client),this.assistants=new nV(this._client),this.threads=new n6(this._client)}}n3.Realtime=nY,n3.Assistants=nV,n3.Threads=n6;class n5 extends nb{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class n9 extends nb{retrieve(e,t,r){let{container_id:s}=t;return this._client.get(nw`/containers/${s}/files/${e}/content`,{...r,headers:nq([{Accept:"application/binary"},r?.headers]),__binaryResponse:!0})}}class n7 extends nb{constructor(){super(...arguments),this.content=new n9(this._client)}create(e,t,r){return this._client.post(nw`/containers/${e}/files`,ni({body:t,...r},this._client))}retrieve(e,t,r){let{container_id:s}=t;return this._client.get(nw`/containers/${s}/files/${e}`,r)}list(e,t={},r){return this._client.getAPIList(nw`/containers/${e}/files`,nt,{query:t,...r})}delete(e,t,r){let{container_id:s}=t;return this._client.delete(nw`/containers/${s}/files/${e}`,{...r,headers:nq([{Accept:"*/*"},r?.headers])})}}n7.Content=n9;class ae extends nb{constructor(){super(...arguments),this.files=new n7(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/containers/${e}`,{...t,headers:nq([{Accept:"*/*"},t?.headers])})}}ae.Files=n7;class at extends nb{create(e,t){let r=!!e.encoding_format,s=r?e.encoding_format:"base64";r&&sR(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let n=this._client.post("/embeddings",{body:{...e,encoding_format:s},...t});return r?n:(sR(this._client).debug("embeddings/decoding base64 embeddings from base64"),n._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=n1(t)}),e)))}}class ar extends nb{retrieve(e,t,r){let{eval_id:s,run_id:n}=t;return this._client.get(nw`/evals/${s}/runs/${n}/output_items/${e}`,r)}list(e,t,r){let{eval_id:s,...n}=t;return this._client.getAPIList(nw`/evals/${s}/runs/${e}/output_items`,nt,{query:n,...r})}}class as extends nb{constructor(){super(...arguments),this.outputItems=new ar(this._client)}create(e,t,r){return this._client.post(nw`/evals/${e}/runs`,{body:t,...r})}retrieve(e,t,r){let{eval_id:s}=t;return this._client.get(nw`/evals/${s}/runs/${e}`,r)}list(e,t={},r){return this._client.getAPIList(nw`/evals/${e}/runs`,nt,{query:t,...r})}delete(e,t,r){let{eval_id:s}=t;return this._client.delete(nw`/evals/${s}/runs/${e}`,r)}cancel(e,t,r){let{eval_id:s}=t;return this._client.post(nw`/evals/${s}/runs/${e}`,r)}}as.OutputItems=ar;class an extends nb{constructor(){super(...arguments),this.runs=new as(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/evals/${e}`,t)}update(e,t,r){return this._client.post(nw`/evals/${e}`,{body:t,...r})}list(e={},t){return this._client.getAPIList("/evals",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/evals/${e}`,t)}}an.Runs=as;class aa extends nb{create(e,t){return this._client.post("/files",ni({body:e,...t},this._client))}retrieve(e,t){return this._client.get(nw`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",nt,{query:e,...t})}delete(e,t){return this._client.delete(nw`/files/${e}`,t)}content(e,t){return this._client.get(nw`/files/${e}/content`,{...t,headers:nq([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=18e5}={}){let s=new Set(["processed","error","deleted"]),n=Date.now(),a=await this.retrieve(e);for(;!a.status||!s.has(a.status);)if(await sy(t),a=await this.retrieve(e),Date.now()-n>r)throw new ss({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return a}}class ai extends nb{}class ao extends nb{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class al extends nb{constructor(){super(...arguments),this.graders=new ao(this._client)}}al.Graders=ao;class ac extends nb{create(e,t,r){return this._client.getAPIList(nw`/fine_tuning/checkpoints/${e}/permissions`,ne,{body:t,method:"post",...r})}retrieve(e,t={},r){return this._client.get(nw`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...r})}delete(e,t,r){let{fine_tuned_model_checkpoint:s}=t;return this._client.delete(nw`/fine_tuning/checkpoints/${s}/permissions/${e}`,r)}}class ad extends nb{constructor(){super(...arguments),this.permissions=new ac(this._client)}}ad.Permissions=ac;class ah extends nb{list(e,t={},r){return this._client.getAPIList(nw`/fine_tuning/jobs/${e}/checkpoints`,nt,{query:t,...r})}}class au extends nb{constructor(){super(...arguments),this.checkpoints=new ah(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(nw`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",nt,{query:e,...t})}cancel(e,t){return this._client.post(nw`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return this._client.getAPIList(nw`/fine_tuning/jobs/${e}/events`,nt,{query:t,...r})}pause(e,t){return this._client.post(nw`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(nw`/fine_tuning/jobs/${e}/resume`,t)}}au.Checkpoints=ah;class af extends nb{constructor(){super(...arguments),this.methods=new ai(this._client),this.jobs=new au(this._client),this.checkpoints=new ad(this._client),this.alpha=new al(this._client)}}af.Methods=ai,af.Jobs=au,af.Checkpoints=ad,af.Alpha=al;class ap extends nb{}class ax extends nb{constructor(){super(...arguments),this.graderModels=new ap(this._client)}}ax.GraderModels=ap;class am extends nb{createVariation(e,t){return this._client.post("/images/variations",ni({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",ni({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class ag extends nb{retrieve(e,t){return this._client.get(nw`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",ne,e)}delete(e,t){return this._client.delete(nw`/models/${e}`,t)}}class ab extends nb{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function ay(e,t){let r=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let r=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(r)?r.$parseRaw(t.arguments):r?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let r=e.content.map(e=>{var r,s;return"output_text"===e.type?{...e,parsed:(r=t,s=e.text,r.text?.format?.type!=="json_schema"?null:"$parseRaw"in r.text?.format?(r.text?.format).$parseRaw(s):JSON.parse(s))}:e});return{...e,content:r}}return e}),s=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||aw(s),Object.defineProperty(s,"output_parsed",{enumerable:!0,get(){for(let e of s.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),s}function aw(e){let t=[];for(let r of e.output)if("message"===r.type)for(let e of r.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class a_ extends nS{constructor(e){super(),te.add(this),tt.set(this,void 0),tr.set(this,void 0),ts.set(this,void 0),r8(this,tt,e,"f")}static createResponse(e,t,r){let s=new a_(t);return s._run(()=>s._createOrRetrieveResponse(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createOrRetrieveResponse(e,t,r){let s,n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),r6(this,te,"m",tn).call(this);let a=null;for await(let n of("response_id"in t?(s=await e.responses.retrieve(t.response_id,{stream:!0},{...r,signal:this.controller.signal,stream:!0}),a=t.starting_after??null):s=await e.responses.create({...t,stream:!0},{...r,signal:this.controller.signal}),this._connected(),s))r6(this,te,"m",ta).call(this,n,a);if(s.controller.signal?.aborted)throw new st;return r6(this,te,"m",ti).call(this)}[(tt=new WeakMap,tr=new WeakMap,ts=new WeakMap,te=new WeakSet,tn=function(){this.ended||r8(this,tr,void 0,"f")},ta=function(e,t){if(this.ended)return;let r=(e,r)=>{(null==t||r.sequence_number>t)&&this._emit(e,r)},s=r6(this,te,"m",to).call(this,e);switch(r("event",e),e.type){case"response.output_text.delta":{let t=s.output[e.output_index];if(!t)throw new r7(`missing output at index ${e.output_index}`);if("message"===t.type){let s=t.content[e.content_index];if(!s)throw new r7(`missing content at index ${e.content_index}`);if("output_text"!==s.type)throw new r7(`expected content to be 'output_text', got ${s.type}`);r("response.output_text.delta",{...e,snapshot:s.text})}break}case"response.function_call_arguments.delta":{let t=s.output[e.output_index];if(!t)throw new r7(`missing output at index ${e.output_index}`);"function_call"===t.type&&r("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:r(e.type,e)}},ti=function(){if(this.ended)throw new r7("stream has ended, this shouldn't happen");let e=r6(this,tr,"f");if(!e)throw new r7("request ended without sending any events");r8(this,tr,void 0,"f");let t=function(e,t){var r;return t&&(r=t,nA(r.text?.format))?ay(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,r6(this,tt,"f"));return r8(this,ts,t,"f"),t},to=function(e){let t=r6(this,tr,"f");if(!t){if("response.created"!==e.type)throw new r7(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return r8(this,tr,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let r=t.output[e.output_index];if(!r)throw new r7(`missing output at index ${e.output_index}`);"message"===r.type&&r.content.push(e.part);break}case"response.output_text.delta":{let r=t.output[e.output_index];if(!r)throw new r7(`missing output at index ${e.output_index}`);if("message"===r.type){let t=r.content[e.content_index];if(!t)throw new r7(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new r7(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let r=t.output[e.output_index];if(!r)throw new r7(`missing output at index ${e.output_index}`);"function_call"===r.type&&(r.arguments+=e.delta);break}case"response.completed":r8(this,tr,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=r6(this,ts,"f");if(!e)throw new r7("stream ended without producing a ChatCompletion");return e}}class av extends nb{list(e,t={},r){return this._client.getAPIList(nw`/responses/${e}/input_items`,nt,{query:t,...r})}}class ak extends nb{constructor(){super(...arguments),this.inputItems=new av(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&aw(e),e))}retrieve(e,t={},r){return this._client.get(nw`/responses/${e}`,{query:t,...r,stream:t?.stream??!1})}delete(e,t){return this._client.delete(nw`/responses/${e}`,{...t,headers:nq([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>ay(t,e))}stream(e,t){return a_.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(nw`/responses/${e}/cancel`,{...t,headers:nq([{Accept:"*/*"},t?.headers])})}}ak.InputItems=av;class aS extends nb{create(e,t,r){return this._client.post(nw`/uploads/${e}/parts`,ni({body:t,...r},this._client))}}class aA extends nb{constructor(){super(...arguments),this.parts=new aS(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(nw`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(nw`/uploads/${e}/complete`,{body:t,...r})}}aA.Parts=aS;let aR=async e=>{let t=await Promise.allSettled(e),r=t.filter(e=>"rejected"===e.status);if(r.length){for(let e of r)console.error(e.reason);throw Error(`${r.length} promise(s) failed - see the above errors`)}let s=[];for(let e of t)"fulfilled"===e.status&&s.push(e.value);return s};class aI extends nb{create(e,t,r){return this._client.post(nw`/vector_stores/${e}/file_batches`,{body:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}retrieve(e,t,r){let{vector_store_id:s}=t;return this._client.get(nw`/vector_stores/${s}/file_batches/${e}`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}cancel(e,t,r){let{vector_store_id:s}=t;return this._client.post(nw`/vector_stores/${s}/file_batches/${e}/cancel`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async createAndPoll(e,t,r){let s=await this.create(e,t);return await this.poll(e,s.id,r)}listFiles(e,t,r){let{vector_store_id:s,...n}=t;return this._client.getAPIList(nw`/vector_stores/${s}/file_batches/${e}/files`,nt,{query:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async poll(e,t,r){let s=nq([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:a}=await this.retrieve(t,{vector_store_id:e},{...r,headers:s}).withResponse();switch(n.status){case"in_progress":let i=5e3;if(r?.pollIntervalMs)i=r.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await sy(i);break;case"failed":case"cancelled":case"completed":return n}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},s){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let n=Math.min(s?.maxConcurrency??5,t.length),a=this._client,i=t.values(),o=[...r];async function l(e){for(let t of e){let e=await a.files.create({file:t,purpose:"assistants"},s);o.push(e.id)}}let c=Array(n).fill(i).map(l);return await aR(c),await this.createAndPoll(e,{file_ids:o})}}class aC extends nb{create(e,t,r){return this._client.post(nw`/vector_stores/${e}/files`,{body:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}retrieve(e,t,r){let{vector_store_id:s}=t;return this._client.get(nw`/vector_stores/${s}/files/${e}`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}update(e,t,r){let{vector_store_id:s,...n}=t;return this._client.post(nw`/vector_stores/${s}/files/${e}`,{body:n,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t={},r){return this._client.getAPIList(nw`/vector_stores/${e}/files`,nt,{query:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}delete(e,t,r){let{vector_store_id:s}=t;return this._client.delete(nw`/vector_stores/${s}/files/${e}`,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async createAndPoll(e,t,r){let s=await this.create(e,t,r);return await this.poll(e,s.id,r)}async poll(e,t,r){let s=nq([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let n=await this.retrieve(t,{vector_store_id:e},{...r,headers:s}).withResponse(),a=n.data;switch(a.status){case"in_progress":let i=5e3;if(r?.pollIntervalMs)i=r.pollIntervalMs;else{let e=n.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await sy(i);break;case"failed":case"completed":return a}}}async upload(e,t,r){let s=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:s.id},r)}async uploadAndPoll(e,t,r){let s=await this.upload(e,t,r);return await this.poll(e,s.id,r)}content(e,t,r){let{vector_store_id:s}=t;return this._client.getAPIList(nw`/vector_stores/${s}/files/${e}/content`,ne,{...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}class aP extends nb{constructor(){super(...arguments),this.files=new aC(this._client),this.fileBatches=new aI(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(nw`/vector_stores/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,r){return this._client.post(nw`/vector_stores/${e}`,{body:t,...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",nt,{query:e,...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(nw`/vector_stores/${e}`,{...t,headers:nq([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,r){return this._client.getAPIList(nw`/vector_stores/${e}/search`,ne,{body:t,method:"post",...r,headers:nq([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}aP.Files=aC,aP.FileBatches=aI;class a${constructor({baseURL:e=n2("OPENAI_BASE_URL"),apiKey:t=n2("OPENAI_API_KEY"),organization:r=n2("OPENAI_ORG_ID")??null,project:s=n2("OPENAI_PROJECT_ID")??null,...n}={}){if(tc.set(this,void 0),this.completions=new n5(this),this.chat=new nW(this),this.embeddings=new at(this),this.files=new aa(this),this.images=new am(this),this.audio=new nX(this),this.moderations=new ab(this),this.models=new ag(this),this.fineTuning=new af(this),this.graders=new ax(this),this.vectorStores=new aP(this),this.beta=new n3(this),this.batches=new nJ(this),this.uploads=new aA(this),this.responses=new ak(this),this.evals=new an(this),this.containers=new ae(this),void 0===t)throw new r7("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let a={apiKey:t,organization:r,project:s,...n,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&sP())throw new r7("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=a.baseURL,this.timeout=a.timeout??tl.DEFAULT_TIMEOUT,this.logger=a.logger??console;let i="warn";this.logLevel=i,this.logLevel=s_(a.logLevel,"ClientOptions.logLevel",this)??s_(n2("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??i,this.fetchOptions=a.fetchOptions,this.maxRetries=a.maxRetries??2,this.fetch=a.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),r8(this,tc,sL,"f"),this._options=a,this.apiKey=t,this.organization=r,this.project=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return nq([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let r,s,n=e,a=function(e=sG){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let r=e.charset||sG.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let s=sD;if(void 0!==e.format){if(!sF.call(sW,e.format))throw TypeError("Unknown format option provided.");s=e.format}let n=sW[s],a=sG.filter;if(("function"==typeof e.filter||sK(e.filter))&&(a=e.filter),t=e.arrayFormat&&e.arrayFormat in sz?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":sG.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let i=void 0===e.allowDots?!0==!!e.encodeDotInKeys||sG.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:sG.addQueryPrefix,allowDots:i,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:sG.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:sG.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?sG.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:sG.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:sG.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:sG.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:sG.encodeValuesOnly,filter:a,format:s,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:sG.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:sG.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:sG.strictNullHandling}}(t);"function"==typeof a.filter?n=(0,a.filter)("",n):sK(a.filter)&&(r=a.filter);let i=[];if("object"!=typeof n||null===n)return"";let o=sz[a.arrayFormat],l="comma"===o&&a.commaRoundTrip;r||(r=Object.keys(n)),a.sort&&r.sort(a.sort);let c=new WeakMap;for(let e=0;e<r.length;++e){let t=r[e];a.skipNulls&&null===n[t]||sJ(i,function e(t,r,s,n,a,i,o,l,c,d,h,u,f,p,x,m,g,b){var y,w;let _,v=t,k=b,S=0,A=!1;for(;void 0!==(k=k.get(sQ))&&!A;){let e=k.get(t);if(S+=1,void 0!==e)if(e===S)throw RangeError("Cyclic object value");else A=!0;void 0===k.get(sQ)&&(S=0)}if("function"==typeof d?v=d(r,v):v instanceof Date?v=f?.(v):"comma"===s&&sK(v)&&(v=sq(v,function(e){return e instanceof Date?f?.(e):e})),null===v){if(i)return c&&!m?c(r,sG.encoder,g,"key",p):r;v=""}if("string"==typeof(y=v)||"number"==typeof y||"boolean"==typeof y||"symbol"==typeof y||"bigint"==typeof y||(w=v)&&"object"==typeof w&&w.constructor&&w.constructor.isBuffer&&w.constructor.isBuffer(w)){if(c){let e=m?r:c(r,sG.encoder,g,"key",p);return[x?.(e)+"="+x?.(c(v,sG.encoder,g,"value",p))]}return[x?.(r)+"="+x?.(String(v))]}let R=[];if(void 0===v)return R;if("comma"===s&&sK(v))m&&c&&(v=sq(v,c)),_=[{value:v.length>0?v.join(",")||null:void 0}];else if(sK(d))_=d;else{let e=Object.keys(v);_=h?e.sort(h):e}let I=l?String(r).replace(/\./g,"%2E"):String(r),C=n&&sK(v)&&1===v.length?I+"[]":I;if(a&&sK(v)&&0===v.length)return C+"[]";for(let r=0;r<_.length;++r){let y=_[r],w="object"==typeof y&&void 0!==y.value?y.value:v[y];if(o&&null===w)continue;let k=u&&l?y.replace(/\./g,"%2E"):y,A=sK(v)?"function"==typeof s?s(C,k):C:C+(u?"."+k:"["+k+"]");b.set(t,S);let I=new WeakMap;I.set(sQ,b),sJ(R,e(w,A,s,n,a,i,o,l,"comma"===s&&m&&sK(v)?null:c,d,h,u,f,p,x,m,g,I))}return R}(n[t],t,o,l,a.allowEmptyArrays,a.strictNullHandling,a.skipNulls,a.encodeDotInKeys,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,c))}let d=i.join(a.delimiter),h=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),d.length>0?h+d:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${sC}`}defaultIdempotencyKey(){return`stainless-node-retry-${r3()}`}makeStatusError(e,t,r,s){return se.generate(e,t,r,s)}buildURL(e,t){let r=new URL(sx(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),s=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(s)&&(t={...s,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(r=>({method:e,path:t,...r})))}request(e,t=null){return new s5(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,r){let s=await e,n=s.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(s);let{req:a,url:i,timeout:o}=this.buildRequest(s,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:s});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===r?"":`, retryOf: ${r}`,d=Date.now();if(sR(this).debug(`[${l}] sending request`,sI({retryOfRequestLogID:r,method:s.method,url:i,options:s,headers:a.headers})),s.signal?.aborted)throw new st;let h=new AbortController,u=await this.fetchWithTimeout(i,a,o,h).catch(r9),f=Date.now();if(u instanceof Error){let e=`retrying, ${t} attempts remaining`;if(s.signal?.aborted)throw new st;let n=r5(u)||/timed? ?out/i.test(String(u)+("cause"in u?String(u.cause):""));if(t)return sR(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),sR(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,sI({retryOfRequestLogID:r,url:i,durationMs:f-d,message:u.message})),this.retryRequest(s,t,r??l);if(sR(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),sR(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,sI({retryOfRequestLogID:r,url:i,durationMs:f-d,message:u.message})),n)throw new ss;throw new sr({cause:u})}let p=[...u.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),x=`[${l}${c}${p}] ${a.method} ${i} ${u.ok?"succeeded":"failed"} with status ${u.status} in ${f-d}ms`;if(!u.ok){let e=this.shouldRetry(u);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await sT(u.body),sR(this).info(`${x} - ${e}`),sR(this).debug(`[${l}] response error (${e})`,sI({retryOfRequestLogID:r,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),this.retryRequest(s,t,r??l,u.headers)}let n=e?"error; no more retries left":"error; not retryable";sR(this).info(`${x} - ${n}`);let a=await u.text().catch(e=>r9(e).message),i=sb(a),o=i?void 0:a;throw sR(this).debug(`[${l}] response error (${n})`,sI({retryOfRequestLogID:r,url:u.url,status:u.status,headers:u.headers,message:o,durationMs:Date.now()-d})),this.makeStatusError(u.status,i,o,u.headers)}return sR(this).info(x),sR(this).debug(`[${l}] response start`,sI({retryOfRequestLogID:r,url:u.url,status:u.status,headers:u.headers,durationMs:f-d})),{response:u,options:s,controller:h,requestLogID:l,retryOfRequestLogID:r,startTime:d}}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}requestAPIList(e,t){return new s7(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,r,s){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>s.abort());let o=setTimeout(()=>s.abort(),r),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:s.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,r,s){let n,a=s?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=s?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let r=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,r)}return await sy(n),this.makeRequest(e,t-1,r)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let r={...e},{method:s,path:n,query:a}=r,i=this.buildURL(n,a);"timeout"in r&&sg("timeout",r.timeout),r.timeout=r.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:r}),c=this.buildHeaders({options:e,method:s,bodyHeaders:o,retryCount:t});return{req:{method:s,headers:c,...r.signal&&{signal:r.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...r.fetchOptions??{}},url:i,timeout:r.timeout}}buildHeaders({options:e,method:t,bodyHeaders:r,retryCount:s}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=nq([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(s),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...sM(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,r,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let r=nq([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&r.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:sN(e)}:r6(this,tc,"f").call(this,{body:e,headers:r})}}tl=a$,tc=new WeakMap,a$.OpenAI=tl,a$.DEFAULT_TIMEOUT=6e5,a$.OpenAIError=r7,a$.APIError=se,a$.APIConnectionError=sr,a$.APIConnectionTimeoutError=ss,a$.APIUserAbortError=st,a$.NotFoundError=so,a$.ConflictError=sl,a$.RateLimitError=sd,a$.BadRequestError=sn,a$.AuthenticationError=sa,a$.InternalServerError=sh,a$.PermissionDeniedError=si,a$.UnprocessableEntityError=sc,a$.toFile=nm,a$.Completions=n5,a$.Chat=nW,a$.Embeddings=at,a$.Files=aa,a$.Images=am,a$.Audio=nX,a$.Moderations=ab,a$.Models=ag,a$.FineTuning=af,a$.Graders=ax,a$.VectorStores=aP,a$.Beta=n3,a$.Batches=nJ,a$.Uploads=aA,a$.Responses=ak,a$.Evals=an,a$.Containers=ae;class aj{static{this.claudeClient=null}static{this.openaiClient=null}static initializeClaude(e){this.claudeClient=new r1({apiKey:e})}static initializeOpenAI(e){this.openaiClient=new a$({apiKey:e})}static async testClaudeConnection(e){try{let t=new r1({apiKey:e}),r=await t.messages.create({model:"claude-3-haiku-20240307",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]});return"text"===r.content[0].type&&r.content[0].text.includes("OK")}catch(e){return console.error("Claude connection test failed:",e),!1}}static async testOpenAIConnection(e){try{let t=new a$({apiKey:e}),r=await t.chat.completions.create({model:"gpt-3.5-turbo",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]});return r.choices[0]?.message?.content?.includes("OK")||!1}catch(e){return console.error("OpenAI connection test failed:",e),!1}}static async generateWithClaude(e,t,r,s){if(!this.claudeClient)throw Error("Claude client not initialized");try{let n=this.buildFullPrompt(e,t,r,s),a=await this.claudeClient.messages.create({model:"claude-3-5-sonnet-latest",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:n}]});if("text"!==a.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(a.content[0].text)}catch(e){throw console.error("Claude generation failed:",e),Error(`Claude API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static async generateWithOpenAI(e,t,r,s){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{let n=this.buildFullPrompt(e,t,r,s),a=await this.openaiClient.chat.completions.create({model:"gpt-4",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts."},{role:"user",content:n}]}),i=a.choices[0]?.message?.content;if(!i)throw Error("No content received from OpenAI");return this.parseAIResponse(i)}catch(e){throw console.error("OpenAI generation failed:",e),Error(`OpenAI API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static buildFullPrompt(e,t,r,s){return`${e}

DOCUMENT CONTENTS:

Enterprise Need Document:
${t.enterpriseNeed}

Solution Description Document:
${t.solution}

Risk of No Investment Document:
${t.risk}

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).`}static parseAIResponse(e){let t=e.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i),r="",s="",n="";for(let e of t){let t=e.trim();t&&(t.toLowerCase().includes("enterprise need")?r=t:t.toLowerCase().includes("proposed solution")?s=t:t.toLowerCase().includes("risk of no investment")&&(n=t))}if(!r||!s||!n){let t=e.split(/\n\s*\n/);t.length>=3?(r=t[0]||"Enterprise Need artifact could not be parsed.",s=t[1]||"Solution artifact could not be parsed.",n=t[2]||"Risk artifact could not be parsed."):(r=`# Enterprise Need Artifact

${e}`,s=`# Proposed Solution Artifact

${e}`,n=`# Risk of No Investment Artifact

${e}`)}return{enterpriseNeedArtifact:r.trim(),solutionArtifact:s.trim(),riskArtifact:n.trim()}}static async generateWithClaudeFiles(e,t,r,s){if(!this.claudeClient)throw Error("Claude client not initialized");try{let r=[],s=(e,t)=>{let s=e.fileType.toLowerCase();if("pdf"===s)r.push({type:"document",source:{type:"base64",media_type:"application/pdf",data:e.base64Data}}),r.push({type:"text",text:`The above document contains the ${t} information.`});else if(["png","jpg","jpeg"].includes(s)){let n="jpg"===s?"image/jpeg":`image/${s}`;r.push({type:"image",source:{type:"base64",media_type:n,data:e.base64Data}}),r.push({type:"text",text:`The above image contains the ${t} information.`})}else r.push({type:"document",source:{type:"base64",media_type:this.getMimeType(s),data:e.base64Data}}),r.push({type:"text",text:`The above document contains the ${t} information.`})};s(t.enterpriseNeedFile,"Enterprise Need"),s(t.solutionFile,"Solution Description"),s(t.riskFile,"Risk of No Investment"),r.push({type:"text",text:`${e}

Please analyze the three attached documents and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

The first document contains the Enterprise Need information.
The second document contains the Solution Description information.
The third document contains the Risk of No Investment information.

Use only the content from these attached documents and do not add any other information other than the items requested in this prompt.`});let n=await this.claudeClient.messages.create({model:"claude-3-5-sonnet-20241022",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:r}]});if("text"!==n.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(n.content[0].text)}catch(e){throw console.error("Claude file generation failed:",e),Error(`Claude API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static async generateWithOpenAIFiles(e,t,r,s){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{let r=[];r.push({type:"text",text:`${e}

I have three documents that I need you to analyze. Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Documents to analyze:`});let s=(e,t)=>{let s=e.fileType.toLowerCase();["png","jpg","jpeg"].includes(s)?(r.push({type:"text",text:`
${t} Document (${e.fileName}):`}),r.push({type:"image_url",image_url:{url:`data:image/${"jpg"===s?"jpeg":s};base64,${e.base64Data}`}})):r.push({type:"text",text:`
${t} Document: ${e.fileName} (${s.toUpperCase()} file - please note that the content extraction may be limited for this file type)`})};s(t.enterpriseNeedFile,"Enterprise Need"),s(t.solutionFile,"Solution Description"),s(t.riskFile,"Risk of No Investment");let n=await this.openaiClient.chat.completions.create({model:"gpt-4-vision-preview",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts. You can analyze both text and image content to create comprehensive business documents."},{role:"user",content:r}]}),a=n.choices[0]?.message?.content;if(!a)throw Error("No content received from OpenAI");return this.parseAIResponse(a)}catch(e){throw console.error("OpenAI file generation failed:",e),Error(`OpenAI API error: ${e instanceof Error?e.message:"Unknown error"}`)}}static async generateArtifactsWithFilesRetry(e,t,r,s,n,a=3){let i=null;for(let o=1;o<=a;o++)try{if("claude"===e)return await this.generateWithClaudeFiles(t,r,s,n);return await this.generateWithOpenAIFiles(t,r,s,n)}catch(e){i=e instanceof Error?e:Error("Unknown error"),console.error(`AI file generation attempt ${o} failed:`,i),o<a&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o)))}throw Error(`AI file generation failed after ${a} attempts: ${i?.message}`)}static getMimeType(e){return({pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",doc:"application/msword",txt:"text/plain",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg"})[e.toLowerCase()]||"application/octet-stream"}static async generateArtifactsWithRetry(e,t,r,s,n,a=3){let i=null;for(let o=1;o<=a;o++)try{if("claude"===e)return await this.generateWithClaude(t,r,s,n);return await this.generateWithOpenAI(t,r,s,n)}catch(e){i=e instanceof Error?e:Error("Unknown error"),console.error(`AI generation attempt ${o} failed:`,i),o<a&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o)))}throw Error(`AI generation failed after ${a} attempts: ${i?.message}`)}}var aE=r(4765),aM=r.n(aE);let aO="main",aN="raviteja";class aB{static encryptApiKey(e){return aM().AES.encrypt(e,aN).toString()}static decryptApiKey(e){return aM().AES.decrypt(e,aN).toString(aM().enc.Utf8)}static async getAPIConfiguration(){try{let e=await (0,tx.x7)((0,tx.H9)(tm.db,"apiConfig",aO));if(!e.exists())return null;let t=e.data();return{id:e.id,claudeApiKey:t.claudeApiKey?this.decryptApiKey(t.claudeApiKey):void 0,openaiApiKey:t.openaiApiKey?this.decryptApiKey(t.openaiApiKey):void 0,createdAt:t.createdAt?.toDate()||new Date,updatedAt:t.updatedAt?.toDate()||new Date,claudeStatus:t.claudeStatus||"untested",openaiStatus:t.openaiStatus||"untested",lastTestedAt:t.lastTestedAt?.toDate(),claudeModelConfig:t.claudeModelConfig||{model:"claude-3-5-sonnet-latest",maxTokens:4e3,temperature:.7},openaiModelConfig:t.openaiModelConfig||{model:"gpt-4-vision-preview",maxTokens:4e3,temperature:.7}}}catch(e){throw console.error("Error fetching API configuration:",e),Error("Failed to fetch API configuration")}}static async saveAPIConfiguration(e){try{let t=(0,tx.H9)(tm.db,"apiConfig",aO),r=await (0,tx.x7)(t),s={updatedAt:tx.Dc.now()};e.claudeApiKey&&(s.claudeApiKey=this.encryptApiKey(e.claudeApiKey),s.claudeStatus="untested"),e.openaiApiKey&&(s.openaiApiKey=this.encryptApiKey(e.openaiApiKey),s.openaiStatus="untested"),e.claudeModelConfig&&(s.claudeModelConfig=e.claudeModelConfig),e.openaiModelConfig&&(s.openaiModelConfig=e.openaiModelConfig),r.exists()?await (0,tx.mZ)(t,s):await (0,tx.BN)(t,{...s,createdAt:tx.Dc.now()})}catch(e){throw console.error("Error saving API configuration:",e),Error("Failed to save API configuration")}}static async testAPIConnections(){try{let{getFunctions:e,httpsCallable:t}=await r.e(890).then(r.bind(r,24890)),s=e(),n=t(s,"testAPIConnectionsFunction");return(await n()).data}catch(e){return console.error("Error testing API connections:",e),{claudeStatus:"error",openaiStatus:"error",claudeError:e instanceof Error?e.message:"Failed to test connections",openaiError:e instanceof Error?e.message:"Failed to test connections"}}}static async getDecryptedAPIKeys(){try{let e=await this.getAPIConfiguration();return{claudeApiKey:e?.claudeApiKey,openaiApiKey:e?.openaiApiKey}}catch(e){throw console.error("Error getting decrypted API keys:",e),Error("Failed to get API keys")}}static async areAPIKeysConfigured(){try{let e=await this.getAPIConfiguration(),t=!!e?.claudeApiKey,r=!!e?.openaiApiKey;return{claudeConfigured:t,openaiConfigured:r,anyConfigured:t||r}}catch(e){return console.error("Error checking API key configuration:",e),{claudeConfigured:!1,openaiConfigured:!1,anyConfigured:!1}}}static async initializeAIServices(){try{let e=await this.getDecryptedAPIKeys();e.claudeApiKey&&aj.initializeClaude(e.claudeApiKey),e.openaiApiKey&&aj.initializeOpenAI(e.openaiApiKey)}catch(e){throw console.error("Error initializing AI services:",e),Error("Failed to initialize AI services")}}static validateAPIKey(e,t){if(!t||0===t.trim().length)return{isValid:!1,error:"API key cannot be empty"};if("claude"===e){if(!t.startsWith("sk-ant-"))return{isValid:!1,error:'Claude API key should start with "sk-ant-"'}}else if("openai"===e&&!t.startsWith("sk-"))return{isValid:!1,error:'OpenAI API key should start with "sk-"'};return t.length<20?{isValid:!1,error:"API key appears to be too short"}:{isValid:!0}}}function aT(){let{user:e,loading:t,isAdmin:r}=(0,tu.A)();(0,tf.useRouter)();let[s,n]=(0,th.useState)(null),[a,i]=(0,th.useState)(!0),[o,l]=(0,th.useState)(!1),[c,d]=(0,th.useState)(!1),[h,u]=(0,th.useState)(!1),[f,p]=(0,th.useState)({claudeApiKey:"",openaiApiKey:"",claudeModelConfig:{model:"claude-3-5-sonnet-latest",maxTokens:4e3,temperature:.7},openaiModelConfig:{model:"gpt-4-vision-preview",maxTokens:4e3,temperature:.7}}),[x,m]=(0,th.useState)({}),g=async()=>{try{i(!0);let e=await aB.getAPIConfiguration();n(e),e&&(p({claudeApiKey:e.claudeApiKey||"",openaiApiKey:e.openaiApiKey||"",claudeModelConfig:e.claudeModelConfig||{model:"claude-3-5-sonnet-latest",maxTokens:4e3,temperature:.7},openaiModelConfig:e.openaiModelConfig||{model:"gpt-4-vision-preview",maxTokens:4e3,temperature:.7}}),m({claudeStatus:e.claudeStatus,openaiStatus:e.openaiStatus}))}catch(e){console.error("Error fetching API config:",e),alert("Error loading API configuration")}finally{i(!1)}},b=async()=>{try{if(l(!0),f.claudeApiKey){let e=aB.validateAPIKey("claude",f.claudeApiKey);if(!e.isValid)return void alert(`Claude API Key Error: ${e.error}`)}if(f.openaiApiKey){let e=aB.validateAPIKey("openai",f.openaiApiKey);if(!e.isValid)return void alert(`OpenAI API Key Error: ${e.error}`)}await aB.saveAPIConfiguration({claudeApiKey:f.claudeApiKey||void 0,openaiApiKey:f.openaiApiKey||void 0,claudeModelConfig:f.claudeModelConfig,openaiModelConfig:f.openaiModelConfig}),alert("API configuration saved successfully!"),await g()}catch(e){console.error("Error saving API config:",e),alert("Error saving API configuration")}finally{l(!1)}},y=async()=>{try{d(!0);let e=await aB.testAPIConnections();m(e);let t=[];"connected"===e.claudeStatus?t.push("✅ Claude API: Connected successfully"):"error"===e.claudeStatus?t.push(`❌ Claude API: ${e.claudeError||"Connection failed"}`):t.push("⚪ Claude API: Not configured"),"connected"===e.openaiStatus?t.push("✅ OpenAI API: Connected successfully"):"error"===e.openaiStatus?t.push(`❌ OpenAI API: ${e.openaiError||"Connection failed"}`):t.push("⚪ OpenAI API: Not configured"),alert("API Connection Test Results:\n\n"+t.join("\n"))}catch(e){console.error("Error testing connections:",e),alert("Error testing API connections. Please try again.")}finally{d(!1)}},w=e=>{switch(e){case"connected":return(0,td.jsx)("span",{className:"text-green-500",children:"✓ Connected"});case"error":return(0,td.jsx)("span",{className:"text-red-500",children:"✗ Error"});default:return(0,td.jsx)("span",{className:"text-gray-500",children:"○ Untested"})}};return t||a?(0,td.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,td.jsx)("div",{className:"text-xl",children:"Loading..."})}):r?(0,td.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,td.jsx)(tp.A,{}),(0,td.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,td.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,td.jsxs)("div",{className:"mb-8",children:[(0,td.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Settings"}),(0,td.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Configure API keys and system settings for VALTICS AI."})]}),(0,td.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,td.jsxs)("div",{className:"p-6",children:[(0,td.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,td.jsxs)("div",{children:[(0,td.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"AI API Configuration"}),(0,td.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Configure API keys for Claude AI and OpenAI to enable AI artifact generation."})]}),(0,td.jsxs)("button",{onClick:()=>u(!h),className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:[h?"Hide":"Show"," API Keys"]})]}),(0,td.jsxs)("div",{className:"space-y-6",children:[(0,td.jsxs)("div",{children:[(0,td.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Claude AI API Key"}),w(x.claudeStatus)]}),(0,td.jsx)("input",{type:h?"text":"password",value:f.claudeApiKey,onChange:e=>p(t=>({...t,claudeApiKey:e.target.value})),placeholder:"sk-ant-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,td.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,td.jsx)("a",{href:"https://console.anthropic.com/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"Anthropic Console"})]})]}),(0,td.jsxs)("div",{children:[(0,td.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"OpenAI API Key"}),w(x.openaiStatus)]}),(0,td.jsx)("input",{type:h?"text":"password",value:f.openaiApiKey,onChange:e=>p(t=>({...t,openaiApiKey:e.target.value})),placeholder:"sk-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,td.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,td.jsx)("a",{href:"https://platform.openai.com/api-keys",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"OpenAI Platform"})]})]}),(0,td.jsxs)("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,td.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Model Configuration"}),(0,td.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"Configure the AI models and parameters used for artifact generation."}),(0,td.jsxs)("div",{className:"mb-6",children:[(0,td.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Claude AI Configuration"}),(0,td.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,td.jsxs)("div",{children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Model"}),(0,td.jsxs)("select",{value:f.claudeModelConfig.model,onChange:e=>p(t=>({...t,claudeModelConfig:{...t.claudeModelConfig,model:e.target.value}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,td.jsx)("option",{value:"claude-opus-4-0",children:"Claude Opus 4 (Latest)"}),(0,td.jsx)("option",{value:"claude-sonnet-4-0",children:"Claude Sonnet 4 (Latest)"}),(0,td.jsx)("option",{value:"claude-3-7-sonnet-latest",children:"Claude Sonnet 3.7 (Latest)"}),(0,td.jsx)("option",{value:"claude-3-5-sonnet-latest",children:"Claude 3.5 Sonnet (Latest)"}),(0,td.jsx)("option",{value:"claude-3-5-haiku-latest",children:"Claude 3.5 Haiku (Latest)"}),(0,td.jsx)("option",{value:"claude-3-opus-latest",children:"Claude 3 Opus (Latest)"}),(0,td.jsx)("option",{value:"claude-3-5-sonnet-20241022",children:"Claude 3.5 Sonnet (Oct 2024)"}),(0,td.jsx)("option",{value:"claude-3-5-haiku-20241022",children:"Claude 3.5 Haiku (Oct 2024)"}),(0,td.jsx)("option",{value:"claude-3-opus-20240229",children:"Claude 3 Opus (Feb 2024)"})]})]}),(0,td.jsxs)("div",{children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Max Tokens"}),(0,td.jsx)("input",{type:"number",min:"100",max:"8000",value:f.claudeModelConfig.maxTokens,onChange:e=>p(t=>({...t,claudeModelConfig:{...t.claudeModelConfig,maxTokens:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,td.jsxs)("div",{children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Temperature"}),(0,td.jsx)("input",{type:"number",min:"0",max:"1",step:"0.1",value:f.claudeModelConfig.temperature,onChange:e=>p(t=>({...t,claudeModelConfig:{...t.claudeModelConfig,temperature:parseFloat(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]}),(0,td.jsxs)("div",{className:"mb-6",children:[(0,td.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"OpenAI Configuration"}),(0,td.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,td.jsxs)("div",{children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Model"}),(0,td.jsxs)("select",{value:f.openaiModelConfig.model,onChange:e=>p(t=>({...t,openaiModelConfig:{...t.openaiModelConfig,model:e.target.value}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,td.jsx)("option",{value:"gpt-4-vision-preview",children:"GPT-4 Vision Preview"}),(0,td.jsx)("option",{value:"gpt-4-turbo-preview",children:"GPT-4 Turbo Preview"}),(0,td.jsx)("option",{value:"gpt-4",children:"GPT-4"}),(0,td.jsx)("option",{value:"gpt-4-32k",children:"GPT-4 32K"}),(0,td.jsx)("option",{value:"gpt-3.5-turbo",children:"GPT-3.5 Turbo"})]})]}),(0,td.jsxs)("div",{children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Max Tokens"}),(0,td.jsx)("input",{type:"number",min:"100",max:"8000",value:f.openaiModelConfig.maxTokens,onChange:e=>p(t=>({...t,openaiModelConfig:{...t.openaiModelConfig,maxTokens:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,td.jsxs)("div",{children:[(0,td.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Temperature"}),(0,td.jsx)("input",{type:"number",min:"0",max:"1",step:"0.1",value:f.openaiModelConfig.temperature,onChange:e=>p(t=>({...t,openaiModelConfig:{...t.openaiModelConfig,temperature:parseFloat(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]})]}),(0,td.jsxs)("div",{className:"flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,td.jsxs)("button",{onClick:b,disabled:o,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[o&&(0,td.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,td.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,td.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,td.jsx)("span",{children:o?"Saving...":"Save Configuration"})]}),(0,td.jsxs)("button",{onClick:y,disabled:c||!f.claudeApiKey&&!f.openaiApiKey,className:"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[c&&(0,td.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,td.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,td.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,td.jsx)("span",{children:c?"Testing...":"Test Connections"})]})]}),s?.lastTestedAt&&(0,td.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Last tested: ",s.lastTestedAt.toLocaleString()]})]})]})}),(0,td.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md",children:(0,td.jsxs)("div",{className:"flex",children:[(0,td.jsx)("div",{className:"flex-shrink-0",children:(0,td.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,td.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,td.jsxs)("div",{className:"ml-3",children:[(0,td.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Security Notice"}),(0,td.jsx)("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:(0,td.jsx)("p",{children:"API keys are encrypted before storage and are only accessible to admin users. Keep your API keys secure and rotate them regularly. Never share your API keys with unauthorized users."})})]})]})})]})})]}):null}},76910:function(e,t,r){e.exports=function(e){var t=e.lib.WordArray,r=e.enc;function s(e){return e<<8&0xff00ff00|e>>>8&0xff00ff}return r.Utf16=r.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],n=0;n<r;n+=2){var a=t[n>>>2]>>>16-n%4*8&65535;s.push(String.fromCharCode(a))}return s.join("")},parse:function(e){for(var r=e.length,s=[],n=0;n<r;n++)s[n>>>1]|=e.charCodeAt(n)<<16-n%2*16;return t.create(s,2*r)}},r.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],a=0;a<r;a+=2){var i=s(t[a>>>2]>>>16-a%4*8&65535);n.push(String.fromCharCode(i))}return n.join("")},parse:function(e){for(var r=e.length,n=[],a=0;a<r;a++)n[a>>>1]|=s(e.charCodeAt(a)<<16-a%2*16);return t.create(n,2*r)}},e.enc.Utf16}(r(98846))},77026:function(e,t,r){var s;s=r(98846),r(86348),s.mode.CFB=function(){var e=s.lib.BlockCipherMode.extend();function t(e,t,r,s){var n,a=this._iv;a?(n=a.slice(0),this._iv=void 0):n=this._prevBlock,s.encryptBlock(n,0);for(var i=0;i<r;i++)e[t+i]^=n[i]}return e.Encryptor=e.extend({processBlock:function(e,r){var s=this._cipher,n=s.blockSize;t.call(this,e,r,n,s),this._prevBlock=e.slice(r,r+n)}}),e.Decryptor=e.extend({processBlock:function(e,r){var s=this._cipher,n=s.blockSize,a=e.slice(r,r+n);t.call(this,e,r,n,s),this._prevBlock=a}}),e}(),e.exports=s.mode.CFB},77089:function(e,t,r){var s;s=r(98846),r(28165),r(40465),r(24613),r(86348),function(){var e=s.lib.StreamCipher,t=s.algo,r=[],n=[],a=[],i=t.RabbitLegacy=e.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],s=this._C=[e[2]<<16|e[2]>>>16,0xffff0000&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,0xffff0000&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,0xffff0000&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,0xffff0000&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)o.call(this);for(var n=0;n<8;n++)s[n]^=r[n+4&7];if(t){var a=t.words,i=a[0],l=a[1],c=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,d=(l<<8|l>>>24)&0xff00ff|(l<<24|l>>>8)&0xff00ff00,h=c>>>16|0xffff0000&d,u=d<<16|65535&c;s[0]^=c,s[1]^=h,s[2]^=d,s[3]^=u,s[4]^=c,s[5]^=h,s[6]^=d,s[7]^=u;for(var n=0;n<4;n++)o.call(this)}},_doProcessBlock:function(e,t){var s=this._X;o.call(this),r[0]=s[0]^s[5]>>>16^s[3]<<16,r[1]=s[2]^s[7]>>>16^s[5]<<16,r[2]=s[4]^s[1]>>>16^s[7]<<16,r[3]=s[6]^s[3]>>>16^s[1]<<16;for(var n=0;n<4;n++)r[n]=(r[n]<<8|r[n]>>>24)&0xff00ff|(r[n]<<24|r[n]>>>8)&0xff00ff00,e[t+n]^=r[n]},blockSize:4,ivSize:2});function o(){for(var e=this._X,t=this._C,r=0;r<8;r++)n[r]=t[r];t[0]=t[0]+0x4d34d34d+this._b|0,t[1]=t[1]+0xd34d34d3+ +(t[0]>>>0<n[0]>>>0)|0,t[2]=t[2]+0x34d34d34+ +(t[1]>>>0<n[1]>>>0)|0,t[3]=t[3]+0x4d34d34d+ +(t[2]>>>0<n[2]>>>0)|0,t[4]=t[4]+0xd34d34d3+ +(t[3]>>>0<n[3]>>>0)|0,t[5]=t[5]+0x34d34d34+ +(t[4]>>>0<n[4]>>>0)|0,t[6]=t[6]+0x4d34d34d+ +(t[5]>>>0<n[5]>>>0)|0,t[7]=t[7]+0xd34d34d3+ +(t[6]>>>0<n[6]>>>0)|0,this._b=+(t[7]>>>0<n[7]>>>0);for(var r=0;r<8;r++){var s=e[r]+t[r],i=65535&s,o=s>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((0xffff0000&s)*s|0)+((65535&s)*s|0);a[r]=l^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}s.RabbitLegacy=e._createHelper(i)}(),e.exports=s.RabbitLegacy},78978:function(e,t,r){var s;s=r(98846),r(23135),function(e){var t=s.lib,r=t.WordArray,n=t.Hasher,a=s.x64.Word,i=s.algo,o=[],l=[],c=[];!function(){for(var e=1,t=0,r=0;r<24;r++){o[e+5*t]=(r+1)*(r+2)/2%64;var s=t%5,n=(2*e+3*t)%5;e=s,t=n}for(var e=0;e<5;e++)for(var t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,d=0;d<24;d++){for(var h=0,u=0,f=0;f<7;f++){if(1&i){var p=(1<<f)-1;p<32?u^=1<<p:h^=1<<p-32}128&i?i=i<<1^113:i<<=1}c[d]=a.create(h,u)}}();for(var d=[],h=0;h<25;h++)d[h]=a.create();var u=i.SHA3=n.extend({cfg:n.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,s=this.blockSize/2,n=0;n<s;n++){var a=e[t+2*n],i=e[t+2*n+1];a=(a<<8|a>>>24)&0xff00ff|(a<<24|a>>>8)&0xff00ff00,i=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00;var h=r[n];h.high^=i,h.low^=a}for(var u=0;u<24;u++){for(var f=0;f<5;f++){for(var p=0,x=0,m=0;m<5;m++){var h=r[f+5*m];p^=h.high,x^=h.low}var g=d[f];g.high=p,g.low=x}for(var f=0;f<5;f++)for(var b=d[(f+4)%5],y=d[(f+1)%5],w=y.high,_=y.low,p=b.high^(w<<1|_>>>31),x=b.low^(_<<1|w>>>31),m=0;m<5;m++){var h=r[f+5*m];h.high^=p,h.low^=x}for(var v=1;v<25;v++){var p,x,h=r[v],k=h.high,S=h.low,A=o[v];A<32?(p=k<<A|S>>>32-A,x=S<<A|k>>>32-A):(p=S<<A-32|k>>>64-A,x=k<<A-32|S>>>64-A);var R=d[l[v]];R.high=p,R.low=x}var I=d[0],C=r[0];I.high=C.high,I.low=C.low;for(var f=0;f<5;f++)for(var m=0;m<5;m++){var v=f+5*m,h=r[v],P=d[v],$=d[(f+1)%5+5*m],j=d[(f+2)%5+5*m];h.high=P.high^~$.high&j.high,h.low=P.low^~$.low&j.low}var h=r[0],E=c[u];h.high^=E.high,h.low^=E.low}},_doFinalize:function(){var t=this._data,s=t.words;this._nDataBytes;var n=8*t.sigBytes,a=32*this.blockSize;s[n>>>5]|=1<<24-n%32,s[(e.ceil((n+1)/a)*a>>>5)-1]|=128,t.sigBytes=4*s.length,this._process();for(var i=this._state,o=this.cfg.outputLength/8,l=o/8,c=[],d=0;d<l;d++){var h=i[d],u=h.high,f=h.low;u=(u<<8|u>>>24)&0xff00ff|(u<<24|u>>>8)&0xff00ff00,f=(f<<8|f>>>24)&0xff00ff|(f<<24|f>>>8)&0xff00ff00,c.push(f),c.push(u)}return new r.init(c,o)},clone:function(){for(var e=n.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});s.SHA3=n._createHelper(u),s.HmacSHA3=n._createHmacHelper(u)}(Math),e.exports=s.SHA3},79551:e=>{"use strict";e.exports=require("url")},81381:function(e,t,r){var s,n;s=r(98846),r(86348),s.mode.ECB=((n=s.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),n.Decryptor=n.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),n),e.exports=s.mode.ECB},81630:e=>{"use strict";e.exports=require("http")},81881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\settings\\page.tsx","default")},86348:function(e,t,r){var s,n,a,i,o,l,c,d,h,u,f,p,x,m,g,b,y,w;s=r(98846),r(24613),e.exports=void(s.lib.Cipher||(a=(n=s.lib).Base,i=n.WordArray,o=n.BufferedBlockAlgorithm,(l=s.enc).Utf8,c=l.Base64,d=s.algo.EvpKDF,h=n.Cipher=o.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?w:b}return function(t){return{encrypt:function(r,s,n){return e(s).encrypt(t,r,s,n)},decrypt:function(r,s,n){return e(s).decrypt(t,r,s,n)}}}}()}),n.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),u=s.mode={},f=n.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=u.CBC=function(){var e=f.extend();function t(e,t,r){var s,n=this._iv;n?(s=n,this._iv=void 0):s=this._prevBlock;for(var a=0;a<r;a++)e[t+a]^=s[a]}return e.Encryptor=e.extend({processBlock:function(e,r){var s=this._cipher,n=s.blockSize;t.call(this,e,r,n),s.encryptBlock(e,r),this._prevBlock=e.slice(r,r+n)}}),e.Decryptor=e.extend({processBlock:function(e,r){var s=this._cipher,n=s.blockSize,a=e.slice(r,r+n);s.decryptBlock(e,r),t.call(this,e,r,n),this._prevBlock=a}}),e}(),x=(s.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,s=r-e.sigBytes%r,n=s<<24|s<<16|s<<8|s,a=[],o=0;o<s;o+=4)a.push(n);var l=i.create(a,s);e.concat(l)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},n.BlockCipher=h.extend({cfg:h.cfg.extend({mode:p,padding:x}),reset:function(){h.reset.call(this);var e,t=this.cfg,r=t.iv,s=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=s.createEncryptor:(e=s.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(s,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),m=n.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),g=(s.format={}).OpenSSL={stringify:function(e){var t,r=e.ciphertext,s=e.salt;return(s?i.create([0x53616c74,0x65645f5f]).concat(s).concat(r):r).toString(c)},parse:function(e){var t,r=c.parse(e),s=r.words;return 0x53616c74==s[0]&&0x65645f5f==s[1]&&(t=i.create(s.slice(2,4)),s.splice(0,4),r.sigBytes-=16),m.create({ciphertext:r,salt:t})}},b=n.SerializableCipher=a.extend({cfg:a.extend({format:g}),encrypt:function(e,t,r,s){s=this.cfg.extend(s);var n=e.createEncryptor(r,s),a=n.finalize(t),i=n.cfg;return m.create({ciphertext:a,key:r,iv:i.iv,algorithm:e,mode:i.mode,padding:i.padding,blockSize:e.blockSize,formatter:s.format})},decrypt:function(e,t,r,s){return s=this.cfg.extend(s),t=this._parse(t,s.format),e.createDecryptor(r,s).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(s.kdf={}).OpenSSL={execute:function(e,t,r,s,n){if(s||(s=i.random(8)),n)var a=d.create({keySize:t+r,hasher:n}).compute(e,s);else var a=d.create({keySize:t+r}).compute(e,s);var o=i.create(a.words.slice(t),4*r);return a.sigBytes=4*t,m.create({key:a,iv:o,salt:s})}},w=n.PasswordBasedCipher=b.extend({cfg:b.cfg.extend({kdf:y}),encrypt:function(e,t,r,s){var n=(s=this.cfg.extend(s)).kdf.execute(r,e.keySize,e.ivSize,s.salt,s.hasher);s.iv=n.iv;var a=b.encrypt.call(this,e,t,n.key,s);return a.mixIn(n),a},decrypt:function(e,t,r,s){s=this.cfg.extend(s),t=this._parse(t,s.format);var n=s.kdf.execute(r,e.keySize,e.ivSize,t.salt,s.hasher);return s.iv=n.iv,b.decrypt.call(this,e,t,n.key,s)}})))},88072:(e,t,r)=>{Promise.resolve().then(r.bind(r,81881))},88822:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81881)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\settings\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\settings\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},91824:function(e,t,r){var s;s=r(98846),r(28165),r(40465),r(24613),r(86348),function(){var e=s.lib.StreamCipher,t=s.algo,r=t.RC4=e.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,s=this._S=[],n=0;n<256;n++)s[n]=n;for(var n=0,a=0;n<256;n++){var i=n%r,o=t[i>>>2]>>>24-i%4*8&255;a=(a+s[n]+o)%256;var l=s[n];s[n]=s[a],s[a]=l}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=n.call(this)},keySize:8,ivSize:0});function n(){for(var e=this._S,t=this._i,r=this._j,s=0,n=0;n<4;n++){r=(r+e[t=(t+1)%256])%256;var a=e[t];e[t]=e[r],e[r]=a,s|=e[(e[t]+e[r])%256]<<24-8*n}return this._i=t,this._j=r,s}s.RC4=e._createHelper(r);var a=t.RC4Drop=r.extend({cfg:r.cfg.extend({drop:192}),_doReset:function(){r._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)n.call(this)}});s.RC4Drop=e._createHelper(a)}(),e.exports=s.RC4},94326:function(e,t,r){var s,n,a;s=r(98846),r(86348),s.mode.OFB=(a=(n=s.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){var r=this._cipher,s=r.blockSize,n=this._iv,a=this._keystream;n&&(a=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(a,0);for(var i=0;i<s;i++)e[t+i]^=a[i]}}),n.Decryptor=a,n),e.exports=s.mode.OFB},94735:e=>{"use strict";e.exports=require("events")},98846:function(e,t,r){var s;e.exports=s||function(e,t){if("undefined"!=typeof window&&window.crypto&&(s=window.crypto),"undefined"!=typeof self&&self.crypto&&(s=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(s=globalThis.crypto),!s&&"undefined"!=typeof window&&window.msCrypto&&(s=window.msCrypto),!s&&"undefined"!=typeof global&&global.crypto&&(s=global.crypto),!s)try{s=r(55511)}catch(e){}var s,n=function(){if(s){if("function"==typeof s.getRandomValues)try{return s.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof s.randomBytes)try{return s.randomBytes(4).readInt32LE()}catch(e){}}throw Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),i={},o=i.lib={},l=o.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=o.WordArray=l.extend({init:function(e,r){e=this.words=e||[],t!=r?this.sigBytes=r:this.sigBytes=4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,r=e.words,s=this.sigBytes,n=e.sigBytes;if(this.clamp(),s%4)for(var a=0;a<n;a++){var i=r[a>>>2]>>>24-a%4*8&255;t[s+a>>>2]|=i<<24-(s+a)%4*8}else for(var o=0;o<n;o+=4)t[s+o>>>2]=r[o>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=0xffffffff<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=l.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(n());return new c.init(t,e)}}),d=i.enc={},h=d.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],n=0;n<r;n++){var a=t[n>>>2]>>>24-n%4*8&255;s.push((a>>>4).toString(16)),s.push((15&a).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,r=[],s=0;s<t;s+=2)r[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new c.init(r,t/2)}},u=d.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,s=[],n=0;n<r;n++){var a=t[n>>>2]>>>24-n%4*8&255;s.push(String.fromCharCode(a))}return s.join("")},parse:function(e){for(var t=e.length,r=[],s=0;s<t;s++)r[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new c.init(r,t)}},f=d.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=l.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,s=this._data,n=s.words,a=s.sigBytes,i=this.blockSize,o=a/(4*i),l=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*i,d=e.min(4*l,a);if(l){for(var h=0;h<l;h+=i)this._doProcessBlock(n,h);r=n.splice(0,l),s.sigBytes-=d}return new c.init(r,d)},clone:function(){var e=l.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=p.extend({cfg:l.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new x.HMAC.init(e,r).finalize(t)}}});var x=i.algo={};return i}(Math)},99305:function(e,t,r){var s;s=r(98846),r(86348),s.pad.NoPadding={pad:function(){},unpad:function(){}},e.exports=s.pad.NoPadding}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567,533,77],()=>r(88822));module.exports=s})();