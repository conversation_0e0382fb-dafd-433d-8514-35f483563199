"use strict";exports.id=137,exports.ids=[137],exports.modules={2193:(e,r,t)=>{t.d(r,{Ay:()=>i,eF:()=>n});var a=t(60687),l=t(85814),d=t.n(l),s=t(51108);function i({children:e,feature:r,showUpgrade:t=!0}){let{user:l,canAccessPremiumFeatures:i}=(0,s.A)();return i?(0,a.jsx)(a.Fragment,{children:e}):l?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("div",{className:"text-red-400 dark:text-red-300 text-6xl mb-4",children:"⏰"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Trial Expired"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:["Your free trial has ended. Upgrade to continue using ",r," and other premium features."]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"What you'll get with a subscription:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[(0,a.jsx)("li",{children:"• Unlimited BVA creation"}),(0,a.jsx)("li",{children:"• Access to all templates"}),(0,a.jsx)("li",{children:"• Advanced analytics"}),(0,a.jsx)("li",{children:"• Priority support"}),(0,a.jsx)("li",{children:"• Export to multiple formats"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[t&&(0,a.jsx)(d(),{href:"/pricing",className:"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block",children:"Upgrade Now"}),(0,a.jsx)(d(),{href:"/dashboard",className:"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block",children:"Back to Dashboard"})]})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-6xl mb-4",children:"\uD83D\uDD12"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Authentication Required"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:["Please log in to access ",r,"."]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d(),{href:"/login",className:"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block",children:"Log In"}),(0,a.jsx)(d(),{href:"/register",className:"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block",children:"Start Free Trial"})]})]})})}function n(){let{user:e,canAccessPremiumFeatures:r}=(0,s.A)();return{hasAccess:r,isTrialUser:e?.isTrialUser||!1,isTrialExpired:e?.trialExpired||!1,user:e}}},5481:(e,r,t)=>{t.d(r,{A:()=>c});var a=t(60687),l=t(85814),d=t.n(l),s=t(30474),i=t(51108),n=t(16189),o=t(27436),x=t(31769);function c({title:e="VALTICS AI",showBackButton:r=!1,backUrl:t="/dashboard",backText:l="← Back to Dashboard"}){let{user:c,logOut:u,isAdmin:g}=(0,i.A)(),h=(0,n.useRouter)(),m=async()=>{try{await u(),h.push("/")}catch(e){console.error("Error logging out:",e)}};return c?(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,a.jsx)(s.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[r&&(0,a.jsx)(d(),{href:t,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:l}),!r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,a.jsx)(d(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),g&&(0,a.jsx)(d(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,a.jsx)(d(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,a.jsx)(x.I,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)("button",{onClick:m,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},16189:(e,r,t)=>{var a=t(65773);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},30474:(e,r,t)=>{t.d(r,{default:()=>l.a});var a=t(31261),l=t.n(a)},31261:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return n},getImageProps:function(){return i}});let a=t(37366),l=t(44953),d=t(46533),s=a._(t(1933));function i(e){let{props:r}=(0,l.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let n=d.Image},31769:(e,r,t)=>{t.d(r,{A:()=>o,I:()=>x});var a=t(60687),l=t(43210),d=t(85814),s=t.n(d),i=t(51108),n=t(53836);function o(){let{user:e}=(0,i.A)(),[r,t]=(0,l.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||r)return null;let{message:d,type:o,daysRemaining:x}=(0,n.Mo)(e);if(!d)return null;let c=()=>{switch(o){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,a.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(o){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"error"===o?(0,a.jsx)("svg",{className:`h-5 w-5 ${c()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===o?(0,a.jsx)("svg",{className:`h-5 w-5 ${c()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:`h-5 w-5 ${c()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:d})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(s(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(o){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,a.jsx)("button",{onClick:()=>t(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function x(){let{user:e}=(0,i.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:r}=(0,n.Mo)(e);return r<=0?(0,a.jsx)(s(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,a.jsxs)(s(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${r<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[r," day",1===r?"":"s"," left"]})}}};