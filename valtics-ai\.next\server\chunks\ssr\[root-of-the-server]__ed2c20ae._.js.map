{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nexport default function Login() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const { signIn, signInWithGoogle, signInWithFacebook } = useAuth();\n  const router = useRouter();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    try {\n      await signIn(email, password);\n      router.push('/dashboard');\n    } catch (error: any) {\n      setError(error.message);\n    }\n  };\n\n  const handleGoogleSignIn = async () => {\n    try {\n      await signInWithGoogle();\n      router.push('/dashboard');\n    } catch (error: any) {\n      setError(error.message);\n    }\n  };\n\n  const handleFacebookSignIn = async () => {\n    try {\n      await signInWithFacebook();\n      router.push('/dashboard');\n    } catch (error: any) {\n      setError(error.message);\n    }\n  };\n\n  return (\n    <div className=\"flex min-h-screen flex-col items-center justify-center p-24 bg-gray-50 dark:bg-gray-900\">\n      <div className=\"w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            <Image\n              src=\"/logo.png\"\n              alt=\"VALTICS AI Logo\"\n              width={64}\n              height={64}\n              className=\"w-16 h-16\"\n            />\n          </div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Login to VALTICS AI</h1>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded\">\n            {error}\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Email\n            </label>\n            <input\n              id=\"email\"\n              name=\"email\"\n              type=\"email\"\n              required\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Password\n            </label>\n            <input\n              id=\"password\"\n              name=\"password\"\n              type=\"password\"\n              required\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Sign in\n            </button>\n          </div>\n        </form>\n\n        <div className=\"mt-6\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300 dark:border-gray-600\"></div>\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400\">Or continue with</span>\n            </div>\n          </div>\n\n          <div className=\"mt-6 grid grid-cols-2 gap-3\">\n            <button\n              onClick={handleGoogleSignIn}\n              className=\"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600\"\n            >\n              Google\n            </button>\n            <button\n              onClick={handleFacebookSignIn}\n              className=\"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600\"\n            >\n              Facebook\n            </button>\n          </div>\n        </div>\n\n        <div className=\"text-center mt-4\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            Don't have an account?{' '}\n            <Link href=\"/register\" className=\"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300\">\n              Register\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC/D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI;YACF,MAAM,OAAO,OAAO;YACpB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;;;;;;;gBAGlE,uBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA6D;;;;;;8CAG9F,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;;;;;;;;;;;;sCAId,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA6D;;;;;;8CAGjG,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;;;;;;;;;;;;sCAId,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAkE;;;;;;;;;;;;;;;;;sCAItF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA2C;4BAC/B;0CACvB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzI", "debugId": null}}]}