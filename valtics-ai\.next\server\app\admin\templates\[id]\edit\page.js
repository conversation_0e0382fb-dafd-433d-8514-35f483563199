(()=>{var e={};e.id=9,e.ids=[9],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41765:(e,t,r)=>{Promise.resolve().then(r.bind(r,64118))},55511:e=>{"use strict";e.exports=require("crypto")},59286:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),i=r(51108),l=r(16189),n=r(85814),d=r.n(n),o=r(75535),c=r(56304),m=r(5481),u=r(49615),p=r(30457);let x=({onFileSelect:e,onFileRemove:t,currentFileUrl:r,currentFileName:i,accept:l=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png",maxSize:n=10,disabled:d=!1,className:o=""})=>{let[c,m]=(0,a.useState)(!1),[u,p]=(0,a.useState)(!1),x=(0,a.useRef)(null),g=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?m(!0):"dragleave"===e.type&&m(!1)},h=t=>{if(t.size>1024*n*1024)return void alert(`File size must be less than ${n}MB`);let r=l.split(",").map(e=>e.trim()),s="."+t.name.split(".").pop()?.toLowerCase();if(!(r.some(e=>e===s||e.includes("/")&&t.type===e)||["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","image/jpeg","image/jpg","image/png"].includes(t.type)))return void alert(`Please select a valid file type: ${l}`);e(t)},b=e=>[".jpg",".jpeg",".png"].includes("."+e.split(".").pop()?.toLowerCase());return(0,s.jsxs)("div",{className:`file-upload ${o}`,children:[(0,s.jsx)("input",{ref:x,type:"file",accept:l,onChange:e=>{let t=e.target.files;t&&t[0]&&h(t[0])},className:"hidden",disabled:d}),r||i?(0,s.jsx)("div",{className:"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-4 bg-green-50 dark:bg-green-900/20",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:i&&b(i)&&r?(0,s.jsxs)("div",{className:"w-16 h-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600",children:[(0,s.jsx)("img",{src:r,alt:i,className:"w-full h-full object-cover",onError:e=>{let t=e.target;t.style.display="none",t.nextElementSibling?.classList.remove("hidden")}}),(0,s.jsx)("svg",{className:"hidden h-16 w-16 text-green-600 dark:text-green-400 p-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})]}):(0,s.jsx)("svg",{className:"h-8 w-8 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:i||"File uploaded"}),(0,s.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400",children:i&&b(i)?"Image uploaded successfully":"Document uploaded successfully"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[r&&(0,s.jsx)("a",{href:r,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View"}),(0,s.jsx)("button",{type:"button",onClick:()=>{x.current&&(x.current.value=""),t()},disabled:d,className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium disabled:opacity-50",children:"Remove"})]})]})}):(0,s.jsx)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${c?"border-blue-400 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"} ${d?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDragEnter:g,onDragLeave:g,onDragOver:g,onDrop:e=>{if(e.preventDefault(),e.stopPropagation(),m(!1),d)return;let t=e.dataTransfer.files;t&&t[0]&&h(t[0])},onClick:()=>{!d&&x.current&&x.current.click()},children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,s.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("span",{className:"font-medium text-blue-600 dark:text-blue-400",children:"Click to upload"})," ","or drag and drop"]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Documents & Images (",l.replace(/\./g,"").toUpperCase(),") up to ",n,"MB"]})]})}),u&&(0,s.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400",children:[(0,s.jsxs)("svg",{className:"animate-spin h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{children:"Uploading..."})]})]})};var g=r(70146);class h{static async uploadFile(e,t,r){try{let r=(0,g.KR)(c.IG,t),s=await (0,g.D)(r,e);return await (0,g.qk)(s.ref)}catch(e){throw console.error("Error uploading file:",e),Error("Failed to upload file")}}static async uploadTemplateDocument(e,t,r){let s=Date.now(),a=e.name.split(".").pop(),i=`${r}_${s}.${a}`,l=`templates/${t}/documents/${i}`;return this.uploadFile(e,l)}static async deleteFile(e){try{let t=e.split("/"),r=t.findIndex(e=>"o"===e)+1;if(0===r)throw Error("Invalid Firebase Storage URL");let s=t[r].split("?")[0],a=decodeURIComponent(s),i=(0,g.KR)(c.IG,a);await (0,g.XR)(i)}catch(e){throw console.error("Error deleting file:",e),Error("Failed to delete file")}}static getFileNameFromUrl(e){try{let t=e.split("/"),r=t.findIndex(e=>"o"===e)+1;if(0===r)return"Unknown file";let s=t[r].split("?")[0],a=decodeURIComponent(s).split("/").pop()||"Unknown file",i=a.split("_");if(i.length>1&&!isNaN(Number(i[i.length-1].split(".")[0])))return i.slice(0,-1).join("_")+"."+a.split(".").pop();return a}catch(e){return console.error("Error parsing file name from URL:",e),"Unknown file"}}static async uploadAIArtifact(e,t,r,s=Date.now()){try{let a=`${r}-${s}.md`,i=`templates/${t}/artifacts/${a}`,l=new Blob([e],{type:"text/markdown"}),n=(0,g.KR)(c.IG,i),d=await (0,g.D)(n,l);return await (0,g.qk)(d.ref)}catch(e){throw console.error("Error uploading AI artifact:",e),Error("Failed to upload AI artifact")}}static async uploadAIArtifacts(e,t){try{let r=Date.now(),[s,a,i]=await Promise.all([this.uploadAIArtifact(e.enterpriseNeedArtifact,t,"enterprise-need",r),this.uploadAIArtifact(e.solutionArtifact,t,"solution",r),this.uploadAIArtifact(e.riskArtifact,t,"risk",r)]);return{enterpriseNeedArtifactUrl:s,solutionArtifactUrl:a,riskArtifactUrl:i}}catch(e){throw console.error("Error uploading AI artifacts:",e),Error("Failed to upload AI artifacts")}}static async deleteAIArtifacts(e){try{let t=`templates/${e}/artifacts/`,r=(0,g.KR)(c.IG,t),s=(await (0,g._w)(r)).items.map(e=>(0,g.XR)(e));await Promise.all(s)}catch(e){console.error("Error deleting AI artifacts:",e)}}static validateFile(e,t=[".pdf",".doc",".docx",".txt",".jpg",".jpeg",".png"],r=10){if(e.size>1024*r*1024)return{isValid:!1,error:`File size must be less than ${r}MB`};let s="."+e.name.split(".").pop()?.toLowerCase();return t.includes(s)?{isValid:!0}:{isValid:!1,error:`Please select a valid file type: ${t.join(", ")}`}}}function b(){let{user:e,loading:t,isAdmin:r}=(0,i.A)(),n=(0,l.useRouter)(),g=(0,l.useParams)().id,[b,y]=(0,a.useState)(null),[f,v]=(0,a.useState)(null),[k,N]=(0,a.useState)(!0),[w,j]=(0,a.useState)(!1),[A,D]=(0,a.useState)("step2"),[I,C]=(0,a.useState)(!0),[F,U]=(0,a.useState)({enterpriseNeed:"",solutionDescriptionDocument:"",riskOfNoInvestment:"",enterpriseNeedFileUrl:"",solutionDescriptionFileUrl:"",riskOfNoInvestmentFileUrl:""}),[P,V]=(0,a.useState)("claude"),[S,E]=(0,a.useState)(""),[$,O]=(0,a.useState)(""),[R,q]=(0,a.useState)(!1),[B,z]=(0,a.useState)(null),[T,M]=(0,a.useState)({enterpriseNeed:!1,solutionDescription:!1,riskOfNoInvestment:!1}),[L,G]=(0,a.useState)({solutionProviderName:"",solutionName:"",solutionDescription:"",templateVersion:"",templateVersionDate:"",category:"",price:0}),H=async()=>{try{N(!0);let e=await (0,o.x7)((0,o.H9)(c.db,"templates",g));if(e.exists()){let t={id:e.id,...e.data()};y(t),U({enterpriseNeed:t.enterpriseNeed||"",solutionDescriptionDocument:t.solutionDescriptionDocument||"",riskOfNoInvestment:t.riskOfNoInvestment||"",enterpriseNeedFileUrl:t.enterpriseNeedFileUrl||"",solutionDescriptionFileUrl:t.solutionDescriptionFileUrl||"",riskOfNoInvestmentFileUrl:t.riskOfNoInvestmentFileUrl||""}),G({solutionProviderName:t.solutionProviderName||"",solutionName:t.solutionName||"",solutionDescription:t.solutionDescription||"",templateVersion:t.templateVersion||"",templateVersionDate:t.templateVersionDate?t.templateVersionDate instanceof Date?t.templateVersionDate.toISOString().split("T")[0]:new Date(1e3*t.templateVersionDate.seconds).toISOString().split("T")[0]:"",category:t.category||"",price:t.price||0}),V(t.selectedLLM||"claude"),O(t.aiPrompt||""),z(t.generatedArtifacts||null),q("generating-artifacts"===t.status);let r=await (0,o.x7)((0,o.H9)(c.db,"brands",t.brandId));r.exists()&&v({id:r.id,...r.data()})}else n.push("/admin")}catch(e){console.error("Error fetching template data:",e),n.push("/admin")}finally{N(!1)}},_=async e=>{if(e.preventDefault(),b)try{j(!0),await (0,o.mZ)((0,o.H9)(c.db,"templates",g),{solutionProviderName:L.solutionProviderName,solutionName:L.solutionName,solutionDescription:L.solutionDescription,templateVersion:L.templateVersion,templateVersionDate:o.Dc.fromDate(new Date(L.templateVersionDate)),category:L.category,price:L.price,name:L.solutionName,description:L.solutionDescription,updatedAt:o.Dc.now()}),alert("Step 1 information updated successfully!"),await H()}catch(e){console.error("Error updating template:",e),alert("Error updating template. Please try again.")}finally{j(!1)}},W=async(e,t)=>{if(b)try{M(e=>({...e,[t]:!0}));let r=await h.uploadTemplateDocument(e,g,t),s=`${t}FileUrl`;U(e=>({...e,[s]:r})),await (0,o.mZ)((0,o.H9)(c.db,"templates",g),{[`${t}FileUrl`]:r,updatedAt:o.Dc.now()}),alert("File uploaded successfully!")}catch(e){console.error("Error uploading file:",e),alert("Error uploading file. Please try again.")}finally{M(e=>({...e,[t]:!1}))}},Z=async e=>{if(b)try{let t=`${e}FileUrl`,r=F[t];r&&await h.deleteFile(r),U(e=>({...e,[t]:""})),await (0,o.mZ)((0,o.H9)(c.db,"templates",g),{[`${e}FileUrl`]:"",updatedAt:o.Dc.now()}),alert("File removed successfully!")}catch(e){console.error("Error removing file:",e),alert("Error removing file. Please try again.")}},K=async e=>{if(e.preventDefault(),b){if(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)return void alert("Please upload all three document files before proceeding.");try{j(!0),await (0,o.mZ)((0,o.H9)(c.db,"templates",g),{enterpriseNeedFileUrl:F.enterpriseNeedFileUrl,solutionDescriptionFileUrl:F.solutionDescriptionFileUrl,riskOfNoInvestmentFileUrl:F.riskOfNoInvestmentFileUrl,step2Completed:!0,updatedAt:o.Dc.now()}),alert("Documents uploaded successfully! You can now proceed to Step 3 to generate AI artifacts."),await H()}catch(e){console.error("Error updating template:",e),alert("Error updating template. Please try again.")}finally{j(!1)}}},X=async()=>{if(b){if(!b.step1Completed||!b.step2Completed||!b.step3Completed)return void alert("Please complete all three steps before publishing.");if(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)return void alert("Please upload all three document files before publishing.");if(!B)return void alert("Please generate AI artifacts in Step 3 before publishing.");if(window.confirm("Are you sure you want to publish this template? Once published, it cannot be edited. You can only create new versions from published templates."))try{j(!0),await (0,o.mZ)((0,o.H9)(c.db,"templates",g),{status:"published",isActive:!0,publishedAt:o.Dc.now(),updatedAt:o.Dc.now()}),alert("Template published successfully! It can no longer be edited."),await H()}catch(e){console.error("Error publishing template:",e),alert("Error publishing template. Please try again.")}finally{j(!1)}}},Y=async()=>{if(b&&f&&window.confirm("This will create a new draft version of this template that you can edit. Continue?"))try{j(!0);let e=(b.templateVersion||"1.0").split("."),t=parseInt(e[0])||1,r=parseInt(e[1])||0,s=`${t}.${r+1}`,a={name:b.name,description:b.description,brandId:b.brandId,category:b.category,price:b.price,solutionProviderName:b.solutionProviderName,solutionName:b.solutionName,solutionDescription:b.solutionDescription,templateVersion:s,templateVersionDate:o.Dc.now(),status:"draft",step1Completed:!0,step2Completed:!1,step3Completed:!1,isActive:!1,fileUrls:{},enterpriseNeed:b.enterpriseNeed||"",solutionDescriptionDocument:b.solutionDescriptionDocument||"",riskOfNoInvestment:b.riskOfNoInvestment||"",enterpriseNeedFileUrl:"",solutionDescriptionFileUrl:"",riskOfNoInvestmentFileUrl:"",createdAt:o.Dc.now(),updatedAt:o.Dc.now(),basedOnTemplateId:g,basedOnVersion:b.templateVersion},i=await (0,o.gS)((0,o.rJ)(c.db,"templates"),a);alert(`New version ${s} created successfully!`),n.push(`/admin/templates/${i.id}/edit`)}catch(e){console.error("Error creating new version:",e),alert("Error creating new version. Please try again.")}finally{j(!1)}},J=async()=>{if(b){if(!P)return void alert("Please select an AI model.");if(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)return void alert("Please upload all three document files before generating AI artifacts.");try{q(!0),await (0,o.mZ)((0,o.H9)(c.db,"templates",g),{status:"generating-artifacts",selectedLLM:P,companyName:S||f?.name||"Company Name",updatedAt:o.Dc.now()}),console.log("AI artifact generation started via Firebase Function")}catch(e){console.error("Error starting AI generation:",e),alert("Error starting AI generation. Please try again."),q(!1)}}};return t||k?(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,s.jsx)("div",{className:"text-xl",children:"Loading..."})}):r&&b?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)(m.A,{}),(0,s.jsx)("div",{className:"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"published"===b.status?"View Template":"Edit Template"}),(0,s.jsxs)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:[b.solutionName," - ",f?.name]}),(0,s.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"published"===b.status?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}`,children:"published"===b.status?"Published":"Draft"}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Version ",b.templateVersion]}),b.basedOnTemplateId&&(0,s.jsxs)("span",{className:"text-xs text-blue-600 dark:text-blue-400",children:["Based on v",b.basedOnVersion]})]}),"published"===b.status&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:(0,s.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Note:"})," This template is published and cannot be edited. You can create a new version to make changes."]})})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:["published"===b.status&&(0,s.jsxs)("button",{onClick:Y,disabled:w,className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[w&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{children:w?"Creating...":"Create New Version"})]}),(0,s.jsx)(d(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Back to Admin"})]})]})}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("div",{className:`flex items-center ${b.step1Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"}`,children:[(0,s.jsx)("div",{className:`flex items-center justify-center w-8 h-8 ${b.step1Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"} text-white rounded-full text-sm font-medium`,children:b.step1Completed?"✓":"1"}),(0,s.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Basic Information"})]}),(0,s.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,s.jsxs)("div",{className:`flex items-center ${b.step2Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"}`,children:[(0,s.jsx)("div",{className:`flex items-center justify-center w-8 h-8 ${b.step2Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"} text-white rounded-full text-sm font-medium`,children:b.step2Completed?"✓":"2"}),(0,s.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Documents"})]}),(0,s.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,s.jsxs)("div",{className:`flex items-center ${b.step3Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"}`,children:[(0,s.jsx)("div",{className:`flex items-center justify-center w-8 h-8 ${b.step3Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"} text-white rounded-full text-sm font-medium`,children:b.step3Completed?"✓":"3"}),(0,s.jsx)("span",{className:"ml-2 text-sm font-medium",children:"AI Artifacts"})]})]})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[(0,s.jsx)("button",{onClick:()=>D("step1"),disabled:"published"===b.status,className:`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${"step1"===A?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"} ${"published"===b.status?"cursor-not-allowed opacity-60":""}`,children:"published"===b.status?"View Basic Information":"Edit Basic Information"}),(0,s.jsx)("button",{onClick:()=>D("step2"),disabled:"published"===b.status,className:`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${"step2"===A?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"} ${"published"===b.status?"cursor-not-allowed opacity-60":""}`,children:"published"===b.status?"View Documents":"Edit Documents"}),(0,s.jsx)("button",{onClick:()=>D("step3"),disabled:"published"===b.status,className:`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${"step3"===A?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"} ${"published"===b.status?"cursor-not-allowed opacity-60":""}`,children:"published"===b.status?"View AI Artifacts":"Generate AI Artifacts"})]})}),"step1"===A&&(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,s.jsxs)("form",{onSubmit:_,className:"p-6 space-y-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"published"===b.status?"Basic Information (Read Only)":"Basic Information"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Provider Name *"}),(0,s.jsx)("input",{type:"text",value:L.solutionProviderName,onChange:e=>G(t=>({...t,solutionProviderName:e.target.value})),disabled:"published"===b.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Name *"}),(0,s.jsx)("input",{type:"text",value:L.solutionName,onChange:e=>G(t=>({...t,solutionName:e.target.value})),disabled:"published"===b.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Solution Description *"}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!I),className:"text-xs text-blue-600 dark:text-blue-400 hover:underline",children:I?"Try Advanced Editor":"Use Simple Editor"})]}),I?(0,s.jsx)(p.A,{value:L.solutionDescription,onChange:e=>G(t=>({...t,solutionDescription:e})),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md",disabled:"published"===b.status}):(0,s.jsx)(u.A,{value:L.solutionDescription,onChange:e=>G(t=>({...t,solutionDescription:e})),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md",disabled:"published"===b.status})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version *"}),(0,s.jsx)("input",{type:"text",value:L.templateVersion,onChange:e=>G(t=>({...t,templateVersion:e.target.value})),disabled:"published"===b.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version Date *"}),(0,s.jsx)("input",{type:"date",value:L.templateVersionDate,onChange:e=>G(t=>({...t,templateVersionDate:e.target.value})),disabled:"published"===b.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,s.jsxs)("select",{value:L.category,onChange:e=>G(t=>({...t,category:e.target.value})),disabled:"published"===b.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",children:[(0,s.jsx)("option",{value:"Business Analysis",children:"Business Analysis"}),(0,s.jsx)("option",{value:"Financial Planning",children:"Financial Planning"}),(0,s.jsx)("option",{value:"Technology Assessment",children:"Technology Assessment"}),(0,s.jsx)("option",{value:"Risk Management",children:"Risk Management"}),(0,s.jsx)("option",{value:"Strategic Planning",children:"Strategic Planning"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Price ($)"}),(0,s.jsx)("input",{type:"number",min:"0",step:"0.01",value:L.price,onChange:e=>G(t=>({...t,price:parseFloat(e.target.value)||0})),disabled:"published"===b.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"})]})]}),"published"!==b.status&&(0,s.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsx)("button",{type:"submit",disabled:w,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed",children:w?"Updating...":"Update Basic Information"})})]})}),"step2"===A&&(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,s.jsxs)("form",{onSubmit:K,className:"p-6 space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"published"===b.status?"View Documents (Read Only)":"Upload Documents"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"published"===b.status?"View the three essential documents for this Business Value Analysis template.":"Upload the three essential documents for your Business Value Analysis template. These files will be used to generate AI artifacts in Step 3."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Enterprise Need Document *"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Upload a document that describes the business need or challenge that this solution addresses."}),(0,s.jsx)(x,{onFileSelect:e=>W(e,"enterpriseNeed"),onFileRemove:()=>Z("enterpriseNeed"),currentFileUrl:F.enterpriseNeedFileUrl,currentFileName:F.enterpriseNeedFileUrl?h.getFileNameFromUrl(F.enterpriseNeedFileUrl):void 0,disabled:T.enterpriseNeed||"published"===b.status,className:"mt-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Description Document *"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Upload a document that provides a comprehensive description of how your solution works and its key features."}),(0,s.jsx)(x,{onFileSelect:e=>W(e,"solutionDescription"),onFileRemove:()=>Z("solutionDescription"),currentFileUrl:F.solutionDescriptionFileUrl,currentFileName:F.solutionDescriptionFileUrl?h.getFileNameFromUrl(F.solutionDescriptionFileUrl):void 0,disabled:T.solutionDescription||"published"===b.status,className:"mt-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Risk of No Investment Document *"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Upload a document that explains the potential risks and consequences of not implementing this solution."}),(0,s.jsx)(x,{onFileSelect:e=>W(e,"riskOfNoInvestment"),onFileRemove:()=>Z("riskOfNoInvestment"),currentFileUrl:F.riskOfNoInvestmentFileUrl,currentFileName:F.riskOfNoInvestmentFileUrl?h.getFileNameFromUrl(F.riskOfNoInvestmentFileUrl):void 0,disabled:T.riskOfNoInvestment||"published"===b.status,className:"mt-2"})]}),"published"!==b.status&&(0,s.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("button",{type:"submit",disabled:w,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[w&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{children:w?"Saving...":"Save & Continue to Step 3"})]})})]})}),"step3"===A&&(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,s.jsxs)("div",{className:"p-6 space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"published"===b.status?"View AI Artifacts (Read Only)":"Generate AI Artifacts"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"published"===b.status?"View the AI-generated artifacts for this Business Value Analysis template.":"Generate AI artifacts based on the uploaded documents. These artifacts will be used in the final Business Value Analysis template."})]}),(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)&&(0,s.jsx)("div",{className:"p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Prerequisites Required"}),(0,s.jsx)("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:(0,s.jsx)("p",{children:"Please complete Step 2 by uploading all three document files before generating AI artifacts."})})]})]})}),"published"!==b.status&&F.enterpriseNeedFileUrl&&F.solutionDescriptionFileUrl&&F.riskOfNoInvestmentFileUrl&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Select AI Model *"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)("div",{className:`relative rounded-lg border p-4 cursor-pointer transition-colors ${"claude"===P?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}`,onClick:()=>V("claude"),children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"llm",value:"claude",checked:"claude"===P,onChange:()=>V("claude"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-900 dark:text-white",children:"Claude AI API"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Anthropic's Claude for advanced reasoning"})]})]})}),(0,s.jsx)("div",{className:`relative rounded-lg border p-4 cursor-pointer transition-colors ${"openai"===P?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}`,onClick:()=>V("openai"),children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"llm",value:"openai",checked:"openai"===P,onChange:()=>V("openai"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-900 dark:text-white",children:"OpenAI API"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"OpenAI's GPT for creative content generation"})]})]})})]})]}),"published"!==b.status&&F.enterpriseNeedFileUrl&&F.solutionDescriptionFileUrl&&F.riskOfNoInvestmentFileUrl&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Company Name (Optional)"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Enter the company name to be used in the artifacts. If left blank, the brand name will be used."}),(0,s.jsx)("input",{type:"text",value:S,onChange:e=>E(e.target.value),placeholder:`Default: ${f?.name||"Company Name"}`,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]}),$&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"AI Generation Prompt"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"This is the exact prompt that will be sent to the selected AI model."}),(0,s.jsx)("textarea",{value:$,readOnly:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white text-sm"})]}),"published"!==b.status&&F.enterpriseNeedFileUrl&&F.solutionDescriptionFileUrl&&F.riskOfNoInvestmentFileUrl&&(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("button",{type:"button",onClick:J,disabled:R,className:"bg-purple-600 dark:bg-purple-700 text-white px-8 py-3 rounded-md font-medium hover:bg-purple-700 dark:hover:bg-purple-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[R&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{children:R?"Generating AI Artifacts...":"Generate AI Artifacts"})]})}),(R||"generating-artifacts"===b.status)&&(0,s.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("svg",{className:"animate-spin h-5 w-5 text-blue-600 dark:text-blue-400 mr-3",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"AI Artifacts Being Generated"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Please wait while the AI processes your documents and generates the artifacts. This may take a few minutes."})]})]})}),"generation-failed"===b.status&&(0,s.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"AI Generation Failed"}),(0,s.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:b.generationError||"An error occurred while generating AI artifacts. Please try again."}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("button",{onClick:()=>{(0,o.mZ)((0,o.H9)(c.db,"templates",g),{status:"draft",updatedAt:o.Dc.now()})},className:"text-sm text-red-800 dark:text-red-200 underline hover:no-underline",children:"Try Again"})})]})]})}),B&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-green-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"AI Artifacts Generated Successfully"}),(0,s.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"The AI has successfully generated three artifacts based on your uploaded documents."})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Enterprise Need Artifact"}),(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4",children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono",children:B.enterpriseNeedArtifact})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Proposed Solution Artifact"}),(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4",children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono",children:B.solutionArtifact})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Risk of No Investment Artifact"}),(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4",children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono",children:B.riskArtifact})})]})]}),"published"!==b.status&&b.step3Completed&&B&&(0,s.jsx)("div",{className:"flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("button",{type:"button",onClick:X,disabled:w,className:"bg-green-600 dark:bg-green-700 text-white px-8 py-3 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[w&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{children:w?"Publishing...":"Publish Template"})]})})]})})]})})]}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\templates\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\[id]\\edit\\page.tsx","default")},71269:(e,t,r)=>{Promise.resolve().then(r.bind(r,59286))},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o={children:["",{children:["admin",{children:["templates",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64118)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\[id]\\edit\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/templates/[id]/edit/page",pathname:"/admin/templates/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823,567,533,111,77,948],()=>r(77492));module.exports=s})();