import { doc, getDoc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { APIConfiguration } from '@/types';
import { AIService } from './aiService';
import CryptoJS from 'crypto-js';

const API_CONFIG_DOC_ID = 'main';
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'valtics-ai-default-key';

export class APIConfigService {
  /**
   * Encrypt API key for storage
   */
  private static encryptApiKey(apiKey: string): string {
    return CryptoJS.AES.encrypt(apiKey, ENCRYPTION_KEY).toString();
  }

  /**
   * Decrypt API key from storage
   */
  private static decryptApiKey(encryptedKey: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedKey, ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Get API configuration
   */
  static async getAPIConfiguration(): Promise<APIConfiguration | null> {
    try {
      const configDoc = await getDoc(doc(db, 'apiConfig', API_CONFIG_DOC_ID));
      
      if (!configDoc.exists()) {
        return null;
      }

      const data = configDoc.data();
      return {
        id: configDoc.id,
        claudeApiKey: data.claudeApiKey ? this.decryptApiKey(data.claudeApiKey) : undefined,
        openaiApiKey: data.openaiApiKey ? this.decryptApiKey(data.openaiApiKey) : undefined,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        claudeStatus: data.claudeStatus || 'untested',
        openaiStatus: data.openaiStatus || 'untested',
        lastTestedAt: data.lastTestedAt?.toDate(),
        claudeModelConfig: data.claudeModelConfig || {
          model: 'claude-3-5-sonnet-20241022',
          maxTokens: 4000,
          temperature: 0.7
        },
        openaiModelConfig: data.openaiModelConfig || {
          model: 'gpt-4-vision-preview',
          maxTokens: 4000,
          temperature: 0.7
        }
      };
    } catch (error) {
      console.error('Error fetching API configuration:', error);
      throw new Error('Failed to fetch API configuration');
    }
  }

  /**
   * Save API configuration
   */
  static async saveAPIConfiguration(config: Partial<APIConfiguration>): Promise<void> {
    try {
      const configRef = doc(db, 'apiConfig', API_CONFIG_DOC_ID);
      const existingDoc = await getDoc(configRef);

      const updateData: any = {
        updatedAt: Timestamp.now()
      };

      if (config.claudeApiKey) {
        updateData.claudeApiKey = this.encryptApiKey(config.claudeApiKey);
        updateData.claudeStatus = 'untested';
      }

      if (config.openaiApiKey) {
        updateData.openaiApiKey = this.encryptApiKey(config.openaiApiKey);
        updateData.openaiStatus = 'untested';
      }

      if (config.claudeModelConfig) {
        updateData.claudeModelConfig = config.claudeModelConfig;
      }

      if (config.openaiModelConfig) {
        updateData.openaiModelConfig = config.openaiModelConfig;
      }

      if (existingDoc.exists()) {
        await updateDoc(configRef, updateData);
      } else {
        await setDoc(configRef, {
          ...updateData,
          createdAt: Timestamp.now()
        });
      }
    } catch (error) {
      console.error('Error saving API configuration:', error);
      throw new Error('Failed to save API configuration');
    }
  }

  /**
   * Test API connections using Firebase Function (secure server-side testing)
   */
  static async testAPIConnections(): Promise<{
    claudeStatus: 'connected' | 'error' | 'untested';
    openaiStatus: 'connected' | 'error' | 'untested';
    claudeError?: string;
    openaiError?: string;
  }> {
    try {
      // Import Firebase Functions
      const { getFunctions, httpsCallable } = await import('firebase/functions');
      const functions = getFunctions();
      const testAPIConnectionsFunction = httpsCallable(functions, 'testAPIConnectionsFunction');

      // Call the Firebase Function to test API connections
      const result = await testAPIConnectionsFunction();
      return result.data as {
        claudeStatus: 'connected' | 'error' | 'untested';
        openaiStatus: 'connected' | 'error' | 'untested';
        claudeError?: string;
        openaiError?: string;
      };
    } catch (error) {
      console.error('Error testing API connections:', error);
      return {
        claudeStatus: 'error',
        openaiStatus: 'error',
        claudeError: error instanceof Error ? error.message : 'Failed to test connections',
        openaiError: error instanceof Error ? error.message : 'Failed to test connections'
      };
    }
  }

  /**
   * Get decrypted API keys for server-side use
   */
  static async getDecryptedAPIKeys(): Promise<{
    claudeApiKey?: string;
    openaiApiKey?: string;
  }> {
    try {
      const config = await this.getAPIConfiguration();
      return {
        claudeApiKey: config?.claudeApiKey,
        openaiApiKey: config?.openaiApiKey
      };
    } catch (error) {
      console.error('Error getting decrypted API keys:', error);
      throw new Error('Failed to get API keys');
    }
  }

  /**
   * Check if API keys are configured
   */
  static async areAPIKeysConfigured(): Promise<{
    claudeConfigured: boolean;
    openaiConfigured: boolean;
    anyConfigured: boolean;
  }> {
    try {
      const config = await this.getAPIConfiguration();
      const claudeConfigured = !!(config?.claudeApiKey);
      const openaiConfigured = !!(config?.openaiApiKey);
      
      return {
        claudeConfigured,
        openaiConfigured,
        anyConfigured: claudeConfigured || openaiConfigured
      };
    } catch (error) {
      console.error('Error checking API key configuration:', error);
      return {
        claudeConfigured: false,
        openaiConfigured: false,
        anyConfigured: false
      };
    }
  }

  /**
   * Initialize AI services with stored API keys
   */
  static async initializeAIServices(): Promise<void> {
    try {
      const keys = await this.getDecryptedAPIKeys();
      
      if (keys.claudeApiKey) {
        AIService.initializeClaude(keys.claudeApiKey);
      }
      
      if (keys.openaiApiKey) {
        AIService.initializeOpenAI(keys.openaiApiKey);
      }
    } catch (error) {
      console.error('Error initializing AI services:', error);
      throw new Error('Failed to initialize AI services');
    }
  }

  /**
   * Validate API key format
   */
  static validateAPIKey(provider: 'claude' | 'openai', apiKey: string): { isValid: boolean; error?: string } {
    if (!apiKey || apiKey.trim().length === 0) {
      return { isValid: false, error: 'API key cannot be empty' };
    }

    if (provider === 'claude') {
      if (!apiKey.startsWith('sk-ant-')) {
        return { isValid: false, error: 'Claude API key should start with "sk-ant-"' };
      }
    } else if (provider === 'openai') {
      if (!apiKey.startsWith('sk-')) {
        return { isValid: false, error: 'OpenAI API key should start with "sk-"' };
      }
    }

    if (apiKey.length < 20) {
      return { isValid: false, error: 'API key appears to be too short' };
    }

    return { isValid: true };
  }
}
