"use strict";exports.id=890,exports.ids=[890],exports.modules={24890:(e,t,n)=>{n.d(t,{getFunctions:()=>E,httpsCallable:()=>b});var r=n(27728),a=n(52122),i=n(35545);function s(e,t){let n={};for(let r in e)e.hasOwnProperty(r)&&(n[r]=t(e[r]));return n}function o(e){if(null==e)return null;if(e instanceof Number&&(e=e.valueOf()),"number"==typeof e&&isFinite(e)||!0===e||!1===e||"[object String]"===Object.prototype.toString.call(e))return e;if(e instanceof Date)return e.toISOString();if(Array.isArray(e))return e.map(e=>o(e));if("function"==typeof e||"object"==typeof e)return s(e,e=>o(e));throw Error("Data cannot be encoded in JSON: "+e)}function l(e){if(null==e)return e;if(e["@type"])switch(e["@type"]){case"type.googleapis.com/google.protobuf.Int64Value":case"type.googleapis.com/google.protobuf.UInt64Value":{let t=Number(e.value);if(isNaN(t))throw Error("Data cannot be decoded from JSON: "+e);return t}default:throw Error("Data cannot be decoded from JSON: "+e)}return Array.isArray(e)?e.map(e=>l(e)):"function"==typeof e||"object"==typeof e?s(e,e=>l(e)):e}let c="functions",u={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class d extends a.g{constructor(e,t,n){super(`${c}/${e}`,t||""),this.details=n,Object.setPrototypeOf(this,d.prototype)}}function p(e,t){let n,r=function(e){if(e>=200&&e<300)return"ok";switch(e){case 0:case 500:return"internal";case 400:return"invalid-argument";case 401:return"unauthenticated";case 403:return"permission-denied";case 404:return"not-found";case 409:return"aborted";case 429:return"resource-exhausted";case 499:return"cancelled";case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline-exceeded"}return"unknown"}(e),a=r;try{let e=t&&t.error;if(e){let t=e.status;if("string"==typeof t){if(!u[t])return new d("internal","internal");r=u[t],a=t}let i=e.message;"string"==typeof i&&(a=i),n=e.details,void 0!==n&&(n=l(n))}}catch(e){}return"ok"===r?null:new d(r,a,n)}class h{constructor(e,t,n,a){this.app=e,this.auth=null,this.messaging=null,this.appCheck=null,this.serverAppAppCheckToken=null,(0,r.xZ)(e)&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.auth=t.getImmediate({optional:!0}),this.messaging=n.getImmediate({optional:!0}),this.auth||t.get().then(e=>this.auth=e,()=>{}),this.messaging||n.get().then(e=>this.messaging=e,()=>{}),this.appCheck||null==a||a.get().then(e=>this.appCheck=e,()=>{})}async getAuthToken(){if(this.auth)try{let e=await this.auth.getToken();return null==e?void 0:e.accessToken}catch(e){return}}async getMessagingToken(){if(this.messaging&&"Notification"in self&&"granted"===Notification.permission)try{return await this.messaging.getToken()}catch(e){return}}async getAppCheckToken(e){if(this.serverAppAppCheckToken)return this.serverAppAppCheckToken;if(this.appCheck){let t=e?await this.appCheck.getLimitedUseToken():await this.appCheck.getToken();return t.error?null:t.token}return null}async getContext(e){let t=await this.getAuthToken();return{authToken:t,messagingToken:await this.getMessagingToken(),appCheckToken:await this.getAppCheckToken(e)}}}let m="us-central1",f=/^data: (.*?)(?:\n|$)/;class g{constructor(e,t,n,r,a=m,i=(...e)=>fetch(...e)){this.app=e,this.fetchImpl=i,this.emulatorOrigin=null,this.contextProvider=new h(e,t,n,r),this.cancelAllRequests=new Promise(e=>{this.deleteService=()=>Promise.resolve(e())});try{let e=new URL(a);this.customDomain=e.origin+("/"===e.pathname?"":e.pathname),this.region=m}catch(e){this.customDomain=null,this.region=a}}_delete(){return this.deleteService()}_url(e){let t=this.app.options.projectId;if(null!==this.emulatorOrigin){let n=this.emulatorOrigin;return`${n}/${t}/${this.region}/${e}`}return null!==this.customDomain?`${this.customDomain}/${e}`:`https://${this.region}-${t}.cloudfunctions.net/${e}`}}async function w(e,t,n,r){let a;n["Content-Type"]="application/json";try{a=await r(e,{method:"POST",body:JSON.stringify(t),headers:n})}catch(e){return{status:0,json:null}}let i=null;try{i=await a.json()}catch(e){}return{status:a.status,json:i}}async function y(e,t){let n={},r=await e.contextProvider.getContext(t.limitedUseAppCheckTokens);return r.authToken&&(n.Authorization="Bearer "+r.authToken),r.messagingToken&&(n["Firebase-Instance-ID-Token"]=r.messagingToken),null!==r.appCheckToken&&(n["X-Firebase-AppCheck"]=r.appCheckToken),n}async function k(e,t,n,r){var a;let i,s={data:n=o(n)},c=await y(e,r),u=(a=r.timeout||7e4,i=null,{promise:new Promise((e,t)=>{i=setTimeout(()=>{t(new d("deadline-exceeded","deadline-exceeded"))},a)}),cancel:()=>{i&&clearTimeout(i)}}),h=await Promise.race([w(t,s,c,e.fetchImpl),u.promise,e.cancelAllRequests]);if(u.cancel(),!h)throw new d("cancelled","Firebase Functions instance was deleted.");let m=p(h.status,h.json);if(m)throw m;if(!h.json)throw new d("internal","Response is not valid JSON object.");let f=h.json.data;if(void 0===f&&(f=h.json.result),void 0===f)throw new d("internal","Response is missing data field.");return{data:l(f)}}async function T(e,t,n,r){var a;let i,s,c,u={data:n=o(n)},h=await y(e,r);h["Content-Type"]="application/json",h.Accept="text/event-stream";try{i=await e.fetchImpl(t,{method:"POST",body:JSON.stringify(u),headers:h,signal:null==r?void 0:r.signal})}catch(t){if(t instanceof Error&&"AbortError"===t.name){let e=new d("cancelled","Request was cancelled.");return{data:Promise.reject(e),stream:{[Symbol.asyncIterator]:()=>({next:()=>Promise.reject(e)})}}}let e=p(0,null);return{data:Promise.reject(e),stream:{[Symbol.asyncIterator]:()=>({next:()=>Promise.reject(e)})}}}let m=new Promise((e,t)=>{s=e,c=t});null==(a=null==r?void 0:r.signal)||a.addEventListener("abort",()=>{let e=new d("cancelled","Request was cancelled.");c(e)});let g=function(e,t,n,r){let a=(e,r)=>{let a=e.match(f);if(!a)return;let i=a[1];try{let e=JSON.parse(i);if("result"in e)return void t(l(e.result));if("message"in e)return void r.enqueue(l(e.message));if("error"in e){let t=p(0,e);r.error(t),n(t);return}}catch(e){if(e instanceof d){r.error(e),n(e);return}}},i=new TextDecoder;return new ReadableStream({start(t){let s="";return o();async function o(){if(null==r?void 0:r.aborted){let e=new d("cancelled","Request was cancelled");return t.error(e),n(e),Promise.resolve()}try{let{value:l,done:c}=await e.read();if(c){s.trim()&&a(s.trim(),t),t.close();return}if(null==r?void 0:r.aborted){let r=new d("cancelled","Request was cancelled");t.error(r),n(r),await e.cancel();return}let u=(s+=i.decode(l,{stream:!0})).split("\n");for(let e of(s=u.pop()||"",u))e.trim()&&a(e.trim(),t);return o()}catch(r){let e=r instanceof d?r:p(0,null);t.error(e),n(e)}}},cancel:()=>e.cancel()})}(i.body.getReader(),s,c,null==r?void 0:r.signal);return{stream:{[Symbol.asyncIterator](){let e=g.getReader();return{async next(){let{value:t,done:n}=await e.read();return{value:t,done:n}},return:async()=>(await e.cancel(),{done:!0,value:void 0})}}},data:m}}let v="@firebase/functions",A="0.12.8";function E(e=(0,r.Sx)(),t=m){let n=(0,r.j6)((0,a.Ku)(e),c).getImmediate({identifier:t}),i=(0,a.yU)("functions");return i&&function(e,t,n){var r=(0,a.Ku)(e);let i=(0,a.zJ)(t);r.emulatorOrigin=`http${i?"s":""}://${t}:${n}`,i&&((0,a.gE)(r.emulatorOrigin),(0,a.P1)("Functions",!0))}(n,...i),n}function b(e,t,n){var r=(0,a.Ku)(e);let i=e=>(function(e,t,n,r){let a=e._url(t);return k(e,a,n,r)})(r,t,e,n||{});return i.stream=(e,n)=>(function(e,t,n,r){let a=e._url(t);return T(e,a,n,r||{})})(r,t,e,n),i}(0,r.om)(new i.uA(c,(e,{instanceIdentifier:t})=>{let n=e.getProvider("app").getImmediate(),r=e.getProvider("auth-internal");return new g(n,r,e.getProvider("messaging-internal"),e.getProvider("app-check-internal"),t)},"PUBLIC").setMultipleInstances(!0)),(0,r.KO)(v,A,void 0),(0,r.KO)(v,A,"esm2017")}};