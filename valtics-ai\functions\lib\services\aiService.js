"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const openai_1 = __importDefault(require("openai"));
const fileProcessor_1 = require("./fileProcessor");
const apiConfigService_1 = require("./apiConfigService");
class AIService {
    /**
     * Initialize Claude client
     */
    static initializeClaude(apiKey) {
        this.claudeClient = new sdk_1.default({
            apiKey: apiKey,
        });
    }
    /**
     * Initialize OpenAI client
     */
    static initializeOpenAI(apiKey) {
        this.openaiClient = new openai_1.default({
            apiKey: apiKey,
        });
    }
    /**
     * Generate artifacts using Claude
     */
    static async generateWithClaude(prompt, documentContext, solutionName, companyName) {
        if (!this.claudeClient) {
            throw new Error('<PERSON> client not initialized');
        }
        try {
            const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);
            // Get model configuration from API config
            const apiConfig = await apiConfigService_1.APIConfigService.getAPIConfig();
            const modelConfig = (apiConfig === null || apiConfig === void 0 ? void 0 : apiConfig.claudeModelConfig) || {
                model: 'claude-3-5-sonnet-20241022',
                maxTokens: 4000,
                temperature: 0.7
            };
            const response = await this.claudeClient.messages.create({
                model: modelConfig.model,
                max_tokens: modelConfig.maxTokens,
                temperature: modelConfig.temperature,
                messages: [
                    {
                        role: 'user',
                        content: fullPrompt
                    }
                ]
            });
            if (response.content[0].type !== 'text') {
                throw new Error('Unexpected response format from Claude');
            }
            return this.parseAIResponse(response.content[0].text);
        }
        catch (error) {
            console.error('Claude generation failed:', error);
            throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate artifacts using OpenAI
     */
    static async generateWithOpenAI(prompt, documentContext, solutionName, companyName) {
        var _a, _b;
        if (!this.openaiClient) {
            throw new Error('OpenAI client not initialized');
        }
        try {
            const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);
            // Get model configuration from API config
            const apiConfig = await apiConfigService_1.APIConfigService.getAPIConfig();
            const modelConfig = (apiConfig === null || apiConfig === void 0 ? void 0 : apiConfig.openaiModelConfig) || {
                model: 'gpt-4-vision-preview',
                maxTokens: 4000,
                temperature: 0.7
            };
            const response = await this.openaiClient.chat.completions.create({
                model: modelConfig.model,
                max_tokens: modelConfig.maxTokens,
                temperature: modelConfig.temperature,
                messages: [
                    {
                        role: 'user',
                        content: fullPrompt
                    }
                ]
            });
            const content = (_b = (_a = response.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content;
            if (!content) {
                throw new Error('No content received from OpenAI');
            }
            return this.parseAIResponse(content);
        }
        catch (error) {
            console.error('OpenAI generation failed:', error);
            throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Build the full prompt with document context
     */
    static buildFullPrompt(basePrompt, documentContext, solutionName, companyName) {
        return `${basePrompt}

DOCUMENT CONTENTS:

Enterprise Need Document:
${documentContext.enterpriseNeed}

Solution Description Document:
${documentContext.solution}

Risk of No Investment Document:
${documentContext.risk}

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).`;
    }
    /**
     * Parse AI response into structured artifacts
     */
    static parseAIResponse(response) {
        // Try to split the response into three artifacts
        const sections = response.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i);
        let enterpriseNeedArtifact = '';
        let solutionArtifact = '';
        let riskArtifact = '';
        for (const section of sections) {
            const trimmedSection = section.trim();
            if (!trimmedSection)
                continue;
            if (trimmedSection.toLowerCase().includes('enterprise need')) {
                enterpriseNeedArtifact = trimmedSection;
            }
            else if (trimmedSection.toLowerCase().includes('proposed solution')) {
                solutionArtifact = trimmedSection;
            }
            else if (trimmedSection.toLowerCase().includes('risk of no investment')) {
                riskArtifact = trimmedSection;
            }
        }
        // If parsing failed, try alternative approach
        if (!enterpriseNeedArtifact || !solutionArtifact || !riskArtifact) {
            const parts = response.split(/\n\s*\n/);
            if (parts.length >= 3) {
                enterpriseNeedArtifact = parts[0] || 'Enterprise Need artifact could not be parsed.';
                solutionArtifact = parts[1] || 'Solution artifact could not be parsed.';
                riskArtifact = parts[2] || 'Risk artifact could not be parsed.';
            }
            else {
                // Fallback: use the entire response for each artifact with appropriate headers
                const fallbackContent = response;
                enterpriseNeedArtifact = `# Enterprise Need Artifact\n\n${fallbackContent}`;
                solutionArtifact = `# Proposed Solution Artifact\n\n${fallbackContent}`;
                riskArtifact = `# Risk of No Investment Artifact\n\n${fallbackContent}`;
            }
        }
        return {
            enterpriseNeedArtifact: enterpriseNeedArtifact.trim(),
            solutionArtifact: solutionArtifact.trim(),
            riskArtifact: riskArtifact.trim()
        };
    }
    /**
     * Test Claude API connection
     */
    static async testClaudeConnection() {
        if (!this.claudeClient) {
            throw new Error('Claude client not initialized');
        }
        try {
            const response = await this.claudeClient.messages.create({
                model: 'claude-3-sonnet-20240229',
                max_tokens: 10,
                temperature: 0,
                messages: [
                    {
                        role: 'user',
                        content: 'Test'
                    }
                ]
            });
            if (!response.content || response.content.length === 0) {
                throw new Error('Invalid response from Claude API');
            }
        }
        catch (error) {
            console.error('Claude API test failed:', error);
            throw new Error(`Claude API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Test OpenAI API connection
     */
    static async testOpenAIConnection() {
        if (!this.openaiClient) {
            throw new Error('OpenAI client not initialized');
        }
        try {
            const response = await this.openaiClient.chat.completions.create({
                model: 'gpt-4',
                max_tokens: 10,
                temperature: 0,
                messages: [
                    {
                        role: 'user',
                        content: 'Test'
                    }
                ]
            });
            if (!response.choices || response.choices.length === 0) {
                throw new Error('Invalid response from OpenAI API');
            }
        }
        catch (error) {
            console.error('OpenAI API test failed:', error);
            throw new Error(`OpenAI API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate artifacts using Claude with direct file attachments
     */
    static async generateWithClaudeFiles(prompt, documentFiles, solutionName, companyName) {
        if (!this.claudeClient) {
            throw new Error('Claude client not initialized');
        }
        try {
            // Build content array with direct file attachments
            const content = [];
            // Helper function to add document content
            const addDocumentContent = (file, documentName) => {
                const fileType = file.fileType.toLowerCase();
                if (fileType === 'pdf') {
                    // Use document attachment for PDF files
                    content.push({
                        type: 'document',
                        source: {
                            type: 'base64',
                            media_type: 'application/pdf',
                            data: file.base64Data
                        }
                    });
                    content.push({
                        type: 'text',
                        text: `The above document contains the ${documentName} information.`
                    });
                }
                else if (['png', 'jpg', 'jpeg'].includes(fileType)) {
                    // Use image attachment for image files
                    const mimeType = fileType === 'jpg' ? 'image/jpeg' : `image/${fileType}`;
                    content.push({
                        type: 'image',
                        source: {
                            type: 'base64',
                            media_type: mimeType,
                            data: file.base64Data
                        }
                    });
                    content.push({
                        type: 'text',
                        text: `The above image contains the ${documentName} information.`
                    });
                }
                else if (['docx', 'doc', 'txt'].includes(fileType)) {
                    // Extract text for text-based documents
                    try {
                        const buffer = Buffer.from(file.base64Data, 'base64');
                        // For text-based files, we can still extract content synchronously for some types
                        if (fileType === 'txt') {
                            const textContent = buffer.toString('utf-8');
                            content.push({
                                type: 'text',
                                text: `${documentName} Document (${file.fileName}):\n\n${fileProcessor_1.FileProcessor.cleanTextForAI(textContent)}\n\n---\n`
                            });
                        }
                        else {
                            // For DOCX/DOC files, we'll need to handle them differently
                            // For now, just include the filename
                            content.push({
                                type: 'text',
                                text: `${documentName} Document: ${file.fileName} (Word document - please process the content)\n\n---\n`
                            });
                        }
                    }
                    catch (extractError) {
                        console.warn(`Failed to process ${file.fileName}, using filename only:`, extractError);
                        content.push({
                            type: 'text',
                            text: `${documentName} Document: ${file.fileName} (content could not be processed)\n\n---\n`
                        });
                    }
                }
                else {
                    // Unsupported file type
                    content.push({
                        type: 'text',
                        text: `${documentName} Document: ${file.fileName} (unsupported file type: ${fileType})\n\n---\n`
                    });
                }
            };
            // Add each document
            addDocumentContent(documentFiles.enterpriseNeedFile, 'Enterprise Need');
            addDocumentContent(documentFiles.solutionFile, 'Solution Description');
            addDocumentContent(documentFiles.riskFile, 'Risk of No Investment');
            // Add the main prompt
            content.push({
                type: 'text',
                text: `${prompt}

Please analyze the provided documents above and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Use only the content from the provided documents and do not add any other information other than the items requested in this prompt.`
            });
            // Get model configuration from API config
            const apiConfig = await apiConfigService_1.APIConfigService.getAPIConfig();
            const modelConfig = (apiConfig === null || apiConfig === void 0 ? void 0 : apiConfig.claudeModelConfig) || {
                model: 'claude-3-5-sonnet-20241022',
                maxTokens: 4000,
                temperature: 0.7
            };
            const response = await this.claudeClient.messages.create({
                model: modelConfig.model,
                max_tokens: modelConfig.maxTokens,
                temperature: modelConfig.temperature,
                messages: [
                    {
                        role: 'user',
                        content
                    }
                ]
            });
            if (response.content[0].type !== 'text') {
                throw new Error('Unexpected response format from Claude');
            }
            return this.parseAIResponse(response.content[0].text);
        }
        catch (error) {
            console.error('Claude file generation failed:', error);
            throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate artifacts using OpenAI with file attachments
     * Uses GPT-4 Vision for images and includes file content for other types
     */
    static async generateWithOpenAIFiles(prompt, documentFiles, solutionName, companyName) {
        var _a, _b;
        if (!this.openaiClient) {
            throw new Error('OpenAI client not initialized');
        }
        try {
            // Build content array for OpenAI
            const content = [];
            // Add the main prompt first
            content.push({
                type: 'text',
                text: `${prompt}

I have three documents that I need you to analyze. Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Documents to analyze:`
            });
            // Helper function to add document content for OpenAI
            const addDocumentContentOpenAI = (file, documentName) => {
                const fileType = file.fileType.toLowerCase();
                if (['png', 'jpg', 'jpeg'].includes(fileType)) {
                    // Use image attachment for image files with GPT-4 Vision
                    content.push({
                        type: 'text',
                        text: `\n${documentName} Document (${file.fileName}):`
                    });
                    content.push({
                        type: 'image_url',
                        image_url: {
                            url: `data:image/${fileType === 'jpg' ? 'jpeg' : fileType};base64,${file.base64Data}`
                        }
                    });
                }
                else {
                    // For non-image files, include filename and note about content
                    content.push({
                        type: 'text',
                        text: `\n${documentName} Document: ${file.fileName} (${fileType.toUpperCase()} file - please note that the content extraction may be limited for this file type)`
                    });
                }
            };
            // Add each document
            addDocumentContentOpenAI(documentFiles.enterpriseNeedFile, 'Enterprise Need');
            addDocumentContentOpenAI(documentFiles.solutionFile, 'Solution Description');
            addDocumentContentOpenAI(documentFiles.riskFile, 'Risk of No Investment');
            // Get model configuration from API config
            const apiConfig = await apiConfigService_1.APIConfigService.getAPIConfig();
            const modelConfig = (apiConfig === null || apiConfig === void 0 ? void 0 : apiConfig.openaiModelConfig) || {
                model: 'gpt-4-vision-preview',
                maxTokens: 4000,
                temperature: 0.7
            };
            const response = await this.openaiClient.chat.completions.create({
                model: modelConfig.model,
                max_tokens: modelConfig.maxTokens,
                temperature: modelConfig.temperature,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert business analyst creating professional Business Value Assessment artifacts. You can analyze both text and image content to create comprehensive business documents.'
                    },
                    {
                        role: 'user',
                        content: content
                    }
                ]
            });
            const responseContent = (_b = (_a = response.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content;
            if (!responseContent) {
                throw new Error('No content received from OpenAI');
            }
            return this.parseAIResponse(responseContent);
        }
        catch (error) {
            console.error('OpenAI file generation failed:', error);
            throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Generate artifacts with retry logic using file attachments
     */
    static async generateArtifactsWithFilesRetry(llmType, prompt, documentFiles, solutionName, companyName, maxRetries = 3) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (llmType === 'claude') {
                    return await this.generateWithClaudeFiles(prompt, documentFiles, solutionName, companyName);
                }
                else {
                    return await this.generateWithOpenAIFiles(prompt, documentFiles, solutionName, companyName);
                }
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');
                console.error(`AI file generation attempt ${attempt} failed:`, lastError);
                if (attempt < maxRetries) {
                    // Wait before retrying (exponential backoff)
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
                }
            }
        }
        throw new Error(`AI file generation failed after ${maxRetries} attempts: ${lastError === null || lastError === void 0 ? void 0 : lastError.message}`);
    }
    /**
     * Generate artifacts with retry logic
     */
    static async generateArtifactsWithRetry(llmType, prompt, documentContext, solutionName, companyName, maxRetries = 3) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (llmType === 'claude') {
                    return await this.generateWithClaude(prompt, documentContext, solutionName, companyName);
                }
                else {
                    return await this.generateWithOpenAI(prompt, documentContext, solutionName, companyName);
                }
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');
                console.error(`AI generation attempt ${attempt} failed:`, lastError);
                if (attempt < maxRetries) {
                    // Wait before retrying (exponential backoff)
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
                }
            }
        }
        throw new Error(`AI generation failed after ${maxRetries} attempts: ${lastError === null || lastError === void 0 ? void 0 : lastError.message}`);
    }
}
exports.AIService = AIService;
AIService.claudeClient = null;
AIService.openaiClient = null;
//# sourceMappingURL=aiService.js.map