{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ThemeToggle.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ThemeToggle.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ThemeToggle.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ThemeToggle.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Image from 'next/image';\nimport ThemeToggle from '@/components/ThemeToggle';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Navigation */}\n      <nav className=\"bg-white dark:bg-gray-800 shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-3\">\n                <Image\n                  src=\"/logo.png\"\n                  alt=\"VALTICS AI Logo\"\n                  width={32}\n                  height={32}\n                  className=\"w-8 h-8\"\n                />\n                <h1 className=\"text-xl font-semibold text-gray-900 dark:text-white\">VALTICS AI</h1>\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/pricing\"\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Pricing\n              </Link>\n              <Link\n                href=\"/login\"\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Login\n              </Link>\n              <ThemeToggle />\n              <Link\n                href=\"/register\"\n                className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center\">\n          <div className=\"flex justify-center mb-8\">\n            <Image\n              src=\"/logo.png\"\n              alt=\"VALTICS AI Logo\"\n              width={120}\n              height={120}\n              className=\"w-24 h-24 md:w-30 md:h-30\"\n            />\n          </div>\n          <h1 className=\"text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Create Powerful Business Value Analysis Reports\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto\">\n            Transform your technology solutions into compelling business cases with AI-powered\n            templates and professional reporting tools.\n          </p>\n          <div className=\"flex justify-center gap-4 mb-16\">\n            <Link\n              href=\"/register\"\n              className=\"bg-blue-600 dark:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n            >\n              Start Free Trial\n            </Link>\n            <Link\n              href=\"/pricing\"\n              className=\"bg-white dark:bg-gray-700 text-gray-800 dark:text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600\"\n            >\n              View Pricing\n            </Link>\n          </div>\n\n          {/* Features */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-20\">\n            <div className=\"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 dark:text-blue-400 text-2xl\">📊</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">Professional Templates</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                Access pre-built BVA templates for Microsoft, security, and enterprise solutions.\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-green-600 dark:text-green-400 text-2xl\">🤖</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">AI-Powered Analysis</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                Generate executive summaries and detailed reports with intelligent automation.\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md\">\n              <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-purple-600 dark:text-purple-400 text-2xl\">📈</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">ROI Calculations</h3>\n              <p className=\"text-gray-600 dark:text-gray-300\">\n                Automatic ROI, payback period, and benefit calculations with visual charts.\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;;;;;;;;;;;;0CAGxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,0HAAA,CAAA,UAAW;;;;;kDACZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAGd,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAI/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAKlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8C;;;;;;;;;;;sDAEhE,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAKlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgD;;;;;;;;;;;sDAElE,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9D", "debugId": null}}]}