import Anthropic from '@anthropic-ai/sdk';
import OpenAI from 'openai';
import { AIResponse, DocumentContext, DocumentContextWithFiles, DocumentFile } from '../types';
import { FileProcessor } from './fileProcessor';

export class AIService {
  private static claudeClient: Anthropic | null = null;
  private static openaiClient: OpenAI | null = null;

  /**
   * Initialize Claude client
   */
  static initializeClaude(apiKey: string): void {
    this.claudeClient = new Anthropic({
      apiKey: apiKey,
    });
  }

  /**
   * Initialize OpenAI client
   */
  static initializeOpenAI(apiKey: string): void {
    this.openaiClient = new OpenAI({
      apiKey: apiKey,
    });
  }

  /**
   * Generate artifacts using Claude
   */
  static async generateWithClaude(
    prompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.claudeClient) {
      throw new Error('Claude client not initialized');
    }

    try {
      const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);

      const response = await this.claudeClient.messages.create({
        model: 'claude-3-sonnet-20240229',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });

      if (response.content[0].type !== 'text') {
        throw new Error('Unexpected response format from Claude');
      }

      return this.parseAIResponse(response.content[0].text);
    } catch (error) {
      console.error('Claude generation failed:', error);
      throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts using OpenAI
   */
  static async generateWithOpenAI(
    prompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);

      const response = await this.openaiClient.chat.completions.create({
        model: 'gpt-4',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('OpenAI generation failed:', error);
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the full prompt with document context
   */
  private static buildFullPrompt(
    basePrompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string
  ): string {
    return `${basePrompt}

DOCUMENT CONTENTS:

Enterprise Need Document:
${documentContext.enterpriseNeed}

Solution Description Document:
${documentContext.solution}

Risk of No Investment Document:
${documentContext.risk}

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).`;
  }

  /**
   * Parse AI response into structured artifacts
   */
  private static parseAIResponse(response: string): AIResponse {
    // Try to split the response into three artifacts
    const sections = response.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i);
    
    let enterpriseNeedArtifact = '';
    let solutionArtifact = '';
    let riskArtifact = '';

    for (const section of sections) {
      const trimmedSection = section.trim();
      if (!trimmedSection) continue;

      if (trimmedSection.toLowerCase().includes('enterprise need')) {
        enterpriseNeedArtifact = trimmedSection;
      } else if (trimmedSection.toLowerCase().includes('proposed solution')) {
        solutionArtifact = trimmedSection;
      } else if (trimmedSection.toLowerCase().includes('risk of no investment')) {
        riskArtifact = trimmedSection;
      }
    }

    // If parsing failed, try alternative approach
    if (!enterpriseNeedArtifact || !solutionArtifact || !riskArtifact) {
      const parts = response.split(/\n\s*\n/);
      if (parts.length >= 3) {
        enterpriseNeedArtifact = parts[0] || 'Enterprise Need artifact could not be parsed.';
        solutionArtifact = parts[1] || 'Solution artifact could not be parsed.';
        riskArtifact = parts[2] || 'Risk artifact could not be parsed.';
      } else {
        // Fallback: use the entire response for each artifact with appropriate headers
        const fallbackContent = response;
        enterpriseNeedArtifact = `# Enterprise Need Artifact\n\n${fallbackContent}`;
        solutionArtifact = `# Proposed Solution Artifact\n\n${fallbackContent}`;
        riskArtifact = `# Risk of No Investment Artifact\n\n${fallbackContent}`;
      }
    }

    return {
      enterpriseNeedArtifact: enterpriseNeedArtifact.trim(),
      solutionArtifact: solutionArtifact.trim(),
      riskArtifact: riskArtifact.trim()
    };
  }

  /**
   * Test Claude API connection
   */
  static async testClaudeConnection(): Promise<void> {
    if (!this.claudeClient) {
      throw new Error('Claude client not initialized');
    }

    try {
      const response = await this.claudeClient.messages.create({
        model: 'claude-3-sonnet-20240229',
        max_tokens: 10,
        temperature: 0,
        messages: [
          {
            role: 'user',
            content: 'Test'
          }
        ]
      });

      if (!response.content || response.content.length === 0) {
        throw new Error('Invalid response from Claude API');
      }
    } catch (error) {
      console.error('Claude API test failed:', error);
      throw new Error(`Claude API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Test OpenAI API connection
   */
  static async testOpenAIConnection(): Promise<void> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.openaiClient.chat.completions.create({
        model: 'gpt-4',
        max_tokens: 10,
        temperature: 0,
        messages: [
          {
            role: 'user',
            content: 'Test'
          }
        ]
      });

      if (!response.choices || response.choices.length === 0) {
        throw new Error('Invalid response from OpenAI API');
      }
    } catch (error) {
      console.error('OpenAI API test failed:', error);
      throw new Error(`OpenAI API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts using Claude with hybrid approach (PDF attachments + text extraction)
   */
  static async generateWithClaudeFiles(
    prompt: string,
    documentFiles: DocumentContextWithFiles,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.claudeClient) {
      throw new Error('Claude client not initialized');
    }

    try {
      // Build content array with mixed approach
      const content: any[] = [];

      // Helper function to add document content
      const addDocumentContent = async (file: DocumentFile, documentName: string) => {
        if (file.fileType.toLowerCase() === 'pdf') {
          // Use document attachment for PDF files
          content.push({
            type: 'document',
            source: {
              type: 'base64',
              media_type: 'application/pdf',
              data: file.base64Data
            }
          });
          content.push({
            type: 'text',
            text: `The above document contains the ${documentName} information.`
          });
        } else {
          // Extract text for non-PDF files and include as text content
          try {
            const buffer = Buffer.from(file.base64Data!, 'base64');
            const extractedContent = await FileProcessor.extractTextFromFile(buffer, file.fileName);
            content.push({
              type: 'text',
              text: `${documentName} Document (${file.fileName}):\n\n${FileProcessor.cleanTextForAI(extractedContent.text)}\n\n---\n`
            });
          } catch (extractError) {
            console.warn(`Failed to extract text from ${file.fileName}, using filename only:`, extractError);
            content.push({
              type: 'text',
              text: `${documentName} Document: ${file.fileName} (content could not be extracted)\n\n---\n`
            });
          }
        }
      };

      // Add each document
      await addDocumentContent(documentFiles.enterpriseNeedFile, 'Enterprise Need');
      await addDocumentContent(documentFiles.solutionFile, 'Solution Description');
      await addDocumentContent(documentFiles.riskFile, 'Risk of No Investment');

      // Add the main prompt
      content.push({
        type: 'text',
        text: `${prompt}

Please analyze the provided documents above and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Use only the content from the provided documents and do not add any other information other than the items requested in this prompt.`
      });

      const response = await this.claudeClient.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'user',
            content
          }
        ]
      });

      if (response.content[0].type !== 'text') {
        throw new Error('Unexpected response format from Claude');
      }

      return this.parseAIResponse(response.content[0].text);
    } catch (error) {
      console.error('Claude file generation failed:', error);
      throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts using OpenAI with file attachments
   * Note: OpenAI doesn't support direct file attachments in chat completions,
   * so we'll include file content in the prompt for now
   */
  static async generateWithOpenAIFiles(
    prompt: string,
    documentFiles: DocumentContextWithFiles,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      // For OpenAI, we'll include file information in the prompt
      // since direct file attachment isn't supported in chat completions
      const fullPrompt = `${prompt}

I have three documents that I need you to analyze:

1. Enterprise Need Document (${documentFiles.enterpriseNeedFile.fileName})
2. Solution Description Document (${documentFiles.solutionFile.fileName})
3. Risk of No Investment Document (${documentFiles.riskFile.fileName})

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Note: The documents are provided as attachments to this request. Please analyze their content to create the artifacts.`;

      const response = await this.openaiClient.chat.completions.create({
        model: 'gpt-4',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'system',
            content: 'You are an expert business analyst creating professional Business Value Assessment artifacts. You have access to document attachments that contain the source material for your analysis.'
          },
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('OpenAI file generation failed:', error);
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts with retry logic using file attachments
   */
  static async generateArtifactsWithFilesRetry(
    llmType: 'claude' | 'openai',
    prompt: string,
    documentFiles: DocumentContextWithFiles,
    solutionName: string,
    companyName: string,
    maxRetries: number = 3
  ): Promise<AIResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (llmType === 'claude') {
          return await this.generateWithClaudeFiles(prompt, documentFiles, solutionName, companyName);
        } else {
          return await this.generateWithOpenAIFiles(prompt, documentFiles, solutionName, companyName);
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`AI file generation attempt ${attempt} failed:`, lastError);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw new Error(`AI file generation failed after ${maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * Generate artifacts with retry logic
   */
  static async generateArtifactsWithRetry(
    llmType: 'claude' | 'openai',
    prompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string,
    maxRetries: number = 3
  ): Promise<AIResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (llmType === 'claude') {
          return await this.generateWithClaude(prompt, documentContext, solutionName, companyName);
        } else {
          return await this.generateWithOpenAI(prompt, documentContext, solutionName, companyName);
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`AI generation attempt ${attempt} failed:`, lastError);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw new Error(`AI generation failed after ${maxRetries} attempts: ${lastError?.message}`);
  }
}
