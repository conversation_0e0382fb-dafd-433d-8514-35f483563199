@echo off
setlocal

echo ===================================
echo VALTICS AI - Firebase Functions Deployment
echo ===================================

REM Navigate to functions directory
cd functions

echo 📦 Installing dependencies...
call npm install

echo 🔨 Building TypeScript...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Build failed. Please fix the errors and try again.
    exit /b 1
)

echo ✅ Build successful!

REM Go back to project root
cd ..

echo 🚀 Deploying functions to Firebase...
echo.
echo Note: Adding delays between API calls to avoid quota limits...
echo.

REM Deploy with rate limiting flag
call firebase deploy --only functions --no-throttle

if %errorlevel% equ 0 (
    echo ✅ Functions deployed successfully!
    echo.
    echo 📋 Next steps:
    echo 1. Verify the function is running in Firebase Console
    echo 2. Test AI artifact generation in the application
    echo 3. Monitor function logs for any issues
    echo.
    echo 🔍 To view logs: firebase functions:log
) else (
    echo ❌ Deployment failed. Please check the errors above.
    echo.
    echo If you're seeing quota exceeded errors (429), try:
    echo 1. Wait a few minutes before retrying
    echo 2. Deploy functions individually with delays between them
    echo 3. Request a quota increase from Google Cloud Console
    exit /b 1
)

pause

