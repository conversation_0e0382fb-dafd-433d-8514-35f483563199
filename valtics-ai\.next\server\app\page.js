(()=>{var e={};e.id=974,e.ids=[974],e.modules={1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:a,objectFit:n}=e,o=i?40*i:t,l=s?40*s:r,d=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\node_modules\\next\\dist\\client\\app-dir\\link.js")},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(21122);let i=r(1322),s=r(27894),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let d,c,u,{src:m,sizes:p,unoptimized:h=!1,priority:g=!1,loading:x,className:f,quality:b,width:v,height:y,fill:w=!1,style:j,overrideSrc:_,onLoad:P,onLoadingComplete:k,placeholder:N="empty",blurDataURL:C,fetchPriority:E,decoding:O="async",layout:S,objectFit:R,objectPosition:q,lazyBoundary:A,lazyRoot:I,...D}=e,{imgConf:z,showAltText:M,blurComplete:T,defaultLoader:G}=t,L=z||s.imageConfigDefault;if("allSizes"in L)d=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),i=null==(r=L.qualities)?void 0:r.sort((e,t)=>e-t);d={...L,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=D.loader||G;delete D.loader,delete D.srcSet;let U="__next_img_default"in F;if(U){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...i}=t;return e(i)}}if(S){"fill"===S&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!p&&(p=t)}let V="",B=o(v),W=o(y);if((l=m)&&"object"==typeof l&&(n(l)||void 0!==l.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,C=C||e.blurDataURL,V=e.src,!w)if(B||W){if(B&&!W){let t=B/e.width;W=Math.round(e.height*t)}else if(!B&&W){let t=W/e.height;B=Math.round(e.width*t)}}else B=e.width,W=e.height}let X=!g&&("lazy"===x||void 0===x);(!(m="string"==typeof m?m:V)||m.startsWith("data:")||m.startsWith("blob:"))&&(h=!0,X=!1),d.unoptimized&&(h=!0),U&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(h=!0);let J=o(b),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:R,objectPosition:q}:{},M?{}:{color:"transparent"},j),H=T||"empty"===N?null:"blur"===N?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:B,heightInt:W,blurWidth:c,blurHeight:u,blurDataURL:C||"",objectFit:Y.objectFit})+'")':'url("'+N+'")',K=a.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,$=H?{backgroundSize:K,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},Q=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:a,sizes:n,loader:o}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,n),c=l.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:l.map((e,i)=>o({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:o({config:t,src:r,quality:a,width:l[c]})}}({config:d,src:m,unoptimized:h,width:B,quality:J,sizes:p,loader:F});return{props:{...D,loading:X?"lazy":x,fetchPriority:E,width:B,height:W,decoding:O,className:f,style:{...Y,...$},sizes:Q.sizes,srcSet:Q.srcSet,src:_||Q.src},meta:{unoptimized:h,priority:g,placeholder:N,fill:w}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var i=r(65239),s=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48707)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},21527:(e,t,r)=>{Promise.resolve().then(r.bind(r,46106)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23))},21820:e=>{"use strict";e.exports=require("os")},26255:(e,t,r)=>{Promise.resolve().then(r.bind(r,27436)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23))},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:a}=e,n=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+n+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},46106:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\components\\\\ThemeToggle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\components\\ThemeToggle.tsx","default")},48707:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var i=r(37413),s=r(4536),a=r.n(s),n=r(70099),o=r.n(n),l=r(46106);function d(){return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:[(0,i.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between h-16",children:[(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsxs)(a(),{href:"/",className:"flex items-center space-x-3",children:[(0,i.jsx)(o(),{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,i.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"VALTICS AI"})]})}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(a(),{href:"/pricing",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Pricing"}),(0,i.jsx)(a(),{href:"/login",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Login"}),(0,i.jsx)(l.default,{}),(0,i.jsx)(a(),{href:"/register",className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Get Started"})]})]})})}),(0,i.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"flex justify-center mb-8",children:(0,i.jsx)(o(),{src:"/logo.png",alt:"VALTICS AI Logo",width:120,height:120,className:"w-24 h-24 md:w-30 md:h-30"})}),(0,i.jsx)("h1",{className:"text-5xl font-bold text-gray-900 dark:text-white mb-6",children:"Create Powerful Business Value Analysis Reports"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto",children:"Transform your technology solutions into compelling business cases with AI-powered templates and professional reporting tools."}),(0,i.jsxs)("div",{className:"flex justify-center gap-4 mb-16",children:[(0,i.jsx)(a(),{href:"/register",className:"bg-blue-600 dark:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Start Free Trial"}),(0,i.jsx)(a(),{href:"/pricing",className:"bg-white dark:bg-gray-700 text-gray-800 dark:text-white px-8 py-3 rounded-md text-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600",children:"View Pricing"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-20",children:[(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("span",{className:"text-blue-600 dark:text-blue-400 text-2xl",children:"\uD83D\uDCCA"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Professional Templates"}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Access pre-built BVA templates for Microsoft, security, and enterprise solutions."})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("span",{className:"text-green-600 dark:text-green-400 text-2xl",children:"\uD83E\uDD16"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"AI-Powered Analysis"}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Generate executive summaries and detailed reports with intelligent automation."})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("span",{className:"text-purple-600 dark:text-purple-400 text-2xl",children:"\uD83D\uDCC8"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"ROI Calculations"}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Automatic ROI, payback period, and benefit calculations with visual charts."})]})]})]})})]})}},49603:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\node_modules\\next\\dist\\client\\image-component.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let i=r(72639),s=r(9131),a=r(49603),n=i._(r(32091));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,823,567,533,77],()=>r(11572));module.exports=i})();