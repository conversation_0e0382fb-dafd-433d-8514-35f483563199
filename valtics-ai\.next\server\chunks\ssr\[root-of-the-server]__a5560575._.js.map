{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,sCAAsC;QACtC,IAAI,CAAC,cAAc;YACjB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,8OAAC,0HAAA,CAAA,iBAAc;;;;;0CAGf,8OAAC,0HAAA,CAAA,UAAW;;;;;0CAEZ,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { collection, query, getDocs, orderBy, where, addDoc, updateDoc, doc } from 'firebase/firestore';\nimport { getAuth, sendPasswordResetEmail } from 'firebase/auth';\nimport { db } from '@/lib/firebase/config';\nimport { Brand, Template, User, BVAInstance } from '@/types';\nimport Navigation from '@/components/Navigation';\n\nexport default function AdminDashboard() {\n  const { user, loading, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [users, setUsers] = useState<User[]>([]);\n  const [bvaInstances, setBvaInstances] = useState<BVAInstance[]>([]);\n  const [loadingData, setLoadingData] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [showNewBrandForm, setShowNewBrandForm] = useState(false);\n  const [newBrand, setNewBrand] = useState({ name: '', description: '', logoUrl: '' });\n\n  // User management state\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [showUserModal, setShowUserModal] = useState(false);\n  const [userSearchTerm, setUserSearchTerm] = useState('');\n  const [userRoleFilter, setUserRoleFilter] = useState<'all' | 'admin' | 'user'>('all');\n  const [userStatusFilter, setUserStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');\n\n  useEffect(() => {\n    if (!loading && (!user || !isAdmin)) {\n      router.push('/dashboard');\n    }\n  }, [user, loading, isAdmin, router]);\n\n  useEffect(() => {\n    if (user && isAdmin) {\n      fetchAdminData();\n    }\n  }, [user, isAdmin]);\n\n  const fetchAdminData = async () => {\n    try {\n      setLoadingData(true);\n\n      // Fetch brands\n      const brandsQuery = query(collection(db, 'brands'), orderBy('name', 'asc'));\n      const brandsSnapshot = await getDocs(brandsQuery);\n      const brandsData = brandsSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Brand[];\n      setBrands(brandsData);\n\n      // Fetch templates\n      const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));\n      const templatesSnapshot = await getDocs(templatesQuery);\n      const templatesData = templatesSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Template[];\n      setTemplates(templatesData);\n\n      // Fetch users\n      const usersQuery = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n      const usersSnapshot = await getDocs(usersQuery);\n      const usersData = usersSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as User[];\n      setUsers(usersData);\n\n      // Fetch BVA instances\n      const bvaQuery = query(collection(db, 'bvaInstances'), orderBy('createdAt', 'desc'));\n      const bvaSnapshot = await getDocs(bvaQuery);\n      const bvaData = bvaSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as BVAInstance[];\n      setBvaInstances(bvaData);\n\n    } catch (error) {\n      console.error('Error fetching admin data:', error);\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  const handleCreateBrand = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      await addDoc(collection(db, 'brands'), {\n        ...newBrand,\n        isActive: true,\n        createdAt: new Date()\n      });\n\n      setNewBrand({ name: '', description: '', logoUrl: '' });\n      setShowNewBrandForm(false);\n      fetchAdminData();\n    } catch (error) {\n      console.error('Error creating brand:', error);\n      alert('Error creating brand. Please try again.');\n    }\n  };\n\n  const toggleBrandStatus = async (brandId: string, currentStatus: boolean) => {\n    try {\n      await updateDoc(doc(db, 'brands', brandId), {\n        isActive: !currentStatus\n      });\n      fetchAdminData();\n    } catch (error) {\n      console.error('Error updating brand status:', error);\n    }\n  };\n\n  const toggleTemplateStatus = async (templateId: string, currentStatus: boolean) => {\n    try {\n      await updateDoc(doc(db, 'templates', templateId), {\n        isActive: !currentStatus\n      });\n      fetchAdminData();\n    } catch (error) {\n      console.error('Error updating template status:', error);\n    }\n  };\n\n  // User management functions\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setShowUserModal(true);\n  };\n\n  const handleSaveUser = async (userData: Partial<User>) => {\n    if (!editingUser) return;\n\n    try {\n      await updateDoc(doc(db, 'users', editingUser.id), {\n        ...userData,\n        updatedAt: new Date()\n      });\n\n      setShowUserModal(false);\n      setEditingUser(null);\n      fetchAdminData();\n      alert('User updated successfully!');\n    } catch (error) {\n      console.error('Error updating user:', error);\n      alert('Error updating user. Please try again.');\n    }\n  };\n\n  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {\n    try {\n      await updateDoc(doc(db, 'users', userId), {\n        isActive: !currentStatus,\n        updatedAt: new Date()\n      });\n      fetchAdminData();\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      alert('Error updating user status. Please try again.');\n    }\n  };\n\n  const handleResetPassword = async (email: string) => {\n    try {\n      const auth = getAuth();\n      await sendPasswordResetEmail(auth, email);\n      alert(`Password reset email sent to ${email}`);\n    } catch (error) {\n      console.error('Error sending password reset email:', error);\n      alert('Error sending password reset email. Please try again.');\n    }\n  };\n\n  const filteredUsers = users.filter(user => {\n    const matchesSearch = user.email.toLowerCase().includes(userSearchTerm.toLowerCase()) ||\n                         (user.firstName && user.firstName.toLowerCase().includes(userSearchTerm.toLowerCase())) ||\n                         (user.lastName && user.lastName.toLowerCase().includes(userSearchTerm.toLowerCase()));\n\n    const matchesRole = userRoleFilter === 'all' || user.role === userRoleFilter;\n    const matchesStatus = userStatusFilter === 'all' ||\n                         (userStatusFilter === 'active' && user.isActive) ||\n                         (userStatusFilter === 'inactive' && !user.isActive);\n\n    return matchesSearch && matchesRole && matchesStatus;\n  });\n\n  if (loading || loadingData) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"text-xl text-gray-900 dark:text-white\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!user || !isAdmin) {\n    return null;\n  }\n\n  const renderOverview = () => (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-md flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">👥</span>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{users.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 w-8 h-8 bg-green-500 dark:bg-green-600 rounded-md flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">🏢</span>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Brands</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {brands.filter(b => b.isActive).length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 w-8 h-8 bg-purple-500 dark:bg-purple-600 rounded-md flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">📄</span>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Templates</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{templates.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 w-8 h-8 bg-orange-500 dark:bg-orange-600 rounded-md flex items-center justify-center\">\n              <span className=\"text-white text-sm font-bold\">📊</span>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">BVAs Created</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{bvaInstances.length}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Users</h3>\n            <div className=\"space-y-3\">\n              {users.slice(0, 5).map((user) => (\n                <div key={user.id} className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">{user.email}</p>\n                    <p className=\"text-xs text-gray-500\">\n                      {new Date(user.createdAt).toLocaleDateString()}\n                    </p>\n                  </div>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'\n                  }`}>\n                    {user.role}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent BVAs</h3>\n            <div className=\"space-y-3\">\n              {bvaInstances.slice(0, 5).map((bva) => (\n                <div key={bva.id} className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">{bva.name}</p>\n                    <p className=\"text-xs text-gray-500\">{bva.clientName}</p>\n                  </div>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    bva.status === 'completed' ? 'bg-green-100 text-green-800' :\n                    bva.status === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {bva.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderBrands = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Brands Management</h2>\n        <button\n          onClick={() => setShowNewBrandForm(true)}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700\"\n        >\n          Add New Brand\n        </button>\n      </div>\n\n      {showNewBrandForm && (\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Create New Brand</h3>\n          <form onSubmit={handleCreateBrand} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Brand Name *\n              </label>\n              <input\n                type=\"text\"\n                value={newBrand.name}\n                onChange={(e) => setNewBrand({ ...newBrand, name: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Description\n              </label>\n              <textarea\n                value={newBrand.description}\n                onChange={(e) => setNewBrand({ ...newBrand, description: e.target.value })}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Logo URL\n              </label>\n              <input\n                type=\"url\"\n                value={newBrand.logoUrl}\n                onChange={(e) => setNewBrand({ ...newBrand, logoUrl: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n            <div className=\"flex space-x-3\">\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700\"\n              >\n                Create Brand\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setShowNewBrandForm(false)}\n                className=\"bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400\"\n              >\n                Cancel\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Brand\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Templates\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Created\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {brands.map((brand) => (\n              <tr key={brand.id}>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    {brand.logoUrl && (\n                      <img className=\"h-8 w-8 rounded mr-3\" src={brand.logoUrl} alt={brand.name} />\n                    )}\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{brand.name}</div>\n                      <div className=\"text-sm text-gray-500\">{brand.description}</div>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {templates.filter(t => t.brandId === brand.id).length}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    brand.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                  }`}>\n                    {brand.isActive ? 'Active' : 'Inactive'}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {new Date(brand.createdAt).toLocaleDateString()}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                  <button\n                    onClick={() => toggleBrandStatus(brand.id, brand.isActive)}\n                    className={`mr-3 ${\n                      brand.isActive\n                        ? 'text-red-600 hover:text-red-900'\n                        : 'text-green-600 hover:text-green-900'\n                    }`}\n                  >\n                    {brand.isActive ? 'Deactivate' : 'Activate'}\n                  </button>\n                  <Link\n                    href={`/admin/brands/${brand.id}`}\n                    className=\"text-blue-600 hover:text-blue-900\"\n                  >\n                    Edit\n                  </Link>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n\n  const renderTemplates = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Templates Management</h2>\n        <Link\n          href=\"/admin/templates/new\"\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700\"\n        >\n          Create New Template\n        </Link>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Template\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Brand\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Category\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Version\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Progress\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {templates.map((template) => {\n              const brand = brands.find(b => b.id === template.brandId);\n              const step1Complete = template.step1Completed || false;\n              const step2Complete = template.step2Completed || false;\n              const progressPercentage = step1Complete && step2Complete ? 100 : step1Complete ? 50 : 0;\n\n              return (\n                <tr key={template.id}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{template.name}</div>\n                      <div className=\"text-sm text-gray-500\">{template.description}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {brand?.name || 'Unknown'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {template.category}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {template.templateVersion || '1.0'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-16 bg-gray-200 rounded-full h-2 mr-2\">\n                        <div\n                          className={`h-2 rounded-full ${progressPercentage === 100 ? 'bg-green-500' : 'bg-blue-500'}`}\n                          style={{ width: `${progressPercentage}%` }}\n                        ></div>\n                      </div>\n                      <span className=\"text-xs text-gray-500\">{progressPercentage}%</span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        template.status === 'published'\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-yellow-100 text-yellow-800'\n                      }`}>\n                        {template.status === 'published' ? 'Published' : 'Draft'}\n                      </span>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        template.isActive ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                      }`}>\n                        {template.isActive ? 'Active' : 'Inactive'}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex flex-col space-y-1\">\n                      <Link\n                        href={`/admin/templates/${template.id}/edit`}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                      >\n                        {template.status === 'published' ? 'View' : 'Edit'}\n                      </Link>\n                      {template.status === 'published' && (\n                        <Link\n                          href={`/admin/templates/${template.id}/edit`}\n                          className=\"text-green-600 hover:text-green-900\"\n                        >\n                          Create New Version\n                        </Link>\n                      )}\n                      <button\n                        onClick={() => toggleTemplateStatus(template.id, template.isActive)}\n                        className={`text-left ${\n                          template.isActive\n                            ? 'text-red-600 hover:text-red-900'\n                            : 'text-green-600 hover:text-green-900'\n                        }`}\n                      >\n                        {template.isActive ? 'Deactivate' : 'Activate'}\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n\n  const renderUsers = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">User Management</h2>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {/* Search */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search Users\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search by name or email...\"\n              value={userSearchTerm}\n              onChange={(e) => setUserSearchTerm(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n\n          {/* Role Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Filter by Role\n            </label>\n            <select\n              value={userRoleFilter}\n              onChange={(e) => setUserRoleFilter(e.target.value as 'all' | 'admin' | 'user')}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"all\">All Roles</option>\n              <option value=\"admin\">Admin</option>\n              <option value=\"user\">User</option>\n            </select>\n          </div>\n\n          {/* Status Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Filter by Status\n            </label>\n            <select\n              value={userStatusFilter}\n              onChange={(e) => setUserStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"active\">Active</option>\n              <option value=\"inactive\">Inactive</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-600\">\n          <thead className=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                User\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Role\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Last Login\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Created\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Actions\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n            {filteredUsers.map((user) => (\n              <tr key={user.id}>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {user.firstName && user.lastName\n                        ? `${user.firstName} ${user.lastName}`\n                        : user.email}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">{user.email}</div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    user.role === 'admin'\n                      ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n                      : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'\n                  }`}>\n                    {user.role}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    user.isActive\n                      ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'\n                      : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n                  }`}>\n                    {user.isActive ? 'Active' : 'Inactive'}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                  {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                  {new Date(user.createdAt).toLocaleDateString()}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                  <button\n                    onClick={() => handleEditUser(user)}\n                    className=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\"\n                  >\n                    Edit\n                  </button>\n                  <button\n                    onClick={() => toggleUserStatus(user.id, user.isActive)}\n                    className={`${\n                      user.isActive\n                        ? 'text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300'\n                        : 'text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300'\n                    }`}\n                  >\n                    {user.isActive ? 'Disable' : 'Enable'}\n                  </button>\n                  <button\n                    onClick={() => handleResetPassword(user.email)}\n                    className=\"text-orange-600 dark:text-orange-400 hover:text-orange-900 dark:hover:text-orange-300\"\n                  >\n                    Reset Password\n                  </button>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n\n        {filteredUsers.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 dark:text-gray-500 text-4xl mb-4\">👥</div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">No users found</h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Try adjusting your search or filter criteria.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  const renderSettings = () => {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">System Settings</h2>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                AI API Configuration\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                Configure API keys for Claude AI and OpenAI to enable AI artifact generation in templates.\n              </p>\n              <Link\n                href=\"/admin/settings\"\n                className=\"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-flex items-center\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                Configure API Keys\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                System Information\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white\">Total Brands</div>\n                  <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">{brands.length}</div>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white\">Total Templates</div>\n                  <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">{templates.length}</div>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white\">Total Users</div>\n                  <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">{users.length}</div>\n                </div>\n                <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white\">BVA Instances</div>\n                  <div className=\"text-2xl font-bold text-orange-600 dark:text-orange-400\">{bvaInstances.length}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation title=\"VALTICS AI - Admin\" />\n\n      {/* Main Content */}\n      <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">Admin Dashboard</h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n              Manage brands, templates, users, and system settings.\n            </p>\n          </div>\n\n          {/* Tabs */}\n          <div className=\"mb-8\">\n            <nav className=\"flex space-x-8\">\n              {[\n                { id: 'overview', name: 'Overview' },\n                { id: 'brands', name: 'Brands' },\n                { id: 'templates', name: 'Templates' },\n                { id: 'users', name: 'Users' },\n                { id: 'settings', name: 'Settings' }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'\n                  }`}\n                >\n                  {tab.name}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Tab Content */}\n          {activeTab === 'overview' && renderOverview()}\n          {activeTab === 'brands' && renderBrands()}\n          {activeTab === 'templates' && renderTemplates()}\n          {activeTab === 'users' && renderUsers()}\n          {activeTab === 'settings' && renderSettings()}\n        </div>\n      </div>\n\n      {/* User Edit Modal */}\n      {showUserModal && editingUser && (\n        <UserEditModal\n          user={editingUser}\n          onSave={handleSaveUser}\n          onClose={() => {\n            setShowUserModal(false);\n            setEditingUser(null);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n\n// User Edit Modal Component\ninterface UserEditModalProps {\n  user: User;\n  onSave: (userData: Partial<User>) => void;\n  onClose: () => void;\n}\n\nfunction UserEditModal({ user, onSave, onClose }: UserEditModalProps) {\n  const [formData, setFormData] = useState({\n    firstName: user.firstName || '',\n    lastName: user.lastName || '',\n    email: user.email,\n    role: user.role,\n    isActive: user.isActive\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSave(formData);\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full\">\n        <div className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">Edit User</h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <span className=\"sr-only\">Close</span>\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                First Name\n              </label>\n              <input\n                type=\"text\"\n                value={formData.firstName}\n                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter first name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Last Name\n              </label>\n              <input\n                type=\"text\"\n                value={formData.lastName}\n                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter last name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Role\n              </label>\n              <select\n                value={formData.role}\n                onChange={(e) => setFormData({ ...formData, role: e.target.value as 'admin' | 'user' })}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"user\">User</option>\n                <option value=\"admin\">Admin</option>\n              </select>\n            </div>\n\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"isActive\"\n                checked={formData.isActive}\n                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded\"\n              />\n              <label htmlFor=\"isActive\" className=\"ml-2 block text-sm text-gray-700 dark:text-gray-300\">\n                Active User\n              </label>\n            </div>\n\n            <div className=\"flex space-x-3 pt-4\">\n              <button\n                type=\"submit\"\n                className=\"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n              >\n                Save Changes\n              </button>\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500\"\n              >\n                Cancel\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AAXA;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,aAAa;QAAI,SAAS;IAAG;IAElF,wBAAwB;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAExF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG;YACnC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;QAAS;KAAO;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,SAAS;YACnB;QACF;IACF,GAAG;QAAC;QAAM;KAAQ;IAElB,MAAM,iBAAiB;QACrB,IAAI;YACF,eAAe;YAEf,eAAe;YACf,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WAAW,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;YACpE,MAAM,iBAAiB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACrC,MAAM,aAAa,eAAe,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACjD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,UAAU;YAEV,kBAAkB;YAClB,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YAC/E,MAAM,oBAAoB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACxC,MAAM,gBAAgB,kBAAkB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACvD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,aAAa;YAEb,cAAc;YACd,MAAM,aAAa,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACpC,MAAM,YAAY,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC/C,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,SAAS;YAET,sBAAsB;YACtB,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,iBAAiB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YAC5E,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YAClC,MAAM,UAAU,YAAY,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3C,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,gBAAgB;QAElB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WAAW;gBACrC,GAAG,QAAQ;gBACX,UAAU;gBACV,WAAW,IAAI;YACjB;YAEA,YAAY;gBAAE,MAAM;gBAAI,aAAa;gBAAI,SAAS;YAAG;YACrD,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,OAAO,SAAiB;QAChD,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,UAAU;gBAC1C,UAAU,CAAC;YACb;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,uBAAuB,OAAO,YAAoB;QACtD,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa,aAAa;gBAChD,UAAU,CAAC;YACb;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,4BAA4B;IAC5B,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,aAAa;QAElB,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,YAAY,EAAE,GAAG;gBAChD,GAAG,QAAQ;gBACX,WAAW,IAAI;YACjB;YAEA,iBAAiB;YACjB,eAAe;YACf;YACA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,SAAS;gBACxC,UAAU,CAAC;gBACX,WAAW,IAAI;YACjB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD;YACnB,MAAM,CAAA,GAAA,6NAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;YACnC,MAAM,CAAC,6BAA6B,EAAE,OAAO;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAC5D,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAClF,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW;QAEtG,MAAM,cAAc,mBAAmB,SAAS,KAAK,IAAI,KAAK;QAC9D,MAAM,gBAAgB,qBAAqB,SACrB,qBAAqB,YAAY,KAAK,QAAQ,IAC9C,qBAAqB,cAAc,CAAC,KAAK,QAAQ;QAEvE,OAAO,iBAAiB,eAAe;IACzC;IAEA,IAAI,WAAW,aAAa;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAwC;;;;;;;;;;;IAG7D;IAEA,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,OAAO;IACT;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAAoD,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAKnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAM9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAAoD,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAKvF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;0DACpE,8OAAC;gDAAE,WAAU;0DAAoD,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO5F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACtB,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAqC,KAAK,KAAK;;;;;;0EAC5D,8OAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;kEAGhD,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,IAAI,KAAK,UAAU,4BAA4B,6BACpD;kEACC,KAAK,IAAI;;;;;;;+CAVJ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;sCAkBzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,8OAAC;gDAAiB,WAAU;;kEAC1B,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAqC,IAAI,IAAI;;;;;;0EAC1D,8OAAC;gEAAE,WAAU;0EAAyB,IAAI,UAAU;;;;;;;;;;;;kEAEtD,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,IAAI,MAAM,KAAK,cAAc,gCAC7B,IAAI,MAAM,KAAK,gBAAgB,kCAC/B,6BACA;kEACC,IAAI,MAAM;;;;;;;+CAVL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqB9B,MAAM,eAAe,kBACnB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCACX;;;;;;;;;;;;gBAKF,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAK,UAAU;4BAAmB,WAAU;;8CAC3C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACjE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpE,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,8OAAC;gCAAM,WAAU;0CACd,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,OAAO,kBACZ,8OAAC;4DAAI,WAAU;4DAAuB,KAAK,MAAM,OAAO;4DAAE,KAAK,MAAM,IAAI;;;;;;sEAE3E,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAqC,MAAM,IAAI;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;8EAAyB,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;0DAI/D,8OAAC;gDAAG,WAAU;0DACX,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,MAAM,EAAE,EAAE,MAAM;;;;;;0DAEvD,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAK,WAAW,CAAC,2CAA2C,EAC3D,MAAM,QAAQ,GAAG,gCAAgC,2BACjD;8DACC,MAAM,QAAQ,GAAG,WAAW;;;;;;;;;;;0DAGjC,8OAAC;gDAAG,WAAU;0DACX,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;0DAE/C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDACC,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE,MAAM,QAAQ;wDACzD,WAAW,CAAC,KAAK,EACf,MAAM,QAAQ,GACV,oCACA,uCACJ;kEAED,MAAM,QAAQ,GAAG,eAAe;;;;;;kEAEnC,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;wDACjC,WAAU;kEACX;;;;;;;;;;;;;uCAvCI,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmD7B,MAAM,kBAAkB,kBACtB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,8OAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,8OAAC;gCAAM,WAAU;0CACd,UAAU,GAAG,CAAC,CAAC;oCACd,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,OAAO;oCACxD,MAAM,gBAAgB,SAAS,cAAc,IAAI;oCACjD,MAAM,gBAAgB,SAAS,cAAc,IAAI;oCACjD,MAAM,qBAAqB,iBAAiB,gBAAgB,MAAM,gBAAgB,KAAK;oCAEvF,qBACE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAqC,SAAS,IAAI;;;;;;sEACjE,8OAAC;4DAAI,WAAU;sEAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;0DAGhE,8OAAC;gDAAG,WAAU;0DACX,OAAO,QAAQ;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DACX,SAAS,QAAQ;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DACX,SAAS,eAAe,IAAI;;;;;;0DAE/B,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAW,CAAC,iBAAiB,EAAE,uBAAuB,MAAM,iBAAiB,eAAe;gEAC5F,OAAO;oEAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAG7C,8OAAC;4DAAK,WAAU;;gEAAyB;gEAAmB;;;;;;;;;;;;;;;;;;0DAGhE,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,MAAM,KAAK,cAChB,gCACA,iCACJ;sEACC,SAAS,MAAM,KAAK,cAAc,cAAc;;;;;;sEAEnD,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,SAAS,QAAQ,GAAG,8BAA8B,6BAClD;sEACC,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;0DAItC,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;4DAC5C,WAAU;sEAET,SAAS,MAAM,KAAK,cAAc,SAAS;;;;;;wDAE7C,SAAS,MAAM,KAAK,6BACnB,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;4DAC5C,WAAU;sEACX;;;;;;sEAIH,8OAAC;4DACC,SAAS,IAAM,qBAAqB,SAAS,EAAE,EAAE,SAAS,QAAQ;4DAClE,WAAW,CAAC,UAAU,EACpB,SAAS,QAAQ,GACb,oCACA,uCACJ;sEAED,SAAS,QAAQ,GAAG,eAAe;;;;;;;;;;;;;;;;;;uCAnEnC,SAAS,EAAE;;;;;gCAyExB;;;;;;;;;;;;;;;;;;;;;;;IAOV,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;;;;;;8BAInE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;;;;;;;;;;;0CAKd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;0CAKzB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,8OAAC;oCAAM,WAAU;8CACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,KAAK,SAAS,IAAI,KAAK,QAAQ,GAC5B,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,KAAK,KAAK;;;;;;0EAEhB,8OAAC;gEAAI,WAAU;0EAA4C,KAAK,KAAK;;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,IAAI,KAAK,UACV,8DACA,iEACJ;kEACC,KAAK,IAAI;;;;;;;;;;;8DAGd,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,QAAQ,GACT,sEACA,6DACJ;kEACC,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;8DAGhC,8OAAC;oDAAG,WAAU;8DACX,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAAK;;;;;;8DAExE,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;8DAE9C,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DACC,SAAS,IAAM,eAAe;4DAC9B,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,QAAQ;4DACtD,WAAW,GACT,KAAK,QAAQ,GACT,8EACA,qFACJ;sEAED,KAAK,QAAQ,GAAG,YAAY;;;;;;sEAE/B,8OAAC;4DACC,SAAS,IAAM,oBAAoB,KAAK,KAAK;4DAC7C,WAAU;sEACX;;;;;;;;;;;;;2CAvDI,KAAK,EAAE;;;;;;;;;;;;;;;;wBAgErB,cAAc,MAAM,KAAK,mBACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAiD;;;;;;8CAChE,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;IAS1D,MAAM,iBAAiB;QACrB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;;;;;;8BAGtE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAG7D,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;;kEACtE,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;kEACrE,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;4CACjE;;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAoD;;;;;;kEACnE,8OAAC;wDAAI,WAAU;kEAAuD,OAAO,MAAM;;;;;;;;;;;;0DAErF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAoD;;;;;;kEACnE,8OAAC;wDAAI,WAAU;kEAAyD,UAAU,MAAM;;;;;;;;;;;;0DAE1F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAoD;;;;;;kEACnE,8OAAC;wDAAI,WAAU;kEAA2D,MAAM,MAAM;;;;;;;;;;;;0DAExF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAoD;;;;;;kEACnE,8OAAC;wDAAI,WAAU;kEAA2D,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ7G;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,UAAU;gBAAC,OAAM;;;;;;0BAGlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAMvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,IAAI;wCAAY,MAAM;oCAAW;oCACnC;wCAAE,IAAI;wCAAU,MAAM;oCAAS;oCAC/B;wCAAE,IAAI;wCAAa,MAAM;oCAAY;oCACrC;wCAAE,IAAI;wCAAS,MAAM;oCAAQ;oCAC7B;wCAAE,IAAI;wCAAY,MAAM;oCAAW;iCACpC,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,qDACA,qJACJ;kDAED,IAAI,IAAI;uCARJ,IAAI,EAAE;;;;;;;;;;;;;;;wBAelB,cAAc,cAAc;wBAC5B,cAAc,YAAY;wBAC1B,cAAc,eAAe;wBAC7B,cAAc,WAAW;wBACzB,cAAc,cAAc;;;;;;;;;;;;YAKhC,iBAAiB,6BAChB,8OAAC;gBACC,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,iBAAiB;oBACjB,eAAe;gBACjB;;;;;;;;;;;;AAKV;AASA,SAAS,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAsB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW,KAAK,SAAS,IAAI;QAC7B,UAAU,KAAK,QAAQ,IAAI;QAC3B,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;QACf,UAAU,KAAK,QAAQ;IACzB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAK3E,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,SAAS;wCACzB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACtE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACrE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAClE,WAAU;wCACV,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAqB;wCACrF,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,SAAS,QAAQ;wCAC1B,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,UAAU,EAAE,MAAM,CAAC,OAAO;4CAAC;wCACvE,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAsD;;;;;;;;;;;;0CAK5F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}