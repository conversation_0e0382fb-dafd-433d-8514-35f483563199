import * as admin from 'firebase-admin';
import * as CryptoJ<PERSON> from 'crypto-js';
import { AIService } from './aiService';

export class APIConfigService {
  // Use the same encryption key as the frontend
  private static readonly ENCRYPTION_KEY = 'raviteja'; // This should match NEXT_PUBLIC_ENCRYPTION_KEY

  /**
   * Decrypt encrypted data
   */
  private static decrypt(encryptedData: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt API key');
    }
  }

  /**
   * Get API configuration from Firestore
   */
  static async getAPIConfig(): Promise<any> {
    try {
      // Use the same collection and document as the frontend
      const configDoc = await admin.firestore()
        .collection('apiConfig')
        .doc('main')
        .get();

      if (!configDoc.exists) {
        console.log('API config document does not exist');
        return null;
      }

      const data = configDoc.data();
      console.log('API config found:', {
        hasClaudeKey: !!data?.claudeApiKey,
        hasOpenaiKey: !!data?.openaiApiKey,
        hasClaudeModelConfig: !!data?.claudeModelConfig,
        hasOpenaiModelConfig: !!data?.openaiModelConfig
      });

      // Add default model configurations if not present
      if (data) {
        if (!data.claudeModelConfig) {
          data.claudeModelConfig = {
            model: 'claude-3-5-sonnet-20241022',
            maxTokens: 4000,
            temperature: 0.7
          };
        }
        if (!data.openaiModelConfig) {
          data.openaiModelConfig = {
            model: 'gpt-4-vision-preview',
            maxTokens: 4000,
            temperature: 0.7
          };
        }
      }

      return data;
    } catch (error) {
      console.error('Error fetching API config:', error);
      return null;
    }
  }

  /**
   * Check if API keys are configured
   */
  static async areAPIKeysConfigured(): Promise<{
    claudeConfigured: boolean;
    openaiConfigured: boolean;
    anyConfigured: boolean;
  }> {
    const config = await this.getAPIConfig();
    
    const claudeConfigured = !!(config?.claudeApiKey);
    const openaiConfigured = !!(config?.openaiApiKey);
    
    return {
      claudeConfigured,
      openaiConfigured,
      anyConfigured: claudeConfigured || openaiConfigured
    };
  }

  /**
   * Initialize AI services with stored API keys
   */
  static async initializeAIServices(): Promise<void> {
    const config = await this.getAPIConfig();
    
    if (!config) {
      throw new Error('No API configuration found');
    }

    // Initialize Claude if key is available
    if (config.claudeApiKey) {
      try {
        const claudeKey = this.decrypt(config.claudeApiKey);
        AIService.initializeClaude(claudeKey);
        console.log('Claude API initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Claude API:', error);
      }
    }

    // Initialize OpenAI if key is available
    if (config.openaiApiKey) {
      try {
        const openaiKey = this.decrypt(config.openaiApiKey);
        AIService.initializeOpenAI(openaiKey);
        console.log('OpenAI API initialized successfully');
      } catch (error) {
        console.error('Failed to initialize OpenAI API:', error);
      }
    }
  }
}
