"use strict";exports.id=948,exports.ids=[948],exports.modules={5481:(e,r,a)=>{a.d(r,{A:()=>x});var t=a(60687),l=a(85814),d=a.n(l),s=a(30474),o=a(51108),i=a(16189),n=a(27436),c=a(31769);function x({title:e="VALTICS AI",showBackButton:r=!1,backUrl:a="/dashboard",backText:l="← Back to Dashboard"}){let{user:x,logOut:b,isAdmin:h}=(0,o.A)(),g=(0,i.useRouter)(),m=async()=>{try{await b(),g.push("/")}catch(e){console.error("Error logging out:",e)}};return x?(0,t.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)(d(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,t.jsx)(s.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,t.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[r&&(0,t.jsx)(d(),{href:a,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:l}),!r&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,t.jsx)(d(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),h&&(0,t.jsx)(d(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,t.jsx)(d(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,t.jsx)(c.I,{}),(0,t.jsx)(n.default,{}),(0,t.jsx)("button",{onClick:m,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},30457:(e,r,a)=>{a.d(r,{A:()=>o});var t=a(60687),l=a(76180),d=a.n(l),s=a(43210);let o=({value:e,onChange:r,placeholder:a="Enter text...",className:l="",disabled:o=!1})=>{let i=(0,s.useRef)(null),n=(e,a)=>{document.execCommand(e,!1,a),i.current&&(i.current.focus(),r(i.current.innerHTML))},c=e=>{n(e?"insertOrderedList":"insertUnorderedList")};return(0,t.jsxs)("div",{className:`jsx-570c7a2a0768ea83 simple-rich-text-editor ${l}`,children:[(0,t.jsxs)("div",{className:"jsx-570c7a2a0768ea83 border border-gray-300 dark:border-gray-600 border-b-0 bg-gray-50 dark:bg-gray-700 p-2 flex flex-wrap gap-1 rounded-t-md",children:[(0,t.jsx)("button",{type:"button",onClick:()=>n("bold"),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:(0,t.jsx)("strong",{className:"jsx-570c7a2a0768ea83",children:"B"})}),(0,t.jsx)("button",{type:"button",onClick:()=>n("italic"),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:(0,t.jsx)("em",{className:"jsx-570c7a2a0768ea83",children:"I"})}),(0,t.jsx)("button",{type:"button",onClick:()=>n("underline"),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:(0,t.jsx)("u",{className:"jsx-570c7a2a0768ea83",children:"U"})}),(0,t.jsx)("div",{className:"jsx-570c7a2a0768ea83 w-px bg-gray-300 dark:bg-gray-600 mx-1"}),(0,t.jsx)("button",{type:"button",onClick:()=>c(!1),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:"• List"}),(0,t.jsx)("button",{type:"button",onClick:()=>c(!0),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:"1. List"}),(0,t.jsx)("div",{className:"jsx-570c7a2a0768ea83 w-px bg-gray-300 dark:bg-gray-600 mx-1"}),(0,t.jsxs)("select",{onChange:e=>n("formatBlock",e.target.value),disabled:o,defaultValue:"",className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none",children:[(0,t.jsx)("option",{value:"",className:"jsx-570c7a2a0768ea83",children:"Normal"}),(0,t.jsx)("option",{value:"h1",className:"jsx-570c7a2a0768ea83",children:"Heading 1"}),(0,t.jsx)("option",{value:"h2",className:"jsx-570c7a2a0768ea83",children:"Heading 2"}),(0,t.jsx)("option",{value:"h3",className:"jsx-570c7a2a0768ea83",children:"Heading 3"}),(0,t.jsx)("option",{value:"h4",className:"jsx-570c7a2a0768ea83",children:"Heading 4"}),(0,t.jsx)("option",{value:"h5",className:"jsx-570c7a2a0768ea83",children:"Heading 5"}),(0,t.jsx)("option",{value:"h6",className:"jsx-570c7a2a0768ea83",children:"Heading 6"})]})]}),(0,t.jsx)("div",{ref:i,contentEditable:!o,onInput:()=>{i.current&&r(i.current.innerHTML)},dangerouslySetInnerHTML:{__html:e},style:{backgroundColor:o?"#f9fafb":void 0},"data-placeholder":a,className:`jsx-570c7a2a0768ea83 
          min-h-[150px] p-3 border border-gray-300 dark:border-gray-600 rounded-b-md
          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          ${o?"bg-gray-100 dark:bg-gray-800 cursor-not-allowed":""}
        `}),(0,t.jsx)(d(),{id:"570c7a2a0768ea83",children:".simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83{outline:none}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h1.jsx-570c7a2a0768ea83{font-size:2em;font-weight:bold;margin:.67em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h2.jsx-570c7a2a0768ea83{font-size:1.5em;font-weight:bold;margin:.75em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h3.jsx-570c7a2a0768ea83{font-size:1.17em;font-weight:bold;margin:.83em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h4.jsx-570c7a2a0768ea83{font-size:1em;font-weight:bold;margin:1.12em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h5.jsx-570c7a2a0768ea83{font-size:.83em;font-weight:bold;margin:1.5em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h6.jsx-570c7a2a0768ea83{font-size:.75em;font-weight:bold;margin:1.67em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 ul.jsx-570c7a2a0768ea83{list-style-type:disc;margin:1em 0;padding-left:2em}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 ol.jsx-570c7a2a0768ea83{list-style-type:decimal;margin:1em 0;padding-left:2em}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 li.jsx-570c7a2a0768ea83{margin:.5em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 p.jsx-570c7a2a0768ea83{margin:1em 0}"})]})}},31769:(e,r,a)=>{a.d(r,{A:()=>n,I:()=>c});var t=a(60687),l=a(43210),d=a(85814),s=a.n(d),o=a(51108),i=a(53836);function n(){let{user:e}=(0,o.A)(),[r,a]=(0,l.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||r)return null;let{message:d,type:n,daysRemaining:c}=(0,i.Mo)(e);if(!d)return null;let x=()=>{switch(n){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,t.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(n){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:"error"===n?(0,t.jsx)("svg",{className:`h-5 w-5 ${x()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===n?(0,t.jsx)("svg",{className:`h-5 w-5 ${x()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,t.jsx)("svg",{className:`h-5 w-5 ${x()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm font-medium",children:d})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(s(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(n){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,t.jsx)("button",{onClick:()=>a(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,t.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function c(){let{user:e}=(0,o.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:r}=(0,i.Mo)(e);return r<=0?(0,t.jsx)(s(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,t.jsxs)(s(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${r<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[r," day",1===r?"":"s"," left"]})}},49615:(e,r,a)=>{a.d(r,{A:()=>x});var t=a(60687),l=a(76180),d=a.n(l),s=a(43210),o=a(30036),i=a(30457);class n extends s.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(e,r){console.error("ReactQuill Error:",e,r),this.props.onError()}render(){return this.state.hasError?null:this.props.children}}let c=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\RichTextEditor.tsx -> react-quill"]},ssr:!1,loading:()=>(0,t.jsx)("div",{className:"h-32 bg-gray-100 dark:bg-gray-700 rounded animate-pulse"})}),x=({value:e,onChange:r,placeholder:a="Enter text...",className:l="",disabled:o=!1})=>{let[x,b]=(0,s.useState)(!1),[h,g]=(0,s.useState)(!1);(0,s.useEffect)(()=>{g(!0)},[]);let m={toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],["link"],[{align:[]}],["clean"]]};return x||!h?(0,t.jsx)(i.A,{value:e,onChange:r,placeholder:a,className:l,disabled:o}):(0,t.jsxs)("div",{className:`jsx-3979297ce4da0409 rich-text-editor ${l}`,children:[(0,t.jsx)("div",{className:"jsx-3979297ce4da0409 mb-2",children:(0,t.jsx)("button",{type:"button",onClick:()=>b(!0),className:"jsx-3979297ce4da0409 text-xs text-blue-600 dark:text-blue-400 hover:underline",children:"Switch to Simple Editor"})}),(0,t.jsx)(n,{onError:()=>{console.warn("ReactQuill error detected, falling back to simple editor"),b(!0)},children:(0,t.jsx)(c,{theme:"snow",value:e,onChange:r,modules:m,formats:["header","bold","italic","underline","strike","list","bullet","indent","link","align"],placeholder:a,readOnly:o,style:{backgroundColor:o?"#f9fafb":"white"}})}),(0,t.jsx)(d(),{id:"3979297ce4da0409",children:".rich-text-editor .ql-editor{min-height:150px}.rich-text-editor .ql-toolbar{border-top:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.rich-text-editor .ql-container{border-bottom:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.dark .rich-text-editor .ql-toolbar{border-color:#4b5563;background-color:#374151}.dark .rich-text-editor .ql-container{border-color:#4b5563;background-color:#1f2937}.dark .rich-text-editor .ql-editor{color:#f9fafb}.dark .rich-text-editor .ql-toolbar .ql-stroke{stroke:#9ca3af}.dark .rich-text-editor .ql-toolbar .ql-fill{fill:#9ca3af}"})]})}}};