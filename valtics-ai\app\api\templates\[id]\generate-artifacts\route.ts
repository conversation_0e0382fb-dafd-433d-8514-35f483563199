import { NextRequest, NextResponse } from 'next/server';
import { doc, updateDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { APIConfigService } from '@/lib/services/apiConfigService';
import { AIService } from '@/lib/services/aiService';
import { FileProcessor } from '@/lib/utils/fileProcessor';
import { StorageService } from '@/lib/firebase/storage';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const templateId = params.id;
    const { selectedLLM, companyName } = await request.json();

    // Validate required parameters
    if (!selectedLLM || !['claude', 'openai'].includes(selectedLLM)) {
      return NextResponse.json(
        { error: 'Invalid or missing LLM selection' },
        { status: 400 }
      );
    }

    // Get template data from request or fetch from database
    const templateData = await request.json();
    
    if (!templateData.enterpriseNeedFileUrl || 
        !templateData.solutionDescriptionFileUrl || 
        !templateData.riskOfNoInvestmentFileUrl) {
      return NextResponse.json(
        { error: 'Missing required document files' },
        { status: 400 }
      );
    }

    // Generate the AI prompt
    const prompt = `Generate three visual artifacts presented as they appear in the three attached files. The content should be in an executive-level tone, a descriptive format, with no bullet points. Use complete sentences that are professionally structured. Use a wide format, like a PowerPoint-ready format. Each of the three artifacts begins with a header containing the [${templateData.solutionName}], followed by [Business Value Assessment]. In each artifact, also include a subtitle labeled [${companyName || 'Company Name'}]. In the first artifact, include a section subtitle labeled 'Enterprise Need' with the corresponding need description. In the second artifact, include a section subtitle labeled 'Proposed Solution' with the corresponding solution description. In the third artifact, include a section subtitle labeled 'Risk of No Investment' with the corresponding solution description. Use only the content from these three attached files and do not add any other information other than the items requested in this prompt.`;

    // Update template status to indicate processing has started
    await updateDoc(doc(db, 'templates', templateId), {
      status: 'generating-artifacts',
      selectedLLM,
      aiPrompt: prompt,
      updatedAt: Timestamp.now()
    });

    // Start asynchronous processing
    processAIGeneration(templateId, selectedLLM, prompt, templateData, companyName);

    return NextResponse.json({
      success: true,
      message: 'AI artifact generation started',
      prompt
    });

  } catch (error) {
    console.error('Error starting AI generation:', error);
    return NextResponse.json(
      { error: 'Failed to start AI generation' },
      { status: 500 }
    );
  }
}

async function processAIGeneration(
  templateId: string,
  selectedLLM: 'claude' | 'openai',
  prompt: string,
  templateData: any,
  companyName?: string
) {
  try {
    // Initialize AI services with stored API keys
    await APIConfigService.initializeAIServices();

    console.log('Preparing documents for AI processing...');

    // Prepare files for AI attachment instead of extracting text
    const [enterpriseNeedFile, solutionFile, riskFile] = await Promise.all([
      FileProcessor.prepareFileForAI(templateData.enterpriseNeedFileUrl),
      FileProcessor.prepareFileForAI(templateData.solutionDescriptionFileUrl),
      FileProcessor.prepareFileForAI(templateData.riskOfNoInvestmentFileUrl)
    ]);

    // Prepare document context with files
    const documentFiles = {
      enterpriseNeedFile,
      solutionFile,
      riskFile
    };

    console.log(`Generating AI artifacts with ${selectedLLM} using file attachments...`);

    // Generate artifacts using AI with file attachments
    const aiResponse = await AIService.generateArtifactsWithFilesRetry(
      selectedLLM,
      prompt,
      documentFiles,
      templateData.solutionName,
      companyName || 'Company Name'
    );

    console.log('AI artifacts generated successfully');

    // Upload artifacts to Firebase Storage
    console.log('Uploading artifacts to storage...');
    const artifactUrls = await StorageService.uploadAIArtifacts(aiResponse, templateId);

    // Prepare final artifacts object
    const generatedArtifacts = {
      enterpriseNeedArtifact: aiResponse.enterpriseNeedArtifact,
      solutionArtifact: aiResponse.solutionArtifact,
      riskArtifact: aiResponse.riskArtifact,
      generatedAt: new Date(),
      ...artifactUrls
    };

    // Update template with generated artifacts and mark as completed
    await updateDoc(doc(db, 'templates', templateId), {
      status: 'draft',
      generatedArtifacts,
      step3Completed: true,
      updatedAt: Timestamp.now()
    });

    console.log(`AI artifact generation completed successfully for template: ${templateId}`);

  } catch (error) {
    console.error(`Error processing AI generation for template ${templateId}:`, error);
    
    // Update status to indicate failure
    await updateDoc(doc(db, 'templates', templateId), {
      status: 'generation-failed',
      generationError: error instanceof Error ? error.message : 'Unknown error occurred',
      updatedAt: Timestamp.now()
    });

    // Log detailed error for debugging
    console.error('AI Generation Error Details:', {
      templateId,
      selectedLLM,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}
