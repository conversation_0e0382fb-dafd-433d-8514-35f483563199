(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[122],{288:(e,t,a)=>{Promise.resolve().then(a.bind(a,6331))},477:()=>{},6331:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),n=a(2115),o=a(3274),i=a(5695),s=a(1573),l=a(5317),c=a(1138),d=a(6281),u=a(9882);class p{static initializeClaude(e){this.claudeClient=new d.Ay({apiKey:e})}static initializeOpenAI(e){this.openaiClient=new u.Ay({apiKey:e})}static async testClaudeConnection(e){try{let t=new d.Ay({apiKey:e}),a=await t.messages.create({model:"claude-3-haiku-20240307",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]});return"text"===a.content[0].type&&a.content[0].text.includes("OK")}catch(e){return console.error("Claude connection test failed:",e),!1}}static async testOpenAIConnection(e){try{var t,a,r;let n=new u.Ay({apiKey:e});return(null==(r=(await n.chat.completions.create({model:"gpt-3.5-turbo",max_tokens:10,messages:[{role:"user",content:'Test connection. Respond with "OK".'}]})).choices[0])||null==(a=r.message)||null==(t=a.content)?void 0:t.includes("OK"))||!1}catch(e){return console.error("OpenAI connection test failed:",e),!1}}static async generateWithClaude(e,t,a,r){if(!this.claudeClient)throw Error("Claude client not initialized");try{let n=this.buildFullPrompt(e,t,a,r),o=await this.claudeClient.messages.create({model:"claude-3-sonnet-20240229",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:n}]});if("text"!==o.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(o.content[0].text)}catch(e){throw console.error("Claude generation failed:",e),Error("Claude API error: ".concat(e instanceof Error?e.message:"Unknown error"))}}static async generateWithOpenAI(e,t,a,r){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{var n,o;let i=this.buildFullPrompt(e,t,a,r),s=null==(o=(await this.openaiClient.chat.completions.create({model:"gpt-4",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts."},{role:"user",content:i}]})).choices[0])||null==(n=o.message)?void 0:n.content;if(!s)throw Error("No content received from OpenAI");return this.parseAIResponse(s)}catch(e){throw console.error("OpenAI generation failed:",e),Error("OpenAI API error: ".concat(e instanceof Error?e.message:"Unknown error"))}}static buildFullPrompt(e,t,a,r){return"".concat(e,"\n\nDOCUMENT CONTENTS:\n\nEnterprise Need Document:\n").concat(t.enterpriseNeed,"\n\nSolution Description Document:\n").concat(t.solution,"\n\nRisk of No Investment Document:\n").concat(t.risk,"\n\nPlease generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).")}static parseAIResponse(e){let t=e.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i),a="",r="",n="";for(let e of t){let t=e.trim();t&&(t.toLowerCase().includes("enterprise need")?a=t:t.toLowerCase().includes("proposed solution")?r=t:t.toLowerCase().includes("risk of no investment")&&(n=t))}if(!a||!r||!n){let t=e.split(/\n\s*\n/);t.length>=3?(a=t[0]||"Enterprise Need artifact could not be parsed.",r=t[1]||"Solution artifact could not be parsed.",n=t[2]||"Risk artifact could not be parsed."):(a="# Enterprise Need Artifact\n\n".concat(e),r="# Proposed Solution Artifact\n\n".concat(e),n="# Risk of No Investment Artifact\n\n".concat(e))}return{enterpriseNeedArtifact:a.trim(),solutionArtifact:r.trim(),riskArtifact:n.trim()}}static async generateWithClaudeFiles(e,t,a,r){if(!this.claudeClient)throw Error("Claude client not initialized");try{let a=[],r=(e,t)=>{let r=e.fileType.toLowerCase();"pdf"===r?(a.push({type:"document",source:{type:"base64",media_type:"application/pdf",data:e.base64Data}}),a.push({type:"text",text:"The above document contains the ".concat(t," information.")})):["png","jpg","jpeg"].includes(r)?(a.push({type:"image",source:{type:"base64",media_type:"jpg"===r?"image/jpeg":"image/".concat(r),data:e.base64Data}}),a.push({type:"text",text:"The above image contains the ".concat(t," information.")})):(a.push({type:"document",source:{type:"base64",media_type:this.getMimeType(r),data:e.base64Data}}),a.push({type:"text",text:"The above document contains the ".concat(t," information.")}))};r(t.enterpriseNeedFile,"Enterprise Need"),r(t.solutionFile,"Solution Description"),r(t.riskFile,"Risk of No Investment"),a.push({type:"text",text:"".concat(e,"\n\nPlease analyze the three attached documents and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).\n\nThe first document contains the Enterprise Need information.\nThe second document contains the Solution Description information.\nThe third document contains the Risk of No Investment information.\n\nUse only the content from these attached documents and do not add any other information other than the items requested in this prompt.")});let n=await this.claudeClient.messages.create({model:"claude-3-5-sonnet-20241022",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:a}]});if("text"!==n.content[0].type)throw Error("Unexpected response format from Claude");return this.parseAIResponse(n.content[0].text)}catch(e){throw console.error("Claude file generation failed:",e),Error("Claude API error: ".concat(e instanceof Error?e.message:"Unknown error"))}}static async generateWithOpenAIFiles(e,t,a,r){if(!this.openaiClient)throw Error("OpenAI client not initialized");try{var n,o;let a=[];a.push({type:"text",text:"".concat(e,"\n\nI have three documents that I need you to analyze. Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).\n\nDocuments to analyze:")});let r=(e,t)=>{let r=e.fileType.toLowerCase();["png","jpg","jpeg"].includes(r)?(a.push({type:"text",text:"\n".concat(t," Document (").concat(e.fileName,"):")}),a.push({type:"image_url",image_url:{url:"data:image/".concat("jpg"===r?"jpeg":r,";base64,").concat(e.base64Data)}})):a.push({type:"text",text:"\n".concat(t," Document: ").concat(e.fileName," (").concat(r.toUpperCase()," file - please note that the content extraction may be limited for this file type)")})};r(t.enterpriseNeedFile,"Enterprise Need"),r(t.solutionFile,"Solution Description"),r(t.riskFile,"Risk of No Investment");let i=null==(o=(await this.openaiClient.chat.completions.create({model:"gpt-4-vision-preview",max_tokens:4e3,temperature:.7,messages:[{role:"system",content:"You are an expert business analyst creating professional Business Value Assessment artifacts. You can analyze both text and image content to create comprehensive business documents."},{role:"user",content:a}]})).choices[0])||null==(n=o.message)?void 0:n.content;if(!i)throw Error("No content received from OpenAI");return this.parseAIResponse(i)}catch(e){throw console.error("OpenAI file generation failed:",e),Error("OpenAI API error: ".concat(e instanceof Error?e.message:"Unknown error"))}}static async generateArtifactsWithFilesRetry(e,t,a,r,n){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:3,i=null;for(let s=1;s<=o;s++)try{if("claude"===e)return await this.generateWithClaudeFiles(t,a,r,n);return await this.generateWithOpenAIFiles(t,a,r,n)}catch(e){i=e instanceof Error?e:Error("Unknown error"),console.error("AI file generation attempt ".concat(s," failed:"),i),s<o&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,s)))}throw Error("AI file generation failed after ".concat(o," attempts: ").concat(null==i?void 0:i.message))}static getMimeType(e){return({pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",doc:"application/msword",txt:"text/plain",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg"})[e.toLowerCase()]||"application/octet-stream"}static async generateArtifactsWithRetry(e,t,a,r,n){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:3,i=null;for(let s=1;s<=o;s++)try{if("claude"===e)return await this.generateWithClaude(t,a,r,n);return await this.generateWithOpenAI(t,a,r,n)}catch(e){i=e instanceof Error?e:Error("Unknown error"),console.error("AI generation attempt ".concat(s," failed:"),i),s<o&&await new Promise(e=>setTimeout(e,1e3*Math.pow(2,s)))}throw Error("AI generation failed after ".concat(o," attempts: ").concat(null==i?void 0:i.message))}}p.claudeClient=null,p.openaiClient=null;var m=a(2109),h=a.n(m);let g="main",y="raviteja";class f{static encryptApiKey(e){return h().AES.encrypt(e,y).toString()}static decryptApiKey(e){return h().AES.decrypt(e,y).toString(h().enc.Utf8)}static async getAPIConfiguration(){try{var e,t,a;let r=await (0,l.x7)((0,l.H9)(c.db,"apiConfig",g));if(!r.exists())return null;let n=r.data();return{id:r.id,claudeApiKey:n.claudeApiKey?this.decryptApiKey(n.claudeApiKey):void 0,openaiApiKey:n.openaiApiKey?this.decryptApiKey(n.openaiApiKey):void 0,createdAt:(null==(e=n.createdAt)?void 0:e.toDate())||new Date,updatedAt:(null==(t=n.updatedAt)?void 0:t.toDate())||new Date,claudeStatus:n.claudeStatus||"untested",openaiStatus:n.openaiStatus||"untested",lastTestedAt:null==(a=n.lastTestedAt)?void 0:a.toDate(),claudeModelConfig:n.claudeModelConfig||{model:"claude-3-5-sonnet-20241022",maxTokens:4e3,temperature:.7},openaiModelConfig:n.openaiModelConfig||{model:"gpt-4-vision-preview",maxTokens:4e3,temperature:.7}}}catch(e){throw console.error("Error fetching API configuration:",e),Error("Failed to fetch API configuration")}}static async saveAPIConfiguration(e){try{let t=(0,l.H9)(c.db,"apiConfig",g),a=await (0,l.x7)(t),r={updatedAt:l.Dc.now()};e.claudeApiKey&&(r.claudeApiKey=this.encryptApiKey(e.claudeApiKey),r.claudeStatus="untested"),e.openaiApiKey&&(r.openaiApiKey=this.encryptApiKey(e.openaiApiKey),r.openaiStatus="untested"),e.claudeModelConfig&&(r.claudeModelConfig=e.claudeModelConfig),e.openaiModelConfig&&(r.openaiModelConfig=e.openaiModelConfig),a.exists()?await (0,l.mZ)(t,r):await (0,l.BN)(t,{...r,createdAt:l.Dc.now()})}catch(e){throw console.error("Error saving API configuration:",e),Error("Failed to save API configuration")}}static async testAPIConnections(){try{let{getFunctions:e,httpsCallable:t}=await a.e(427).then(a.bind(a,1427)),r=e(),n=t(r,"testAPIConnectionsFunction");return(await n()).data}catch(e){return console.error("Error testing API connections:",e),{claudeStatus:"error",openaiStatus:"error",claudeError:e instanceof Error?e.message:"Failed to test connections",openaiError:e instanceof Error?e.message:"Failed to test connections"}}}static async getDecryptedAPIKeys(){try{let e=await this.getAPIConfiguration();return{claudeApiKey:null==e?void 0:e.claudeApiKey,openaiApiKey:null==e?void 0:e.openaiApiKey}}catch(e){throw console.error("Error getting decrypted API keys:",e),Error("Failed to get API keys")}}static async areAPIKeysConfigured(){try{let e=await this.getAPIConfiguration(),t=!!(null==e?void 0:e.claudeApiKey),a=!!(null==e?void 0:e.openaiApiKey);return{claudeConfigured:t,openaiConfigured:a,anyConfigured:t||a}}catch(e){return console.error("Error checking API key configuration:",e),{claudeConfigured:!1,openaiConfigured:!1,anyConfigured:!1}}}static async initializeAIServices(){try{let e=await this.getDecryptedAPIKeys();e.claudeApiKey&&p.initializeClaude(e.claudeApiKey),e.openaiApiKey&&p.initializeOpenAI(e.openaiApiKey)}catch(e){throw console.error("Error initializing AI services:",e),Error("Failed to initialize AI services")}}static validateAPIKey(e,t){if(!t||0===t.trim().length)return{isValid:!1,error:"API key cannot be empty"};if("claude"===e){if(!t.startsWith("sk-ant-"))return{isValid:!1,error:'Claude API key should start with "sk-ant-"'}}else if("openai"===e&&!t.startsWith("sk-"))return{isValid:!1,error:'OpenAI API key should start with "sk-"'};return t.length<20?{isValid:!1,error:"API key appears to be too short"}:{isValid:!0}}}function x(){let{user:e,loading:t,isAdmin:a}=(0,o.A)(),l=(0,i.useRouter)(),[c,d]=(0,n.useState)(null),[u,p]=(0,n.useState)(!0),[m,h]=(0,n.useState)(!1),[g,y]=(0,n.useState)(!1),[x,b]=(0,n.useState)(!1),[A,w]=(0,n.useState)({claudeApiKey:"",openaiApiKey:"",claudeModelConfig:{model:"claude-3-5-sonnet-20241022",maxTokens:4e3,temperature:.7},openaiModelConfig:{model:"gpt-4-vision-preview",maxTokens:4e3,temperature:.7}}),[v,k]=(0,n.useState)({});(0,n.useEffect)(()=>{if(!t&&!a)return void l.push("/");a&&C()},[t,a,l]);let C=async()=>{try{p(!0);let e=await f.getAPIConfiguration();d(e),e&&(w({claudeApiKey:e.claudeApiKey||"",openaiApiKey:e.openaiApiKey||"",claudeModelConfig:e.claudeModelConfig||{model:"claude-3-5-sonnet-20241022",maxTokens:4e3,temperature:.7},openaiModelConfig:e.openaiModelConfig||{model:"gpt-4-vision-preview",maxTokens:4e3,temperature:.7}}),k({claudeStatus:e.claudeStatus,openaiStatus:e.openaiStatus}))}catch(e){console.error("Error fetching API config:",e),alert("Error loading API configuration")}finally{p(!1)}},I=async()=>{try{if(h(!0),A.claudeApiKey){let e=f.validateAPIKey("claude",A.claudeApiKey);if(!e.isValid)return void alert("Claude API Key Error: ".concat(e.error))}if(A.openaiApiKey){let e=f.validateAPIKey("openai",A.openaiApiKey);if(!e.isValid)return void alert("OpenAI API Key Error: ".concat(e.error))}await f.saveAPIConfiguration({claudeApiKey:A.claudeApiKey||void 0,openaiApiKey:A.openaiApiKey||void 0,claudeModelConfig:A.claudeModelConfig,openaiModelConfig:A.openaiModelConfig}),alert("API configuration saved successfully!"),await C()}catch(e){console.error("Error saving API config:",e),alert("Error saving API configuration")}finally{h(!1)}},j=async()=>{try{y(!0);let e=await f.testAPIConnections();k(e);let t=[];"connected"===e.claudeStatus?t.push("✅ Claude API: Connected successfully"):"error"===e.claudeStatus?t.push("❌ Claude API: ".concat(e.claudeError||"Connection failed")):t.push("⚪ Claude API: Not configured"),"connected"===e.openaiStatus?t.push("✅ OpenAI API: Connected successfully"):"error"===e.openaiStatus?t.push("❌ OpenAI API: ".concat(e.openaiError||"Connection failed")):t.push("⚪ OpenAI API: Not configured"),alert("API Connection Test Results:\n\n"+t.join("\n"))}catch(e){console.error("Error testing connections:",e),alert("Error testing API connections. Please try again.")}finally{y(!1)}},N=e=>{switch(e){case"connected":return(0,r.jsx)("span",{className:"text-green-500",children:"✓ Connected"});case"error":return(0,r.jsx)("span",{className:"text-red-500",children:"✗ Error"});default:return(0,r.jsx)("span",{className:"text-gray-500",children:"○ Untested"})}};return t||u?(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}):a?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(s.A,{}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Settings"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Configure API keys and system settings for VALTICS AI."})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"AI API Configuration"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Configure API keys for Claude AI and OpenAI to enable AI artifact generation."})]}),(0,r.jsxs)("button",{onClick:()=>b(!x),className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:[x?"Hide":"Show"," API Keys"]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Claude AI API Key"}),N(v.claudeStatus)]}),(0,r.jsx)("input",{type:x?"text":"password",value:A.claudeApiKey,onChange:e=>w(t=>({...t,claudeApiKey:e.target.value})),placeholder:"sk-ant-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,r.jsx)("a",{href:"https://console.anthropic.com/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"Anthropic Console"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"OpenAI API Key"}),N(v.openaiStatus)]}),(0,r.jsx)("input",{type:x?"text":"password",value:A.openaiApiKey,onChange:e=>w(t=>({...t,openaiApiKey:e.target.value})),placeholder:"sk-...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Get your API key from ",(0,r.jsx)("a",{href:"https://platform.openai.com/api-keys",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"OpenAI Platform"})]})]}),(0,r.jsxs)("div",{className:"pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Model Configuration"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"Configure the AI models and parameters used for artifact generation."}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Claude AI Configuration"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Model"}),(0,r.jsxs)("select",{value:A.claudeModelConfig.model,onChange:e=>w(t=>({...t,claudeModelConfig:{...t.claudeModelConfig,model:e.target.value}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,r.jsx)("option",{value:"claude-3-5-sonnet-20241022",children:"Claude 3.5 Sonnet (Latest)"}),(0,r.jsx)("option",{value:"claude-3-sonnet-20240229",children:"Claude 3 Sonnet"}),(0,r.jsx)("option",{value:"claude-3-haiku-20240307",children:"Claude 3 Haiku"}),(0,r.jsx)("option",{value:"claude-3-opus-20240229",children:"Claude 3 Opus"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Max Tokens"}),(0,r.jsx)("input",{type:"number",min:"100",max:"8000",value:A.claudeModelConfig.maxTokens,onChange:e=>w(t=>({...t,claudeModelConfig:{...t.claudeModelConfig,maxTokens:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Temperature"}),(0,r.jsx)("input",{type:"number",min:"0",max:"1",step:"0.1",value:A.claudeModelConfig.temperature,onChange:e=>w(t=>({...t,claudeModelConfig:{...t.claudeModelConfig,temperature:parseFloat(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"OpenAI Configuration"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Model"}),(0,r.jsxs)("select",{value:A.openaiModelConfig.model,onChange:e=>w(t=>({...t,openaiModelConfig:{...t.openaiModelConfig,model:e.target.value}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,r.jsx)("option",{value:"gpt-4-vision-preview",children:"GPT-4 Vision Preview"}),(0,r.jsx)("option",{value:"gpt-4-turbo-preview",children:"GPT-4 Turbo Preview"}),(0,r.jsx)("option",{value:"gpt-4",children:"GPT-4"}),(0,r.jsx)("option",{value:"gpt-4-32k",children:"GPT-4 32K"}),(0,r.jsx)("option",{value:"gpt-3.5-turbo",children:"GPT-3.5 Turbo"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Max Tokens"}),(0,r.jsx)("input",{type:"number",min:"100",max:"8000",value:A.openaiModelConfig.maxTokens,onChange:e=>w(t=>({...t,openaiModelConfig:{...t.openaiModelConfig,maxTokens:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Temperature"}),(0,r.jsx)("input",{type:"number",min:"0",max:"1",step:"0.1",value:A.openaiModelConfig.temperature,onChange:e=>w(t=>({...t,openaiModelConfig:{...t.openaiModelConfig,temperature:parseFloat(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("button",{onClick:I,disabled:m,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[m&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:m?"Saving...":"Save Configuration"})]}),(0,r.jsxs)("button",{onClick:j,disabled:g||!A.claudeApiKey&&!A.openaiApiKey,className:"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[g&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{children:g?"Testing...":"Test Connections"})]})]}),(null==c?void 0:c.lastTestedAt)&&(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Last tested: ",c.lastTestedAt.toLocaleString()]})]})]})}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Security Notice"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:(0,r.jsx)("p",{children:"API keys are encrypted before storage and are only accessible to admin users. Keep your API keys secure and rotate them regularly. Never share your API keys with unauthorized users."})})]})]})})]})})]}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,973,573,441,684,358],()=>t(288)),_N_E=e.O()}]);