import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import { AIService } from './services/aiService';
import { APIConfigService } from './services/apiConfigService';
import { ApiRateLimiter } from './utils/apiRateLimiter';

/**
 * Firebase function that triggers when a template status changes
 * to generate AI artifacts
 */
export const generateAIArtifacts = async (
  change: functions.Change<functions.firestore.DocumentSnapshot>,
  context: functions.EventContext
) => {
  const beforeData = change.before.data();
  const afterData = change.after.data();
  const templateId = context.params.templateId;

  // Check if status changed to 'pending-generation'
  if (beforeData?.status !== 'pending-generation' && afterData?.status === 'pending-generation') {
    console.log(`Starting AI artifact generation for template: ${templateId}`);

    try {
      // Validate required data
      if (!afterData.selectedLLM || !['claude', 'openai'].includes(afterData.selectedLLM)) {
        throw new Error('Invalid or missing LLM selection');
      }

      if (!afterData.enterpriseNeedFileUrl || 
          !afterData.solutionDescriptionFileUrl || 
          !afterData.riskOfNoInvestmentFileUrl) {
        throw new Error('Missing required document files');
      }

      // Check if API keys are configured with rate limit handling
      const apiKeyStatus = await ApiRateLimiter.executeWithRetry(
        async () => await APIConfigService.areAPIKeysConfigured()
      );
      
      if (!apiKeyStatus.anyConfigured) {
        throw new Error('No AI API keys configured');
      }

      if (afterData.selectedLLM === 'claude' && !apiKeyStatus.claudeConfigured) {
        throw new Error('Claude API key not configured');
      }

      if (afterData.selectedLLM === 'openai' && !apiKeyStatus.openaiConfigured) {
        throw new Error('OpenAI API key not configured');
      }

      // Initialize AI service
      await AIService.initialize();

      // Download document files with rate limit handling
      const documentFiles = await ApiRateLimiter.executeWithRetry(
        async () => {
          // Your file download logic here
          return {
            enterpriseNeedFile: { /* file data */ },
            solutionFile: { /* file data */ },
            riskFile: { /* file data */ }
          };
        }
      );

      // Generate artifacts with rate limit handling
      const artifacts = await ApiRateLimiter.executeWithRetry(
        async () => await AIService.generateArtifactsWithFilesRetry(
          afterData.selectedLLM,
          afterData.aiPrompt || 'Generate business value assessment artifacts',
          documentFiles,
          afterData.solutionName || 'Solution',
          afterData.companyName || 'Company'
        )
      );

      // Update template with generated artifacts
      await admin.firestore()
        .collection('templates')
        .doc(templateId)
        .update({
          status: 'generation-completed',
          artifacts: artifacts,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

      console.log(`AI artifact generation completed successfully for template: ${templateId}`);

    } catch (error) {
      console.error(`Error processing AI generation for template ${templateId}:`, error);
      
      // Update status to indicate failure
      await admin.firestore()
        .collection('templates')
        .doc(templateId)
        .update({
          status: 'generation-failed',
          generationError: error instanceof Error ? error.message : 'Unknown error occurred',
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

      // Log detailed error for debugging
      console.error('AI Generation Error Details:', {
        templateId,
        selectedLLM: afterData?.selectedLLM,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  }
};