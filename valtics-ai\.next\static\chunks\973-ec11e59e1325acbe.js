(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[973],{92:function(e,t,s){var r,n,a,i,o,l,c,d,h,f,u,x,p,b,g,m,y,_;r=s(3652),s(5371),e.exports=void(r.lib.Cipher||(a=(n=r.lib).Base,i=n.WordArray,o=n.BufferedBlockAlgorithm,(l=r.enc).Utf8,c=l.Base64,d=r.algo.EvpKDF,h=n.Cipher=o.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,s){this.cfg=this.cfg.extend(s),this._xformMode=e,this._key=t,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?_:m}return function(t){return{encrypt:function(s,r,n){return e(r).encrypt(t,s,r,n)},decrypt:function(s,r,n){return e(r).decrypt(t,s,r,n)}}}}()}),n.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),f=r.mode={},u=n.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),x=f.CBC=function(){var e=u.extend();function t(e,t,s){var r,n=this._iv;n?(r=n,this._iv=void 0):r=this._prevBlock;for(var a=0;a<s;a++)e[t+a]^=r[a]}return e.Encryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize;t.call(this,e,s,n),r.encryptBlock(e,s),this._prevBlock=e.slice(s,s+n)}}),e.Decryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize,a=e.slice(s,s+n);r.decryptBlock(e,s),t.call(this,e,s,n),this._prevBlock=a}}),e}(),p=(r.pad={}).Pkcs7={pad:function(e,t){for(var s=4*t,r=s-e.sigBytes%s,n=r<<24|r<<16|r<<8|r,a=[],o=0;o<r;o+=4)a.push(n);var l=i.create(a,r);e.concat(l)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},n.BlockCipher=h.extend({cfg:h.cfg.extend({mode:x,padding:p}),reset:function(){h.reset.call(this);var e,t=this.cfg,s=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,s&&s.words):(this._mode=e.call(r,this,s&&s.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),b=n.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),g=(r.format={}).OpenSSL={stringify:function(e){var t,s=e.ciphertext,r=e.salt;return(r?i.create([0x53616c74,0x65645f5f]).concat(r).concat(s):s).toString(c)},parse:function(e){var t,s=c.parse(e),r=s.words;return 0x53616c74==r[0]&&0x65645f5f==r[1]&&(t=i.create(r.slice(2,4)),r.splice(0,4),s.sigBytes-=16),b.create({ciphertext:s,salt:t})}},m=n.SerializableCipher=a.extend({cfg:a.extend({format:g}),encrypt:function(e,t,s,r){r=this.cfg.extend(r);var n=e.createEncryptor(s,r),a=n.finalize(t),i=n.cfg;return b.create({ciphertext:a,key:s,iv:i.iv,algorithm:e,mode:i.mode,padding:i.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,s,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(s,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(r.kdf={}).OpenSSL={execute:function(e,t,s,r,n){if(r||(r=i.random(8)),n)var a=d.create({keySize:t+s,hasher:n}).compute(e,r);else var a=d.create({keySize:t+s}).compute(e,r);var o=i.create(a.words.slice(t),4*s);return a.sigBytes=4*t,b.create({key:a,iv:o,salt:r})}},_=n.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:y}),encrypt:function(e,t,s,r){var n=(r=this.cfg.extend(r)).kdf.execute(s,e.keySize,e.ivSize,r.salt,r.hasher);r.iv=n.iv;var a=m.encrypt.call(this,e,t,n.key,r);return a.mixIn(n),a},decrypt:function(e,t,s,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var n=r.kdf.execute(s,e.keySize,e.ivSize,t.salt,r.hasher);return r.iv=n.iv,m.decrypt.call(this,e,t,n.key,r)}})))},312:function(e,t,s){var r;r=s(3652),s(92),r.pad.AnsiX923={pad:function(e,t){var s=e.sigBytes,r=4*t,n=r-s%r,a=s+n-1;e.clamp(),e.words[a>>>2]|=n<<24-a%4*8,e.sigBytes+=n},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.exports=r.pad.Ansix923},402:function(e,t,s){var r,n,a,i,o,l,c,d,h,f,u,x,p,b,g;r=s(3652),s(5883),s(8261),s(5371),s(92),n=r.lib.BlockCipher,a=r.algo,i=[],o=[],l=[],c=[],d=[],h=[],f=[],u=[],x=[],p=[],function(){for(var e=[],t=0;t<256;t++)t<128?e[t]=t<<1:e[t]=t<<1^283;for(var s=0,r=0,t=0;t<256;t++){var n=r^r<<1^r<<2^r<<3^r<<4;n=n>>>8^255&n^99,i[s]=n,o[n]=s;var a=e[s],b=e[a],g=e[b],m=257*e[n]^0x1010100*n;l[s]=m<<24|m>>>8,c[s]=m<<16|m>>>16,d[s]=m<<8|m>>>24,h[s]=m;var m=0x1010101*g^65537*b^257*a^0x1010100*s;f[n]=m<<24|m>>>8,u[n]=m<<16|m>>>16,x[n]=m<<8|m>>>24,p[n]=m,s?(s=a^e[e[e[g^a]]],r^=e[e[r]]):s=r=1}}(),b=[0,1,2,4,8,16,32,64,128,27,54],g=a.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e,t=this._keyPriorReset=this._key,s=t.words,r=t.sigBytes/4,n=((this._nRounds=r+6)+1)*4,a=this._keySchedule=[],o=0;o<n;o++)o<r?a[o]=s[o]:(e=a[o-1],o%r?r>6&&o%r==4&&(e=i[e>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e]):e=(i[(e=e<<8|e>>>24)>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e])^b[o/r|0]<<24,a[o]=a[o-r]^e);for(var l=this._invKeySchedule=[],c=0;c<n;c++){var o=n-c;if(c%4)var e=a[o];else var e=a[o-4];c<4||o<=4?l[c]=e:l[c]=f[i[e>>>24]]^u[i[e>>>16&255]]^x[i[e>>>8&255]]^p[i[255&e]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,l,c,d,h,i)},decryptBlock:function(e,t){var s=e[t+1];e[t+1]=e[t+3],e[t+3]=s,this._doCryptBlock(e,t,this._invKeySchedule,f,u,x,p,o);var s=e[t+1];e[t+1]=e[t+3],e[t+3]=s},_doCryptBlock:function(e,t,s,r,n,a,i,o){for(var l=this._nRounds,c=e[t]^s[0],d=e[t+1]^s[1],h=e[t+2]^s[2],f=e[t+3]^s[3],u=4,x=1;x<l;x++){var p=r[c>>>24]^n[d>>>16&255]^a[h>>>8&255]^i[255&f]^s[u++],b=r[d>>>24]^n[h>>>16&255]^a[f>>>8&255]^i[255&c]^s[u++],g=r[h>>>24]^n[f>>>16&255]^a[c>>>8&255]^i[255&d]^s[u++],m=r[f>>>24]^n[c>>>16&255]^a[d>>>8&255]^i[255&h]^s[u++];c=p,d=b,h=g,f=m}var p=(o[c>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&f])^s[u++],b=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[f>>>8&255]<<8|o[255&c])^s[u++],g=(o[h>>>24]<<24|o[f>>>16&255]<<16|o[c>>>8&255]<<8|o[255&d])^s[u++],m=(o[f>>>24]<<24|o[c>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^s[u++];e[t]=p,e[t+1]=b,e[t+2]=g,e[t+3]=m},keySize:8}),r.AES=n._createHelper(g),e.exports=r.AES},511:function(e,t,s){var r,n;r=s(3652),s(92),r.mode.ECB=((n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),n.Decryptor=n.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),n),e.exports=r.mode.ECB},933:function(e,t,s){var r;r=s(3652),s(5883),s(8261),s(5371),s(92),function(){var e=r.lib,t=e.WordArray,s=e.BlockCipher,n=r.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],i=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],o=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],l=[{0:8421888,0x10000000:32768,0x20000000:8421378,0x30000000:2,0x40000000:512,0x50000000:8421890,0x60000000:8389122,0x70000000:8388608,0x80000000:514,0x90000000:8389120,0xa0000000:33280,0xb0000000:8421376,0xc0000000:32770,0xd0000000:8388610,0xe0000000:0,0xf0000000:33282,0x8000000:0,0x18000000:8421890,0x28000000:33282,0x38000000:32768,0x48000000:8421888,0x58000000:512,0x68000000:8421378,0x78000000:2,0x88000000:8389120,0x98000000:33280,0xa8000000:8421376,0xb8000000:8389122,0xc8000000:8388610,0xd8000000:32770,0xe8000000:514,0xf8000000:8388608,1:32768,0x10000001:2,0x20000001:8421888,0x30000001:8388608,0x40000001:8421378,0x50000001:33280,0x60000001:512,0x70000001:8389122,0x80000001:8421890,0x90000001:8421376,0xa0000001:8388610,0xb0000001:33282,0xc0000001:514,0xd0000001:8389120,0xe0000001:32770,0xf0000001:0,0x8000001:8421890,0x18000001:8421376,0x28000001:8388608,0x38000001:512,0x48000001:32768,0x58000001:8388610,0x68000001:2,0x78000001:33282,0x88000001:32770,0x98000001:8389122,0xa8000001:514,0xb8000001:8421888,0xc8000001:8389120,0xd8000001:0,0xe8000001:33280,0xf8000001:8421378},{0:0x40084010,0x1000000:16384,0x2000000:524288,0x3000000:0x40080010,0x4000000:0x40000010,0x5000000:0x40084000,0x6000000:0x40004000,0x7000000:16,0x8000000:540672,0x9000000:0x40004010,0xa000000:0x40000000,0xb000000:540688,0xc000000:524304,0xd000000:0,0xe000000:16400,0xf000000:0x40080000,8388608:0x40004000,0x1800000:540688,0x2800000:16,0x3800000:0x40004010,0x4800000:0x40084010,0x5800000:0x40000000,0x6800000:524288,0x7800000:0x40080010,0x8800000:524304,0x9800000:0,0xa800000:16384,0xb800000:0x40080000,0xc800000:0x40000010,0xd800000:540672,0xe800000:0x40084000,0xf800000:16400,0x10000000:0,0x11000000:0x40080010,0x12000000:0x40004010,0x13000000:0x40084000,0x14000000:0x40080000,0x15000000:16,0x16000000:540688,0x17000000:16384,0x18000000:16400,0x19000000:524288,0x1a000000:524304,0x1b000000:0x40000010,0x1c000000:540672,0x1d000000:0x40004000,0x1e000000:0x40000000,0x1f000000:0x40084010,0x10800000:540688,0x11800000:524288,0x12800000:0x40080000,0x13800000:16384,0x14800000:0x40004000,0x15800000:0x40084010,0x16800000:16,0x17800000:0x40000000,0x18800000:0x40084000,0x19800000:0x40000010,0x1a800000:0x40004010,0x1b800000:524304,0x1c800000:0,0x1d800000:16400,0x1e800000:0x40080010,0x1f800000:540672},{0:260,1048576:0,2097152:0x4000100,3145728:65796,4194304:65540,5242880:0x4000004,6291456:0x4010104,7340032:0x4010000,8388608:0x4000000,9437184:0x4010100,0xa00000:65792,0xb00000:0x4010004,0xc00000:0x4000104,0xd00000:65536,0xe00000:4,0xf00000:256,524288:0x4010100,1572864:0x4010004,2621440:0,3670016:0x4000100,4718592:0x4000004,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,0xa80000:0x4010000,0xb80000:65796,0xc80000:65792,0xd80000:0x4000104,0xe80000:0x4010104,0xf80000:0x4000000,0x1000000:0x4010100,0x1100000:65540,0x1200000:65536,0x1300000:0x4000100,0x1400000:256,0x1500000:0x4010104,0x1600000:0x4000004,0x1700000:0,0x1800000:0x4000104,0x1900000:0x4000000,0x1a00000:4,0x1b00000:65792,0x1c00000:0x4010000,0x1d00000:260,0x1e00000:65796,0x1f00000:0x4010004,0x1080000:0x4000000,0x1180000:260,0x1280000:0x4010100,0x1380000:0,0x1480000:65540,0x1580000:0x4000100,0x1680000:256,0x1780000:0x4010004,0x1880000:65536,0x1980000:0x4010104,0x1a80000:65796,0x1b80000:0x4000004,0x1c80000:0x4000104,0x1d80000:0x4010000,0x1e80000:4,0x1f80000:65792},{0:0x80401000,65536:0x80001040,131072:4198464,196608:0x80400000,262144:0,327680:4198400,393216:0x80000040,458752:4194368,524288:0x80000000,589824:4194304,655360:64,720896:0x80001000,786432:0x80400040,851968:4160,917504:4096,983040:0x80401040,32768:0x80001040,98304:64,163840:0x80400040,229376:0x80001000,294912:4198400,360448:0x80401040,425984:0,491520:0x80400000,557056:4096,622592:0x80401000,688128:4194304,753664:4160,819200:0x80000000,884736:4194368,950272:4198464,1015808:0x80000040,1048576:4194368,1114112:4198400,1179648:0x80000040,1245184:0,1310720:4160,1376256:0x80400040,1441792:0x80401000,1507328:0x80001040,1572864:0x80401040,1638400:0x80000000,1703936:0x80400000,1769472:4198464,1835008:0x80001000,1900544:4194304,1966080:64,2031616:4096,1081344:0x80400000,1146880:0x80401040,1212416:0,1277952:4198400,1343488:4194368,1409024:0x80000000,1474560:0x80001040,1540096:64,1605632:0x80000040,1671168:4096,1736704:0x80001000,1802240:0x80400040,1867776:4160,1933312:0x80401000,1998848:4194304,2064384:4198464},{0:128,4096:0x1040000,8192:262144,12288:0x20000000,16384:0x20040080,20480:0x1000080,24576:0x21000080,28672:262272,32768:0x1000000,36864:0x20040000,40960:0x20000080,45056:0x21040080,49152:0x21040000,53248:0,57344:0x1040080,61440:0x21000000,2048:0x1040080,6144:0x21000080,10240:128,14336:0x1040000,18432:262144,22528:0x20040080,26624:0x21040000,30720:0x20000000,34816:0x20040000,38912:0,43008:0x21040080,47104:0x1000080,51200:0x20000080,55296:0x21000000,59392:0x1000000,63488:262272,65536:262144,69632:128,73728:0x20000000,77824:0x21000080,81920:0x1000080,86016:0x21040000,90112:0x20040080,94208:0x1000000,98304:0x21040080,102400:0x21000000,106496:0x1040000,110592:0x20040000,114688:262272,118784:0x20000080,122880:0,126976:0x1040080,67584:0x21000080,71680:0x1000000,75776:0x1040000,79872:0x20040080,83968:0x20000000,88064:0x1040080,92160:128,96256:0x21040000,100352:262272,104448:0x21040080,108544:0,112640:0x21000000,116736:0x1000080,120832:262144,124928:0x20040000,129024:0x20000080},{0:0x10000008,256:8192,512:0x10200000,768:0x10202008,1024:0x10002000,1280:2097152,1536:2097160,1792:0x10000000,2048:0,2304:0x10002008,2560:2105344,2816:8,3072:0x10200008,3328:2105352,3584:8200,3840:0x10202000,128:0x10200000,384:0x10202008,640:8,896:2097152,1152:2105352,1408:0x10000008,1664:0x10002000,1920:8200,2176:2097160,2432:8192,2688:0x10002008,2944:0x10200008,3200:0,3456:0x10202000,3712:2105344,3968:0x10000000,4096:0x10002000,4352:0x10200008,4608:0x10202008,4864:8200,5120:2097152,5376:0x10000000,5632:0x10000008,5888:2105344,6144:2105352,6400:0,6656:8,6912:0x10200000,7168:8192,7424:0x10002008,7680:0x10202000,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:0x10000008,5248:0x10002000,5504:8200,5760:0x10202008,6016:0x10200000,6272:0x10202000,6528:0x10200008,6784:8192,7040:2105352,7296:2097160,7552:0,7808:0x10000000,8064:0x10002008},{0:1048576,16:0x2000401,32:1024,48:1049601,64:0x2100401,80:0,96:1,112:0x2100001,128:0x2000400,144:1048577,160:0x2000001,176:0x2100400,192:0x2100000,208:1025,224:1049600,240:0x2000000,8:0x2100001,24:0,40:0x2000401,56:0x2100400,72:1048576,88:0x2000001,104:0x2000000,120:1025,136:1049601,152:0x2000400,168:0x2100000,184:1048577,200:1024,216:0x2100401,232:1,248:1049600,256:0x2000000,272:1048576,288:0x2000401,304:0x2100001,320:1048577,336:0x2000400,352:0x2100400,368:1049601,384:1025,400:0x2100401,416:1049600,432:1,448:0,464:0x2100000,480:0x2000001,496:1024,264:1049600,280:0x2000401,296:0x2100001,312:1,328:0x2000000,344:1048576,360:1025,376:0x2100400,392:0x2000001,408:0x2100000,424:0,440:0x2100401,456:1049601,472:1024,488:0x2000400,504:1048577},{0:0x8000820,1:131072,2:0x8000000,3:32,4:131104,5:0x8020820,6:0x8020800,7:2048,8:0x8020000,9:0x8000800,10:133120,11:0x8020020,12:2080,13:0,14:0x8000020,15:133152,0x80000000:2048,0x80000001:0x8020820,0x80000002:0x8000820,0x80000003:0x8000000,0x80000004:0x8020000,0x80000005:133120,0x80000006:133152,0x80000007:32,0x80000008:0x8000020,0x80000009:2080,0x8000000a:131104,0x8000000b:0x8020800,0x8000000c:0,0x8000000d:0x8020020,0x8000000e:0x8000800,0x8000000f:131072,16:133152,17:0x8020800,18:32,19:2048,20:0x8000800,21:0x8000020,22:0x8020020,23:131072,24:0,25:131104,26:0x8020000,27:0x8000820,28:0x8020820,29:133120,30:2080,31:0x8000000,0x80000010:131072,0x80000011:2048,0x80000012:0x8020020,0x80000013:133152,0x80000014:32,0x80000015:0x8020000,0x80000016:0x8000000,0x80000017:0x8000820,0x80000018:0x8020820,0x80000019:0x8000020,0x8000001a:0x8000800,0x8000001b:0,0x8000001c:133120,0x8000001d:2080,0x8000001e:131104,0x8000001f:0x8020800}],c=[0xf8000001,0x1f800000,0x1f80000,2064384,129024,8064,504,0x8000001f],d=n.DES=s.extend({_doReset:function(){for(var e=this._key.words,t=[],s=0;s<56;s++){var r=a[s]-1;t[s]=e[r>>>5]>>>31-r%32&1}for(var n=this._subKeys=[],l=0;l<16;l++){for(var c=n[l]=[],d=o[l],s=0;s<24;s++)c[s/6|0]|=t[(i[s]-1+d)%28]<<31-s%6,c[4+(s/6|0)]|=t[28+(i[s+24]-1+d)%28]<<31-s%6;c[0]=c[0]<<1|c[0]>>>31;for(var s=1;s<7;s++)c[s]=c[s]>>>(s-1)*4+3;c[7]=c[7]<<5|c[7]>>>27}for(var h=this._invSubKeys=[],s=0;s<16;s++)h[s]=n[15-s]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,s){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,0xf0f0f0f),h.call(this,16,65535),f.call(this,2,0x33333333),f.call(this,8,0xff00ff),h.call(this,1,0x55555555);for(var r=0;r<16;r++){for(var n=s[r],a=this._lBlock,i=this._rBlock,o=0,d=0;d<8;d++)o|=l[d][((i^n[d])&c[d])>>>0];this._lBlock=i,this._rBlock=a^o}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,h.call(this,1,0x55555555),f.call(this,8,0xff00ff),f.call(this,2,0x33333333),h.call(this,16,65535),h.call(this,4,0xf0f0f0f),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var s=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=s,this._lBlock^=s<<e}function f(e,t){var s=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=s,this._rBlock^=s<<e}r.DES=s._createHelper(d);var u=n.TripleDES=s.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var s=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),n=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=d.createEncryptor(t.create(s)),this._des2=d.createEncryptor(t.create(r)),this._des3=d.createEncryptor(t.create(n))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});r.TripleDES=s._createHelper(u)}(),e.exports=r.TripleDES},992:function(e,t,s){var r;r=s(3652),s(92),r.mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();function t(e,t,s,r){var n,a=this._iv;a?(n=a.slice(0),this._iv=void 0):n=this._prevBlock,r.encryptBlock(n,0);for(var i=0;i<s;i++)e[t+i]^=n[i]}return e.Encryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize;t.call(this,e,s,n,r),this._prevBlock=e.slice(s,s+n)}}),e.Decryptor=e.extend({processBlock:function(e,s){var r=this._cipher,n=r.blockSize,a=e.slice(s,s+n);t.call(this,e,s,n,r),this._prevBlock=a}}),e}(),e.exports=r.mode.CFB},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return l},getImageProps:function(){return o}});let r=s(8229),n=s(8883),a=s(3063),i=r._(s(1193));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let l=a.Image},1609:function(e,t,s){var r,n,a,i,o;a=(n=(r=s(3652)).lib).Base,i=n.WordArray,(o=r.x64={}).Word=a.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=a.extend({init:function(e,t){e=this.words=e||[],void 0!=t?this.sigBytes=t:this.sigBytes=8*e.length},toX32:function(){for(var e=this.words,t=e.length,s=[],r=0;r<t;r++){var n=e[r];s.push(n.high),s.push(n.low)}return i.create(s,this.sigBytes)},clone:function(){for(var e=a.clone.call(this),t=e.words=this.words.slice(0),s=t.length,r=0;r<s;r++)t[r]=t[r].clone();return e}}),e.exports=r},2109:function(e,t,s){var r;r=s(3652),s(1609),s(7793),s(8278),s(5883),s(3828),s(8261),s(9734),s(3656),s(2309),s(9349),s(4068),s(5e3),s(8793),s(8832),s(4922),s(5371),s(92),s(992),s(7458),s(4173),s(4628),s(511),s(312),s(6560),s(6819),s(9930),s(8765),s(7776),s(402),s(933),s(3776),s(9403),s(2113),s(4737),e.exports=r},2113:function(e,t,s){var r;r=s(3652),s(5883),s(8261),s(5371),s(92),function(){var e=r.lib.StreamCipher,t=r.algo,s=[],n=[],a=[],i=t.RabbitLegacy=e.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,s=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,0xffff0000&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,0xffff0000&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,0xffff0000&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,0xffff0000&e[3]|65535&e[0]];this._b=0;for(var n=0;n<4;n++)o.call(this);for(var n=0;n<8;n++)r[n]^=s[n+4&7];if(t){var a=t.words,i=a[0],l=a[1],c=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,d=(l<<8|l>>>24)&0xff00ff|(l<<24|l>>>8)&0xff00ff00,h=c>>>16|0xffff0000&d,f=d<<16|65535&c;r[0]^=c,r[1]^=h,r[2]^=d,r[3]^=f,r[4]^=c,r[5]^=h,r[6]^=d,r[7]^=f;for(var n=0;n<4;n++)o.call(this)}},_doProcessBlock:function(e,t){var r=this._X;o.call(this),s[0]=r[0]^r[5]>>>16^r[3]<<16,s[1]=r[2]^r[7]>>>16^r[5]<<16,s[2]=r[4]^r[1]>>>16^r[7]<<16,s[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)s[n]=(s[n]<<8|s[n]>>>24)&0xff00ff|(s[n]<<24|s[n]>>>8)&0xff00ff00,e[t+n]^=s[n]},blockSize:4,ivSize:2});function o(){for(var e=this._X,t=this._C,s=0;s<8;s++)n[s]=t[s];t[0]=t[0]+0x4d34d34d+this._b|0,t[1]=t[1]+0xd34d34d3+ +(t[0]>>>0<n[0]>>>0)|0,t[2]=t[2]+0x34d34d34+ +(t[1]>>>0<n[1]>>>0)|0,t[3]=t[3]+0x4d34d34d+ +(t[2]>>>0<n[2]>>>0)|0,t[4]=t[4]+0xd34d34d3+ +(t[3]>>>0<n[3]>>>0)|0,t[5]=t[5]+0x34d34d34+ +(t[4]>>>0<n[4]>>>0)|0,t[6]=t[6]+0x4d34d34d+ +(t[5]>>>0<n[5]>>>0)|0,t[7]=t[7]+0xd34d34d3+ +(t[6]>>>0<n[6]>>>0)|0,this._b=+(t[7]>>>0<n[7]>>>0);for(var s=0;s<8;s++){var r=e[s]+t[s],i=65535&r,o=r>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((0xffff0000&r)*r|0)+((65535&r)*r|0);a[s]=l^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}r.RabbitLegacy=e._createHelper(i)}(),e.exports=r.RabbitLegacy},2309:function(e,t,s){var r,n,a,i,o;r=s(3652),s(3656),n=r.lib.WordArray,i=(a=r.algo).SHA256,o=a.SHA224=i.extend({_doReset:function(){this._hash=new n.init([0xc1059ed8,0x367cd507,0x3070dd17,0xf70e5939,0xffc00b31,0x68581511,0x64f98fa7,0xbefa4fa4])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=4,e}}),r.SHA224=i._createHelper(o),r.HmacSHA224=i._createHmacHelper(o),e.exports=r.SHA224},3652:function(e,t,s){var r;e.exports=r||function(e,t){if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==s.g&&s.g.crypto&&(r=s.g.crypto),!r)try{r=s(477)}catch(e){}var r,n=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var s;return e.prototype=t,s=new e,e.prototype=null,s}}(),i={},o=i.lib={},l=o.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=o.WordArray=l.extend({init:function(e,s){e=this.words=e||[],t!=s?this.sigBytes=s:this.sigBytes=4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,s=e.words,r=this.sigBytes,n=e.sigBytes;if(this.clamp(),r%4)for(var a=0;a<n;a++){var i=s[a>>>2]>>>24-a%4*8&255;t[r+a>>>2]|=i<<24-(r+a)%4*8}else for(var o=0;o<n;o+=4)t[r+o>>>2]=s[o>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,s=this.sigBytes;t[s>>>2]&=0xffffffff<<32-s%4*8,t.length=e.ceil(s/4)},clone:function(){var e=l.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],s=0;s<e;s+=4)t.push(n());return new c.init(t,e)}}),d=i.enc={},h=d.Hex={stringify:function(e){for(var t=e.words,s=e.sigBytes,r=[],n=0;n<s;n++){var a=t[n>>>2]>>>24-n%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,s=[],r=0;r<t;r+=2)s[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new c.init(s,t/2)}},f=d.Latin1={stringify:function(e){for(var t=e.words,s=e.sigBytes,r=[],n=0;n<s;n++){var a=t[n>>>2]>>>24-n%4*8&255;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var t=e.length,s=[],r=0;r<t;r++)s[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new c.init(s,t)}},u=d.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},x=o.BufferedBlockAlgorithm=l.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var s,r=this._data,n=r.words,a=r.sigBytes,i=this.blockSize,o=a/(4*i),l=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*i,d=e.min(4*l,a);if(l){for(var h=0;h<l;h+=i)this._doProcessBlock(n,h);s=n.splice(0,l),r.sigBytes-=d}return new c.init(s,d)},clone:function(){var e=l.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=x.extend({cfg:l.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){x.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,s){return new e.init(s).finalize(t)}},_createHmacHelper:function(e){return function(t,s){return new p.HMAC.init(e,s).finalize(t)}}});var p=i.algo={};return i}(Math)},3656:function(e,t,s){var r;r=s(3652),function(e){var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.algo,i=[],o=[];function l(e){return(e-(0|e))*0x100000000|0}for(var c=2,d=0;d<64;)(function(t){for(var s=e.sqrt(t),r=2;r<=s;r++)if(!(t%r))return!1;return!0})(c)&&(d<8&&(i[d]=l(e.pow(c,.5))),o[d]=l(e.pow(c,1/3)),d++),c++;var h=[],f=a.SHA256=n.extend({_doReset:function(){this._hash=new s.init(i.slice(0))},_doProcessBlock:function(e,t){for(var s=this._hash.words,r=s[0],n=s[1],a=s[2],i=s[3],l=s[4],c=s[5],d=s[6],f=s[7],u=0;u<64;u++){if(u<16)h[u]=0|e[t+u];else{var x=h[u-15],p=(x<<25|x>>>7)^(x<<14|x>>>18)^x>>>3,b=h[u-2],g=(b<<15|b>>>17)^(b<<13|b>>>19)^b>>>10;h[u]=p+h[u-7]+g+h[u-16]}var m=l&c^~l&d,y=r&n^r&a^n&a,_=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),w=f+((l<<26|l>>>6)^(l<<21|l>>>11)^(l<<7|l>>>25))+m+o[u]+h[u],v=_+y;f=d,d=c,c=l,l=i+w|0,i=a,a=n,n=r,r=w+v|0}s[0]=s[0]+r|0,s[1]=s[1]+n|0,s[2]=s[2]+a|0,s[3]=s[3]+i|0,s[4]=s[4]+l|0,s[5]=s[5]+c|0,s[6]=s[6]+d|0,s[7]=s[7]+f|0},_doFinalize:function(){var t=this._data,s=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return s[n>>>5]|=128<<24-n%32,s[(n+64>>>9<<4)+14]=e.floor(r/0x100000000),s[(n+64>>>9<<4)+15]=r,t.sigBytes=4*s.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=n._createHelper(f),r.HmacSHA256=n._createHmacHelper(f)}(Math),e.exports=r.SHA256},3776:function(e,t,s){var r;r=s(3652),s(5883),s(8261),s(5371),s(92),function(){var e=r.lib.StreamCipher,t=r.algo,s=t.RC4=e.extend({_doReset:function(){for(var e=this._key,t=e.words,s=e.sigBytes,r=this._S=[],n=0;n<256;n++)r[n]=n;for(var n=0,a=0;n<256;n++){var i=n%s,o=t[i>>>2]>>>24-i%4*8&255;a=(a+r[n]+o)%256;var l=r[n];r[n]=r[a],r[a]=l}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=n.call(this)},keySize:8,ivSize:0});function n(){for(var e=this._S,t=this._i,s=this._j,r=0,n=0;n<4;n++){s=(s+e[t=(t+1)%256])%256;var a=e[t];e[t]=e[s],e[s]=a,r|=e[(e[t]+e[s])%256]<<24-8*n}return this._i=t,this._j=s,r}r.RC4=e._createHelper(s);var a=t.RC4Drop=s.extend({cfg:s.cfg.extend({drop:192}),_doReset:function(){s._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)n.call(this)}});r.RC4Drop=e._createHelper(a)}(),e.exports=r.RC4},3828:function(e,t,s){var r,n;n=(r=s(3652)).lib.WordArray,r.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var s=e.words,r=e.sigBytes,n=t?this._safe_map:this._map;e.clamp();for(var a=[],i=0;i<r;i+=3)for(var o=(s[i>>>2]>>>24-i%4*8&255)<<16|(s[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|s[i+2>>>2]>>>24-(i+2)%4*8&255,l=0;l<4&&i+.75*l<r;l++)a.push(n.charAt(o>>>6*(3-l)&63));var c=n.charAt(64);if(c)for(;a.length%4;)a.push(c);return a.join("")},parse:function(e,t){void 0===t&&(t=!0);var s=e.length,r=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<r.length;i++)a[r.charCodeAt(i)]=i}var o=r.charAt(64);if(o){var l=e.indexOf(o);-1!==l&&(s=l)}for(var c=e,d=s,h=a,f=[],u=0,x=0;x<d;x++)if(x%4){var p=h[c.charCodeAt(x-1)]<<x%4*2|h[c.charCodeAt(x)]>>>6-x%4*2;f[u>>>2]|=p<<24-u%4*8,u++}return n.create(f,u)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},e.exports=r.enc.Base64url},4068:function(e,t,s){var r,n,a,i,o,l,c;r=s(3652),s(1609),s(9349),a=(n=r.x64).Word,i=n.WordArray,l=(o=r.algo).SHA512,c=o.SHA384=l.extend({_doReset:function(){this._hash=new i.init([new a.init(0xcbbb9d5d,0xc1059ed8),new a.init(0x629a292a,0x367cd507),new a.init(0x9159015a,0x3070dd17),new a.init(0x152fecd8,0xf70e5939),new a.init(0x67332667,0xffc00b31),new a.init(0x8eb44a87,0x68581511),new a.init(0xdb0c2e0d,0x64f98fa7),new a.init(0x47b5481d,0xbefa4fa4)])},_doFinalize:function(){var e=l._doFinalize.call(this);return e.sigBytes-=16,e}}),r.SHA384=l._createHelper(c),r.HmacSHA384=l._createHmacHelper(c),e.exports=r.SHA384},4173:function(e,t,s){var r;r=s(3652),s(92),r.mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function t(e){if((e>>24&255)==255){var t=e>>16&255,s=e>>8&255,r=255&e;255===t?(t=0,255===s?(s=0,255===r?r=0:++r):++s):++t,e=0+(t<<16)+(s<<8)+r}else e+=0x1000000;return e}var s=e.Encryptor=e.extend({processBlock:function(e,s){var r,n=this._cipher,a=n.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0),0===((r=o)[0]=t(r[0]))&&(r[1]=t(r[1]));var l=o.slice(0);n.encryptBlock(l,0);for(var c=0;c<a;c++)e[s+c]^=l[c]}});return e.Decryptor=s,e}(),e.exports=r.mode.CTRGladman},4628:function(e,t,s){var r,n,a;r=s(3652),s(92),r.mode.OFB=(a=(n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){var s=this._cipher,r=s.blockSize,n=this._iv,a=this._keystream;n&&(a=this._keystream=n.slice(0),this._iv=void 0),s.encryptBlock(a,0);for(var i=0;i<r;i++)e[t+i]^=a[i]}}),n.Decryptor=a,n),e.exports=r.mode.OFB},4737:function(e,t,s){var r;r=s(3652),s(5883),s(8261),s(5371),s(92),function(){var e=r.lib.BlockCipher,t=r.algo;let s=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],n=[[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a],[0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7],[0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0],[0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6]];var a={pbox:[],sbox:[]};function i(e,t){let s=e.sbox[0][t>>24&255]+e.sbox[1][t>>16&255];return s^=e.sbox[2][t>>8&255],s+=e.sbox[3][255&t]}function o(e,t,s){let r,n=t,a=s;for(let t=0;t<16;++t)n^=e.pbox[t],a=i(e,n)^a,r=n,n=a,a=r;return r=n,n=a,a=r^e.pbox[16],{left:n^=e.pbox[17],right:a}}var l=t.Blowfish=e.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key;!function(e,t,r){for(let t=0;t<4;t++){e.sbox[t]=[];for(let s=0;s<256;s++)e.sbox[t][s]=n[t][s]}let a=0;for(let n=0;n<18;n++)e.pbox[n]=s[n]^t[a],++a>=r&&(a=0);let i=0,l=0,c=0;for(let t=0;t<18;t+=2)i=(c=o(e,i,l)).left,l=c.right,e.pbox[t]=i,e.pbox[t+1]=l;for(let t=0;t<4;t++)for(let s=0;s<256;s+=2)i=(c=o(e,i,l)).left,l=c.right,e.sbox[t][s]=i,e.sbox[t][s+1]=l}(a,e.words,e.sigBytes/4)}},encryptBlock:function(e,t){var s=o(a,e[t],e[t+1]);e[t]=s.left,e[t+1]=s.right},decryptBlock:function(e,t){var s=function(e,t,s){let r,n=t,a=s;for(let t=17;t>1;--t)n^=e.pbox[t],a=i(e,n)^a,r=n,n=a,a=r;return r=n,n=a,a=r^e.pbox[1],{left:n^=e.pbox[0],right:a}}(a,e[t],e[t+1]);e[t]=s.left,e[t+1]=s.right},blockSize:2,keySize:4,ivSize:2});r.Blowfish=e._createHelper(l)}(),e.exports=r.Blowfish},4922:function(e,t,s){var r,n,a,i,o,l,c,d;r=s(3652),s(3656),s(8832),a=(n=r.lib).Base,i=n.WordArray,l=(o=r.algo).SHA256,c=o.HMAC,d=o.PBKDF2=a.extend({cfg:a.extend({keySize:4,hasher:l,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var s=this.cfg,r=c.create(s.hasher,e),n=i.create(),a=i.create([1]),o=n.words,l=a.words,d=s.keySize,h=s.iterations;o.length<d;){var f=r.update(t).finalize(a);r.reset();for(var u=f.words,x=u.length,p=f,b=1;b<h;b++){p=r.finalize(p),r.reset();for(var g=p.words,m=0;m<x;m++)u[m]^=g[m]}n.concat(f),l[0]++}return n.sigBytes=4*d,n}}),r.PBKDF2=function(e,t,s){return d.create(s).compute(e,t)},e.exports=r.PBKDF2},5e3:function(e,t,s){var r;r=s(3652),s(1609),function(e){var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.x64.Word,i=r.algo,o=[],l=[],c=[];!function(){for(var e=1,t=0,s=0;s<24;s++){o[e+5*t]=(s+1)*(s+2)/2%64;var r=t%5,n=(2*e+3*t)%5;e=r,t=n}for(var e=0;e<5;e++)for(var t=0;t<5;t++)l[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,d=0;d<24;d++){for(var h=0,f=0,u=0;u<7;u++){if(1&i){var x=(1<<u)-1;x<32?f^=1<<x:h^=1<<x-32}128&i?i=i<<1^113:i<<=1}c[d]=a.create(h,f)}}();for(var d=[],h=0;h<25;h++)d[h]=a.create();var f=i.SHA3=n.extend({cfg:n.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var s=this._state,r=this.blockSize/2,n=0;n<r;n++){var a=e[t+2*n],i=e[t+2*n+1];a=(a<<8|a>>>24)&0xff00ff|(a<<24|a>>>8)&0xff00ff00,i=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00;var h=s[n];h.high^=i,h.low^=a}for(var f=0;f<24;f++){for(var u=0;u<5;u++){for(var x=0,p=0,b=0;b<5;b++){var h=s[u+5*b];x^=h.high,p^=h.low}var g=d[u];g.high=x,g.low=p}for(var u=0;u<5;u++)for(var m=d[(u+4)%5],y=d[(u+1)%5],_=y.high,w=y.low,x=m.high^(_<<1|w>>>31),p=m.low^(w<<1|_>>>31),b=0;b<5;b++){var h=s[u+5*b];h.high^=x,h.low^=p}for(var v=1;v<25;v++){var x,p,h=s[v],S=h.high,k=h.low,A=o[v];A<32?(x=S<<A|k>>>32-A,p=k<<A|S>>>32-A):(x=k<<A-32|S>>>64-A,p=S<<A-32|k>>>64-A);var R=d[l[v]];R.high=x,R.low=p}var $=d[0],B=s[0];$.high=B.high,$.low=B.low;for(var u=0;u<5;u++)for(var b=0;b<5;b++){var v=u+5*b,h=s[v],O=d[v],I=d[(u+1)%5+5*b],E=d[(u+2)%5+5*b];h.high=O.high^~I.high&E.high,h.low=O.low^~I.low&E.low}var h=s[0],P=c[f];h.high^=P.high,h.low^=P.low}},_doFinalize:function(){var t=this._data,r=t.words;this._nDataBytes;var n=8*t.sigBytes,a=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((n+1)/a)*a>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var i=this._state,o=this.cfg.outputLength/8,l=o/8,c=[],d=0;d<l;d++){var h=i[d],f=h.high,u=h.low;f=(f<<8|f>>>24)&0xff00ff|(f<<24|f>>>8)&0xff00ff00,u=(u<<8|u>>>24)&0xff00ff|(u<<24|u>>>8)&0xff00ff00,c.push(u),c.push(f)}return new s.init(c,o)},clone:function(){for(var e=n.clone.call(this),t=e._state=this._state.slice(0),s=0;s<25;s++)t[s]=t[s].clone();return e}});r.SHA3=n._createHelper(f),r.HmacSHA3=n._createHmacHelper(f)}(Math),e.exports=r.SHA3},5371:function(e,t,s){var r,n,a,i,o,l,c;r=s(3652),s(9734),s(8832),a=(n=r.lib).Base,i=n.WordArray,l=(o=r.algo).MD5,c=o.EvpKDF=a.extend({cfg:a.extend({keySize:4,hasher:l,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var s,r=this.cfg,n=r.hasher.create(),a=i.create(),o=a.words,l=r.keySize,c=r.iterations;o.length<l;){s&&n.update(s),s=n.update(e).finalize(t),n.reset();for(var d=1;d<c;d++)s=n.finalize(s),n.reset();a.concat(s)}return a.sigBytes=4*l,a}}),r.EvpKDF=function(e,t,s){return c.create(s).compute(e,t)},e.exports=r.EvpKDF},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},5883:function(e,t,s){var r,n;n=(r=s(3652)).lib.WordArray,r.enc.Base64={stringify:function(e){var t=e.words,s=e.sigBytes,r=this._map;e.clamp();for(var n=[],a=0;a<s;a+=3)for(var i=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,o=0;o<4&&a+.75*o<s;o++)n.push(r.charAt(i>>>6*(3-o)&63));var l=r.charAt(64);if(l)for(;n.length%4;)n.push(l);return n.join("")},parse:function(e){var t=e.length,s=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var a=0;a<s.length;a++)r[s.charCodeAt(a)]=a}var i=s.charAt(64);if(i){var o=e.indexOf(i);-1!==o&&(t=o)}for(var l=e,c=t,d=r,h=[],f=0,u=0;u<c;u++)if(u%4){var x=d[l.charCodeAt(u-1)]<<u%4*2|d[l.charCodeAt(u)]>>>6-u%4*2;h[f>>>2]|=x<<24-f%4*8,f++}return n.create(h,f)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.exports=r.enc.Base64},6281:(e,t,s)=>{"use strict";var r,n,a,i,o,l,c,d,h,f,u,x,p,b,g,m,y,_,w,v,S,k,A,R,$,B,O,I,E,P,M,C,T,j,N,D,L,W,H,U,q,F,z,X,J,K,V,G,Q;let Y,Z,ee;function et(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function es(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}s.d(t,{Ay:()=>tL});let er=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return er=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function en(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let ea=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class ei extends Error{}class eo extends ei{constructor(e,t,s,r){super(`${eo.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("request-id"),this.error=t}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){return e&&r?400===e?new eh(e,t,s,r):401===e?new ef(e,t,s,r):403===e?new eu(e,t,s,r):404===e?new ex(e,t,s,r):409===e?new ep(e,t,s,r):422===e?new eb(e,t,s,r):429===e?new eg(e,t,s,r):e>=500?new em(e,t,s,r):new eo(e,t,s,r):new ec({message:s,cause:ea(t)})}}class el extends eo{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class ec extends eo{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class ed extends ec{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eh extends eo{}class ef extends eo{}class eu extends eo{}class ex extends eo{}class ep extends eo{}class eb extends eo{}class eg extends eo{}class em extends eo{}let ey=/^[a-z][a-z0-9+.-]*:/i,e_=e=>ey.test(e);function ew(e){return"object"!=typeof e?{}:e??{}}let ev=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new ei(`${e} must be an integer`);if(t<0)throw new ei(`${e} must be a positive integer`);return t},eS=e=>{try{return JSON.parse(e)}catch(e){return}},ek=e=>new Promise(t=>setTimeout(t,e)),eA={off:0,error:200,warn:300,info:400,debug:500},eR=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(eA,e))return e;eE(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eA))}`)}};function e$(){}function eB(e,t,s){return!t||eA[e]>eA[s]?e$:t[e].bind(t)}let eO={error:e$,warn:e$,info:e$,debug:e$},eI=new WeakMap;function eE(e){let t=e.logger,s=e.logLevel??"off";if(!t)return eO;let r=eI.get(t);if(r&&r[0]===s)return r[1];let n={error:eB("error",t,s),warn:eB("warn",t,s),info:eB("info",t,s),debug:eB("debug",t,s)};return eI.set(t,[s,n]),n}let eP=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"x-api-key"===e.toLowerCase()||"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),eM="0.52.0",eC=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,eT=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eM,"X-Stainless-OS":eN(Deno.build.os),"X-Stainless-Arch":ej(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eM,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eM,"X-Stainless-OS":eN(globalThis.process.platform),"X-Stainless-Arch":ej(globalThis.process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eM,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eM,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},ej=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",eN=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",eD=()=>Y??(Y=eT());function eL(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function eW(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return eL({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function eH(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function eU(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let eq=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)});function eF(e){let t;return(Z??(Z=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function ez(e){let t;return(ee??(ee=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class eX{constructor(){r.set(this,void 0),n.set(this,void 0),et(this,r,new Uint8Array,"f"),et(this,n,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?eF(e):e;et(this,r,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([es(this,r,"f"),s]),"f");let a=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(es(this,r,"f"),es(this,n,"f")));){if(t.carriage&&null==es(this,n,"f")){et(this,n,t.index,"f");continue}if(null!=es(this,n,"f")&&(t.index!==es(this,n,"f")+1||t.carriage)){a.push(ez(es(this,r,"f").subarray(0,es(this,n,"f")-1))),et(this,r,es(this,r,"f").subarray(es(this,n,"f")),"f"),et(this,n,null,"f");continue}let e=null!==es(this,n,"f")?t.preceding-1:t.preceding,s=ez(es(this,r,"f").subarray(0,e));a.push(s),et(this,r,es(this,r,"f").subarray(t.index),"f"),et(this,n,null,"f")}return a}flush(){return es(this,r,"f").length?this.decode("\n"):[]}}r=new WeakMap,n=new WeakMap,eX.NEWLINE_CHARS=new Set(["\n","\r"]),eX.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class eJ{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*r(){if(s)throw new ei("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of eK(e,t)){if("completion"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("message_start"===s.event||"message_delta"===s.event||"message_stop"===s.event||"content_block_start"===s.event||"content_block_delta"===s.event||"content_block_stop"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("ping"!==s.event&&"error"===s.event)throw new eo(void 0,eS(s.data)??s.data,void 0,e.headers)}r=!0}catch(e){if(en(e))return;throw e}finally{r||t.abort()}}return new eJ(r,t)}static fromReadableStream(e,t){let s=!1;async function*r(){let t=new eX;for await(let s of eH(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new eJ(async function*(){if(s)throw new ei("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(en(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new eJ(()=>r(e),this.controller),new eJ(()=>r(t),this.controller)]}toReadableStream(){let e,t=this;return eL({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=eF(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*eK(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ei("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ei("Attempted to iterate over a response with no body")}let s=new eG,r=new eX;for await(let t of eV(eH(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*eV(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?eF(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class eG{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function eQ(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(eE(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):eJ.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?eY(await s.json(),s):await s.text()})();return eE(e).debug(`[${r}] response parsed`,eP({retryOfRequestLogID:n,url:s.url,status:s.status,body:i,durationMs:Date.now()-a})),i}function eY(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("request-id"),enumerable:!1})}class eZ extends Promise{constructor(e,t,s=eQ){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,a.set(this,void 0),et(this,a,e,"f")}_thenUnwrap(e){return new eZ(es(this,a,"f"),this.responsePromise,async(t,s)=>eY(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(es(this,a,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}a=new WeakMap;class e0{constructor(e,t,s,r){i.set(this,void 0),et(this,i,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new ei("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await es(this,i,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(i=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class e1 extends eZ{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await eQ(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class e2 extends e0{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1,this.first_id=s.first_id||null,this.last_id=s.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){if(this.options.query?.before_id){let e=this.first_id;return e?{...this.options,query:{...ew(this.options.query),before_id:e}}:null}let e=this.last_id;return e?{...this.options,query:{...ew(this.options.query),after_id:e}}:null}}let e4=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function e8(e,t,s){return e4(),new File(e,t??"unknown_file",s)}function e6(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let e5=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],e3=async(e,t)=>({...e,body:await e7(e.body,t)}),e9=new WeakMap,e7=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=e9.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return e9.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tr(s,e,t))),s},te=e=>e instanceof Blob&&"name"in e,tt=e=>"object"==typeof e&&null!==e&&(e instanceof Response||e5(e)||te(e)),ts=e=>{if(tt(e))return!0;if(Array.isArray(e))return e.some(ts);if(e&&"object"==typeof e){for(let t in e)if(ts(e[t]))return!0}return!1},tr=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response){let r={},n=s.headers.get("Content-Type");n&&(r={type:n}),e.append(t,e8([await s.blob()],e6(s),r))}else if(e5(s))e.append(t,e8([await new Response(eW(s)).blob()],e6(s)));else if(te(s))e.append(t,e8([s],e6(s),{type:s.type}));else if(Array.isArray(s))await Promise.all(s.map(s=>tr(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>tr(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},tn=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,ta=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tn(e),ti=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function to(e,t,s){if(e4(),e=await e,t||(t=e6(e)),ta(e))return e instanceof File&&null==t&&null==s?e:e8([await e.arrayBuffer()],t??e.name,{type:e.type,lastModified:e.lastModified,...s});if(ti(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),e8(await tl(r),t,s)}let r=await tl(e);if(!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return e8(r,t,s)}async function tl(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tn(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(e5(e))for await(let s of e)t.push(...await tl(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tc{constructor(e){this._client=e}}let td=Symbol.for("brand.privateNullableHeaders"),th=Array.isArray,tf=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(td in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():th(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=th(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===a?(t.delete(n),s.add(r)):(t.append(n,a),s.delete(r))}}return{[td]:!0,values:t,nulls:s}};function tu(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let tx=((e=tu)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,r,a)=>(/[?#]/.test(r)&&(n=!0),t+r+(a===s.length?"":(n?encodeURIComponent:e)(String(s[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(i));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new ei(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(tu);class tp extends tc{list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/files",e2,{query:r,...t,headers:tf([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(tx`/v1/files/${e}`,{...s,headers:tf([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}download(e,t={},s){let{betas:r}=t??{};return this._client.get(tx`/v1/files/${e}/content`,{...s,headers:tf([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},s){let{betas:r}=t??{};return this._client.get(tx`/v1/files/${e}`,{...s,headers:tf([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}upload(e,t){let{betas:s,...r}=e;return this._client.post("/v1/files",e3({body:r,...t,headers:tf([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])},this._client))}}class tb extends tc{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tx`/v1/models/${e}?beta=true`,{...s,headers:tf([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models?beta=true",e2,{query:r,...t,headers:tf([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}class tg{constructor(e,t){this.iterator=e,this.controller=t}async *decoder(){let e=new eX;for await(let t of this.iterator)for(let s of e.decode(t))yield JSON.parse(s);for(let t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ei("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ei("Attempted to iterate over a response with no body")}return new tg(eH(e.body),t)}}class tm extends tc{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/batches?beta=true",{body:r,...t,headers:tf([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tx`/v1/messages/batches/${e}?beta=true`,{...s,headers:tf([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/messages/batches?beta=true",e2,{query:r,...t,headers:tf([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(tx`/v1/messages/batches/${e}?beta=true`,{...s,headers:tf([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}cancel(e,t={},s){let{betas:r}=t??{};return this._client.post(tx`/v1/messages/batches/${e}/cancel?beta=true`,{...s,headers:tf([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}async results(e,t={},s){let r=await this.retrieve(e);if(!r.results_url)throw new ei(`No batch \`results_url\`; Has it finished processing? ${r.processing_status} - ${r.id}`);let{betas:n}=t??{};return this._client.get(r.results_url,{...s,headers:tf([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},s?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>tg.fromResponse(t.response,t.controller))}}let ty=e=>{let t=0,s=[];for(;t<e.length;){let r=e[t];if("\\"===r){t++;continue}if("{"===r){s.push({type:"brace",value:"{"}),t++;continue}if("}"===r){s.push({type:"brace",value:"}"}),t++;continue}if("["===r){s.push({type:"paren",value:"["}),t++;continue}if("]"===r){s.push({type:"paren",value:"]"}),t++;continue}if(":"===r){s.push({type:"separator",value:":"}),t++;continue}if(","===r){s.push({type:"delimiter",value:","}),t++;continue}if('"'===r){let n="",a=!1;for(r=e[++t];'"'!==r;){if(t===e.length){a=!0;break}if("\\"===r){if(++t===e.length){a=!0;break}n+=r+e[t],r=e[++t]}else n+=r,r=e[++t]}r=e[++t],a||s.push({type:"string",value:n});continue}let n=/\s/;if(r&&n.test(r)){t++;continue}let a=/[0-9]/;if(r&&a.test(r)||"-"===r||"."===r){let n="";for("-"===r&&(n+=r,r=e[++t]);r&&a.test(r)||"."===r;)n+=r,r=e[++t];s.push({type:"number",value:n});continue}let i=/[a-z]/i;if(r&&i.test(r)){let n="";for(;r&&i.test(r)&&t!==e.length;)n+=r,r=e[++t];"true"==n||"false"==n||"null"===n?s.push({type:"name",value:n}):t++;continue}t++}return s},t_=e=>{if(0===e.length)return e;let t=e[e.length-1];switch(t.type){case"separator":return t_(e=e.slice(0,e.length-1));case"number":let s=t.value[t.value.length-1];if("."===s||"-"===s)return t_(e=e.slice(0,e.length-1));case"string":let r=e[e.length-2];if(r?.type==="delimiter"||r?.type==="brace"&&"{"===r.value)return t_(e=e.slice(0,e.length-1));break;case"delimiter":return t_(e=e.slice(0,e.length-1))}return e},tw=e=>{let t=[];return e.map(e=>{"brace"===e.type&&("{"===e.value?t.push("}"):t.splice(t.lastIndexOf("}"),1)),"paren"===e.type&&("["===e.value?t.push("]"):t.splice(t.lastIndexOf("]"),1))}),t.length>0&&t.reverse().map(t=>{"}"===t?e.push({type:"brace",value:"}"}):"]"===t&&e.push({type:"paren",value:"]"})}),e},tv=e=>{let t="";return e.map(e=>{"string"===e.type?t+='"'+e.value+'"':t+=e.value}),t},tS=e=>JSON.parse(tv(tw(t_(ty(e))))),tk="__json_buf";class tA{constructor(){o.add(this),this.messages=[],this.receivedMessages=[],l.set(this,void 0),this.controller=new AbortController,c.set(this,void 0),d.set(this,()=>{}),h.set(this,()=>{}),f.set(this,void 0),u.set(this,()=>{}),x.set(this,()=>{}),p.set(this,{}),b.set(this,!1),g.set(this,!1),m.set(this,!1),y.set(this,!1),_.set(this,void 0),w.set(this,void 0),k.set(this,e=>{if(et(this,g,!0,"f"),en(e)&&(e=new el),e instanceof el)return et(this,m,!0,"f"),this._emit("abort",e);if(e instanceof ei)return this._emit("error",e);if(e instanceof Error){let t=new ei(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ei(String(e)))}),et(this,c,new Promise((e,t)=>{et(this,d,e,"f"),et(this,h,t,"f")}),"f"),et(this,f,new Promise((e,t)=>{et(this,u,e,"f"),et(this,x,t,"f")}),"f"),es(this,c,"f").catch(()=>{}),es(this,f,"f").catch(()=>{})}get response(){return es(this,_,"f")}get request_id(){return es(this,w,"f")}async withResponse(){let e=await es(this,c,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new tA;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new tA;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},es(this,k,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),es(this,o,"m",A).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))es(this,o,"m",R).call(this,e);if(a.controller.signal?.aborted)throw new el;es(this,o,"m",$).call(this)}_connected(e){this.ended||(et(this,_,e,"f"),et(this,w,e?.headers.get("request-id"),"f"),es(this,d,"f").call(this,e),this._emit("connect"))}get ended(){return es(this,b,"f")}get errored(){return es(this,g,"f")}get aborted(){return es(this,m,"f")}abort(){this.controller.abort()}on(e,t){return(es(this,p,"f")[e]||(es(this,p,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=es(this,p,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(es(this,p,"f")[e]||(es(this,p,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{et(this,y,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){et(this,y,!0,"f"),await es(this,f,"f")}get currentMessage(){return es(this,l,"f")}async finalMessage(){return await this.done(),es(this,o,"m",v).call(this)}async finalText(){return await this.done(),es(this,o,"m",S).call(this)}_emit(e,...t){if(es(this,b,"f"))return;"end"===e&&(et(this,b,!0,"f"),es(this,u,"f").call(this));let s=es(this,p,"f")[e];if(s&&(es(this,p,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];es(this,y,"f")||s?.length||Promise.reject(e),es(this,h,"f").call(this,e),es(this,x,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];es(this,y,"f")||s?.length||Promise.reject(e),es(this,h,"f").call(this,e),es(this,x,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",es(this,o,"m",v).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),es(this,o,"m",A).call(this),this._connected(null);let r=eJ.fromReadableStream(e,this.controller);for await(let e of r)es(this,o,"m",R).call(this,e);if(r.controller.signal?.aborted)throw new el;es(this,o,"m",$).call(this)}[(l=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,f=new WeakMap,u=new WeakMap,x=new WeakMap,p=new WeakMap,b=new WeakMap,g=new WeakMap,m=new WeakMap,y=new WeakMap,_=new WeakMap,w=new WeakMap,k=new WeakMap,o=new WeakSet,v=function(){if(0===this.receivedMessages.length)throw new ei("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},S=function(){if(0===this.receivedMessages.length)throw new ei("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new ei("stream ended without producing a content block with type=text");return e.join(" ")},A=function(){this.ended||et(this,l,void 0,"f")},R=function(e){if(this.ended)return;let t=es(this,o,"m",B).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":("tool_use"===s.type||"mcp_tool_use"===s.type)&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":et(this,l,t,"f")}},$=function(){if(this.ended)throw new ei("stream has ended, this shouldn't happen");let e=es(this,l,"f");if(!e)throw new ei("request ended without sending any chunks");return et(this,l,void 0,"f"),e},B=function(e){let t=es(this,l,"f");if("message_start"===e.type){if(t)throw new ei(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new ei(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.container=e.delta.container,t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"||s?.type==="mcp_tool_use"){let t=s[tk]||"";Object.defineProperty(s,tk,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=tS(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new eJ(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}let tR={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},t$={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};class tB extends tc{constructor(){super(...arguments),this.batches=new tm(this._client)}create(e,t){let{betas:s,...r}=e;r.model in t$&&console.warn(`The model '${r.model}' is deprecated and will reach end-of-life on ${t$[r.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let n=this._client._options.timeout;if(!r.stream&&null==n){let e=tR[r.model]??void 0;n=this._client.calculateNonstreamingTimeout(r.max_tokens,e)}return this._client.post("/v1/messages?beta=true",{body:r,timeout:n??6e5,...t,headers:tf([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}stream(e,t){return tA.createMessage(this,e,t)}countTokens(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:r,...t,headers:tf([{"anthropic-beta":[...s??[],"token-counting-2024-11-01"].toString()},t?.headers])})}}tB.Batches=tm;class tO extends tc{constructor(){super(...arguments),this.models=new tb(this._client),this.messages=new tB(this._client),this.files=new tp(this._client)}}tO.Models=tb,tO.Messages=tB,tO.Files=tp;class tI extends tc{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/complete",{body:r,timeout:this._client._options.timeout??6e5,...t,headers:tf([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}}let tE="__json_buf";class tP{constructor(){O.add(this),this.messages=[],this.receivedMessages=[],I.set(this,void 0),this.controller=new AbortController,E.set(this,void 0),P.set(this,()=>{}),M.set(this,()=>{}),C.set(this,void 0),T.set(this,()=>{}),j.set(this,()=>{}),N.set(this,{}),D.set(this,!1),L.set(this,!1),W.set(this,!1),H.set(this,!1),U.set(this,void 0),q.set(this,void 0),X.set(this,e=>{if(et(this,L,!0,"f"),en(e)&&(e=new el),e instanceof el)return et(this,W,!0,"f"),this._emit("abort",e);if(e instanceof ei)return this._emit("error",e);if(e instanceof Error){let t=new ei(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ei(String(e)))}),et(this,E,new Promise((e,t)=>{et(this,P,e,"f"),et(this,M,t,"f")}),"f"),et(this,C,new Promise((e,t)=>{et(this,T,e,"f"),et(this,j,t,"f")}),"f"),es(this,E,"f").catch(()=>{}),es(this,C,"f").catch(()=>{})}get response(){return es(this,U,"f")}get request_id(){return es(this,q,"f")}async withResponse(){let e=await es(this,E,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new tP;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new tP;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},es(this,X,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),es(this,O,"m",J).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))es(this,O,"m",K).call(this,e);if(a.controller.signal?.aborted)throw new el;es(this,O,"m",V).call(this)}_connected(e){this.ended||(et(this,U,e,"f"),et(this,q,e?.headers.get("request-id"),"f"),es(this,P,"f").call(this,e),this._emit("connect"))}get ended(){return es(this,D,"f")}get errored(){return es(this,L,"f")}get aborted(){return es(this,W,"f")}abort(){this.controller.abort()}on(e,t){return(es(this,N,"f")[e]||(es(this,N,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=es(this,N,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(es(this,N,"f")[e]||(es(this,N,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{et(this,H,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){et(this,H,!0,"f"),await es(this,C,"f")}get currentMessage(){return es(this,I,"f")}async finalMessage(){return await this.done(),es(this,O,"m",F).call(this)}async finalText(){return await this.done(),es(this,O,"m",z).call(this)}_emit(e,...t){if(es(this,D,"f"))return;"end"===e&&(et(this,D,!0,"f"),es(this,T,"f").call(this));let s=es(this,N,"f")[e];if(s&&(es(this,N,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];es(this,H,"f")||s?.length||Promise.reject(e),es(this,M,"f").call(this,e),es(this,j,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];es(this,H,"f")||s?.length||Promise.reject(e),es(this,M,"f").call(this,e),es(this,j,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",es(this,O,"m",F).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),es(this,O,"m",J).call(this),this._connected(null);let r=eJ.fromReadableStream(e,this.controller);for await(let e of r)es(this,O,"m",K).call(this,e);if(r.controller.signal?.aborted)throw new el;es(this,O,"m",V).call(this)}[(I=new WeakMap,E=new WeakMap,P=new WeakMap,M=new WeakMap,C=new WeakMap,T=new WeakMap,j=new WeakMap,N=new WeakMap,D=new WeakMap,L=new WeakMap,W=new WeakMap,H=new WeakMap,U=new WeakMap,q=new WeakMap,X=new WeakMap,O=new WeakSet,F=function(){if(0===this.receivedMessages.length)throw new ei("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},z=function(){if(0===this.receivedMessages.length)throw new ei("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new ei("stream ended without producing a content block with type=text");return e.join(" ")},J=function(){this.ended||et(this,I,void 0,"f")},K=function(e){if(this.ended)return;let t=es(this,O,"m",G).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":"tool_use"===s.type&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":et(this,I,t,"f")}},V=function(){if(this.ended)throw new ei("stream has ended, this shouldn't happen");let e=es(this,I,"f");if(!e)throw new ei("request ended without sending any chunks");return et(this,I,void 0,"f"),e},G=function(e){let t=es(this,I,"f");if("message_start"===e.type){if(t)throw new ei(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new ei(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"){let t=s[tE]||"";Object.defineProperty(s,tE,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=tS(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new eJ(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class tM extends tc{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(tx`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",e2,{query:e,...t})}delete(e,t){return this._client.delete(tx`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(tx`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){let s=await this.retrieve(e);if(!s.results_url)throw new ei(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);return this._client.get(s.results_url,{...t,headers:tf([{Accept:"application/binary"},t?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>tg.fromResponse(t.response,t.controller))}}class tC extends tc{constructor(){super(...arguments),this.batches=new tM(this._client)}create(e,t){e.model in tT&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${tT[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let s=this._client._options.timeout;if(!e.stream&&null==s){let t=tR[e.model]??void 0;s=this._client.calculateNonstreamingTimeout(e.max_tokens,t)}return this._client.post("/v1/messages",{body:e,timeout:s??6e5,...t,stream:e.stream??!1})}stream(e,t){return tP.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}}let tT={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};tC.Batches=tM;class tj extends tc{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tx`/v1/models/${e}`,{...s,headers:tf([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models",e2,{query:r,...t,headers:tf([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}let tN=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class tD{constructor({baseURL:e=tN("ANTHROPIC_BASE_URL"),apiKey:t=tN("ANTHROPIC_API_KEY")??null,authToken:s=tN("ANTHROPIC_AUTH_TOKEN")??null,...r}={}){Q.set(this,void 0);let n={apiKey:t,authToken:s,...r,baseURL:e||"https://api.anthropic.com"};if(!n.dangerouslyAllowBrowser&&eC())throw new ei("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\n");this.baseURL=n.baseURL,this.timeout=n.timeout??tL.DEFAULT_TIMEOUT,this.logger=n.logger??console;let a="warn";this.logLevel=a,this.logLevel=eR(n.logLevel,"ClientOptions.logLevel",this)??eR(tN("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??a,this.fetchOptions=n.fetchOptions,this.maxRetries=n.maxRetries??2,this.fetch=n.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),et(this,Q,eq,"f"),this._options=n,this.apiKey=t,this.authToken=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key")||t.has("x-api-key")||this.authToken&&e.get("authorization"))&&!t.has("authorization"))throw Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(e){return tf([this.apiKeyAuth(e),this.bearerAuth(e)])}apiKeyAuth(e){if(null!=this.apiKey)return tf([{"X-Api-Key":this.apiKey}])}bearerAuth(e){if(null!=this.authToken)return tf([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new ei(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${eM}`}defaultIdempotencyKey(){return`stainless-node-retry-${er()}`}makeStatusError(e,t,s,r){return eo.generate(e,t,s,r)}buildURL(e,t){let s=new URL(e_(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new ei("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details");return 6e5}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new eZ(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:a,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,d=Date.now();if(eE(this).debug(`[${l}] sending request`,eP({retryOfRequestLogID:s,method:r.method,url:i,options:r,headers:a.headers})),r.signal?.aborted)throw new el;let h=new AbortController,f=await this.fetchWithTimeout(i,a,o,h).catch(ea),u=Date.now();if(f instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new el;let n=en(f)||/timed? ?out/i.test(String(f)+("cause"in f?String(f.cause):""));if(t)return eE(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),eE(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,eP({retryOfRequestLogID:s,url:i,durationMs:u-d,message:f.message})),this.retryRequest(r,t,s??l);if(eE(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),eE(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,eP({retryOfRequestLogID:s,url:i,durationMs:u-d,message:f.message})),n)throw new ed;throw new ec({cause:f})}let x=[...f.headers.entries()].filter(([e])=>"request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),p=`[${l}${c}${x}] ${a.method} ${i} ${f.ok?"succeeded":"failed"} with status ${f.status} in ${u-d}ms`;if(!f.ok){let e=this.shouldRetry(f);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await eU(f.body),eE(this).info(`${p} - ${e}`),eE(this).debug(`[${l}] response error (${e})`,eP({retryOfRequestLogID:s,url:f.url,status:f.status,headers:f.headers,durationMs:u-d})),this.retryRequest(r,t,s??l,f.headers)}let n=e?"error; no more retries left":"error; not retryable";eE(this).info(`${p} - ${n}`);let a=await f.text().catch(e=>ea(e).message),i=eS(a),o=i?void 0:a;throw eE(this).debug(`[${l}] response error (${n})`,eP({retryOfRequestLogID:s,url:f.url,status:f.status,headers:f.headers,message:o,durationMs:Date.now()-d})),this.makeStatusError(f.status,i,o,f.headers)}return eE(this).info(p),eE(this).debug(`[${l}] response start`,eP({retryOfRequestLogID:s,url:f.url,status:f.status,headers:f.headers,durationMs:u-d})),{response:f,options:r,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:d}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new e1(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,r){let n,a=r?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=r?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await ek(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||null!=t&&e>t)throw new ei("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:a}=s,i=this.buildURL(n,a);"timeout"in s&&ev("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:i,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=tf([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...eD(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=tf([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:eW(e)}:es(this,Q,"f").call(this,{body:e,headers:s})}}Q=new WeakMap,tD.Anthropic=tD,tD.HUMAN_PROMPT="\n\nHuman:",tD.AI_PROMPT="\n\nAssistant:",tD.DEFAULT_TIMEOUT=6e5,tD.AnthropicError=ei,tD.APIError=eo,tD.APIConnectionError=ec,tD.APIConnectionTimeoutError=ed,tD.APIUserAbortError=el,tD.NotFoundError=ex,tD.ConflictError=ep,tD.RateLimitError=eg,tD.BadRequestError=eh,tD.AuthenticationError=ef,tD.InternalServerError=em,tD.PermissionDeniedError=eu,tD.UnprocessableEntityError=eb,tD.toFile=to;class tL extends tD{constructor(){super(...arguments),this.completions=new tI(this),this.messages=new tC(this),this.models=new tj(this),this.beta=new tO(this)}}tL.Completions=tI,tL.Messages=tC,tL.Models=tj,tL.Beta=tO;let{HUMAN_PROMPT:tW,AI_PROMPT:tH}=tL},6560:function(e,t,s){var r;r=s(3652),s(92),r.pad.Iso10126={pad:function(e,t){var s=4*t,n=s-e.sigBytes%s;e.concat(r.lib.WordArray.random(n-1)).concat(r.lib.WordArray.create([n<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.exports=r.pad.Iso10126},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>n.a});var r=s(1469),n=s.n(r)},6819:function(e,t,s){var r;r=s(3652),s(92),r.pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([0x80000000],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},e.exports=r.pad.Iso97971},7458:function(e,t,s){var r,n,a;r=s(3652),s(92),r.mode.CTR=(a=(n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){var s=this._cipher,r=s.blockSize,n=this._iv,a=this._counter;n&&(a=this._counter=n.slice(0),this._iv=void 0);var i=a.slice(0);s.encryptBlock(i,0),a[r-1]=a[r-1]+1|0;for(var o=0;o<r;o++)e[t+o]^=i[o]}}),n.Decryptor=a,n),e.exports=r.mode.CTR},7776:function(e,t,s){var r,n,a;r=s(3652),s(92),n=r.lib.CipherParams,a=r.enc.Hex,r.format.Hex={stringify:function(e){return e.ciphertext.toString(a)},parse:function(e){var t=a.parse(e);return n.create({ciphertext:t})}},e.exports=r.format.Hex},7793:function(e,t,s){e.exports=function(e){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,s=t.init;(t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,r=[],n=0;n<t;n++)r[n>>>2]|=e[n]<<24-n%4*8;s.call(this,r,t)}else s.apply(this,arguments)}).prototype=t}return e.lib.WordArray}(s(3652))},8261:function(e,t,s){var r;r=s(3652),function(e){for(var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.algo,i=[],o=0;o<64;o++)i[o]=0x100000000*e.abs(e.sin(o+1))|0;var l=a.MD5=n.extend({_doReset:function(){this._hash=new s.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476])},_doProcessBlock:function(e,t){for(var s=0;s<16;s++){var r=t+s,n=e[r];e[r]=(n<<8|n>>>24)&0xff00ff|(n<<24|n>>>8)&0xff00ff00}var a=this._hash.words,o=e[t+0],l=e[t+1],u=e[t+2],x=e[t+3],p=e[t+4],b=e[t+5],g=e[t+6],m=e[t+7],y=e[t+8],_=e[t+9],w=e[t+10],v=e[t+11],S=e[t+12],k=e[t+13],A=e[t+14],R=e[t+15],$=a[0],B=a[1],O=a[2],I=a[3];$=c($,B,O,I,o,7,i[0]),I=c(I,$,B,O,l,12,i[1]),O=c(O,I,$,B,u,17,i[2]),B=c(B,O,I,$,x,22,i[3]),$=c($,B,O,I,p,7,i[4]),I=c(I,$,B,O,b,12,i[5]),O=c(O,I,$,B,g,17,i[6]),B=c(B,O,I,$,m,22,i[7]),$=c($,B,O,I,y,7,i[8]),I=c(I,$,B,O,_,12,i[9]),O=c(O,I,$,B,w,17,i[10]),B=c(B,O,I,$,v,22,i[11]),$=c($,B,O,I,S,7,i[12]),I=c(I,$,B,O,k,12,i[13]),O=c(O,I,$,B,A,17,i[14]),B=c(B,O,I,$,R,22,i[15]),$=d($,B,O,I,l,5,i[16]),I=d(I,$,B,O,g,9,i[17]),O=d(O,I,$,B,v,14,i[18]),B=d(B,O,I,$,o,20,i[19]),$=d($,B,O,I,b,5,i[20]),I=d(I,$,B,O,w,9,i[21]),O=d(O,I,$,B,R,14,i[22]),B=d(B,O,I,$,p,20,i[23]),$=d($,B,O,I,_,5,i[24]),I=d(I,$,B,O,A,9,i[25]),O=d(O,I,$,B,x,14,i[26]),B=d(B,O,I,$,y,20,i[27]),$=d($,B,O,I,k,5,i[28]),I=d(I,$,B,O,u,9,i[29]),O=d(O,I,$,B,m,14,i[30]),B=d(B,O,I,$,S,20,i[31]),$=h($,B,O,I,b,4,i[32]),I=h(I,$,B,O,y,11,i[33]),O=h(O,I,$,B,v,16,i[34]),B=h(B,O,I,$,A,23,i[35]),$=h($,B,O,I,l,4,i[36]),I=h(I,$,B,O,p,11,i[37]),O=h(O,I,$,B,m,16,i[38]),B=h(B,O,I,$,w,23,i[39]),$=h($,B,O,I,k,4,i[40]),I=h(I,$,B,O,o,11,i[41]),O=h(O,I,$,B,x,16,i[42]),B=h(B,O,I,$,g,23,i[43]),$=h($,B,O,I,_,4,i[44]),I=h(I,$,B,O,S,11,i[45]),O=h(O,I,$,B,R,16,i[46]),B=h(B,O,I,$,u,23,i[47]),$=f($,B,O,I,o,6,i[48]),I=f(I,$,B,O,m,10,i[49]),O=f(O,I,$,B,A,15,i[50]),B=f(B,O,I,$,b,21,i[51]),$=f($,B,O,I,S,6,i[52]),I=f(I,$,B,O,x,10,i[53]),O=f(O,I,$,B,w,15,i[54]),B=f(B,O,I,$,l,21,i[55]),$=f($,B,O,I,y,6,i[56]),I=f(I,$,B,O,R,10,i[57]),O=f(O,I,$,B,g,15,i[58]),B=f(B,O,I,$,k,21,i[59]),$=f($,B,O,I,p,6,i[60]),I=f(I,$,B,O,v,10,i[61]),O=f(O,I,$,B,u,15,i[62]),B=f(B,O,I,$,_,21,i[63]),a[0]=a[0]+$|0,a[1]=a[1]+B|0,a[2]=a[2]+O|0,a[3]=a[3]+I|0},_doFinalize:function(){var t=this._data,s=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;s[n>>>5]|=128<<24-n%32;var a=e.floor(r/0x100000000);s[(n+64>>>9<<4)+15]=(a<<8|a>>>24)&0xff00ff|(a<<24|a>>>8)&0xff00ff00,s[(n+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,t.sigBytes=(s.length+1)*4,this._process();for(var i=this._hash,o=i.words,l=0;l<4;l++){var c=o[l];o[l]=(c<<8|c>>>24)&0xff00ff|(c<<24|c>>>8)&0xff00ff00}return i},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,s,r,n,a,i){var o=e+(t&s|~t&r)+n+i;return(o<<a|o>>>32-a)+t}function d(e,t,s,r,n,a,i){var o=e+(t&r|s&~r)+n+i;return(o<<a|o>>>32-a)+t}function h(e,t,s,r,n,a,i){var o=e+(t^s^r)+n+i;return(o<<a|o>>>32-a)+t}function f(e,t,s,r,n,a,i){var o=e+(s^(t|~r))+n+i;return(o<<a|o>>>32-a)+t}r.MD5=n._createHelper(l),r.HmacMD5=n._createHmacHelper(l)}(Math),e.exports=r.MD5},8278:function(e,t,s){e.exports=function(e){var t=e.lib.WordArray,s=e.enc;function r(e){return e<<8&0xff00ff00|e>>>8&0xff00ff}return s.Utf16=s.Utf16BE={stringify:function(e){for(var t=e.words,s=e.sigBytes,r=[],n=0;n<s;n+=2){var a=t[n>>>2]>>>16-n%4*8&65535;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var s=e.length,r=[],n=0;n<s;n++)r[n>>>1]|=e.charCodeAt(n)<<16-n%2*16;return t.create(r,2*s)}},s.Utf16LE={stringify:function(e){for(var t=e.words,s=e.sigBytes,n=[],a=0;a<s;a+=2){var i=r(t[a>>>2]>>>16-a%4*8&65535);n.push(String.fromCharCode(i))}return n.join("")},parse:function(e){for(var s=e.length,n=[],a=0;a<s;a++)n[a>>>1]|=r(e.charCodeAt(a)<<16-a%2*16);return t.create(n,2*s)}},e.enc.Utf16}(s(3652))},8765:function(e,t,s){var r;r=s(3652),s(92),r.pad.NoPadding={pad:function(){},unpad:function(){}},e.exports=r.pad.NoPadding},8793:function(e,t,s){var r;r=s(3652),function(e){var t=r.lib,s=t.WordArray,n=t.Hasher,a=r.algo,i=s.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=s.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=s.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=s.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),d=s.create([0,0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xa953fd4e]),h=s.create([0x50a28be6,0x5c4dd124,0x6d703ef3,0x7a6d76e9,0]),f=a.RIPEMD160=n.extend({_doReset:function(){this._hash=s.create([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var s,r,n,a,f,x,p,b,g,m,y,_,w,v,S,k,A,R,$,B=0;B<16;B++){var O=t+B,I=e[O];e[O]=(I<<8|I>>>24)&0xff00ff|(I<<24|I>>>8)&0xff00ff00}var E=this._hash.words,P=d.words,M=h.words,C=i.words,T=o.words,j=l.words,N=c.words;v=g=E[0],S=m=E[1],k=y=E[2],A=_=E[3],R=w=E[4];for(var B=0;B<80;B+=1){$=g+e[t+C[B]]|0,B<16?$+=(m^y^_)+P[0]:B<32?$+=((s=m)&y|~s&_)+P[1]:B<48?$+=((m|~y)^_)+P[2]:B<64?$+=(r=m,n=y,(r&(a=_)|n&~a)+P[3]):$+=(m^(y|~_))+P[4],$|=0,$=($=u($,j[B]))+w|0,g=w,w=_,_=u(y,10),y=m,m=$,$=v+e[t+T[B]]|0,B<16?$+=(S^(k|~A))+M[0]:B<32?$+=(f=S,x=k,(f&(p=A)|x&~p)+M[1]):B<48?$+=((S|~k)^A)+M[2]:B<64?$+=((b=S)&k|~b&A)+M[3]:$+=(S^k^A)+M[4],$|=0,$=($=u($,N[B]))+R|0,v=R,R=A,A=u(k,10),k=S,S=$}$=E[1]+y+A|0,E[1]=E[2]+_+R|0,E[2]=E[3]+w+v|0,E[3]=E[4]+g+S|0,E[4]=E[0]+m+k|0,E[0]=$},_doFinalize:function(){var e=this._data,t=e.words,s=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[(r+64>>>9<<4)+14]=(s<<8|s>>>24)&0xff00ff|(s<<24|s>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();for(var n=this._hash,a=n.words,i=0;i<5;i++){var o=a[i];a[i]=(o<<8|o>>>24)&0xff00ff|(o<<24|o>>>8)&0xff00ff00}return n},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t){return e<<t|e>>>32-t}r.RIPEMD160=n._createHelper(f),r.HmacRIPEMD160=n._createHmacHelper(f)}(Math),e.exports=r.RIPEMD160},8832:function(e,t,s){var r,n,a;e.exports=void(n=(r=s(3652)).lib.Base,a=r.enc.Utf8,r.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var s=e.blockSize,r=4*s;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),i=this._iKey=t.clone(),o=n.words,l=i.words,c=0;c<s;c++)o[c]^=0x5c5c5c5c,l[c]^=0x36363636;n.sigBytes=i.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,s=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(s))}}))},9349:function(e,t,s){var r;r=s(3652),s(1609),function(){var e=r.lib.Hasher,t=r.x64,s=t.Word,n=t.WordArray,a=r.algo;function i(){return s.create.apply(s,arguments)}for(var o=[i(0x428a2f98,0xd728ae22),i(0x71374491,0x23ef65cd),i(0xb5c0fbcf,0xec4d3b2f),i(0xe9b5dba5,0x8189dbbc),i(0x3956c25b,0xf348b538),i(0x59f111f1,0xb605d019),i(0x923f82a4,0xaf194f9b),i(0xab1c5ed5,0xda6d8118),i(0xd807aa98,0xa3030242),i(0x12835b01,0x45706fbe),i(0x243185be,0x4ee4b28c),i(0x550c7dc3,0xd5ffb4e2),i(0x72be5d74,0xf27b896f),i(0x80deb1fe,0x3b1696b1),i(0x9bdc06a7,0x25c71235),i(0xc19bf174,0xcf692694),i(0xe49b69c1,0x9ef14ad2),i(0xefbe4786,0x384f25e3),i(0xfc19dc6,0x8b8cd5b5),i(0x240ca1cc,0x77ac9c65),i(0x2de92c6f,0x592b0275),i(0x4a7484aa,0x6ea6e483),i(0x5cb0a9dc,0xbd41fbd4),i(0x76f988da,0x831153b5),i(0x983e5152,0xee66dfab),i(0xa831c66d,0x2db43210),i(0xb00327c8,0x98fb213f),i(0xbf597fc7,0xbeef0ee4),i(0xc6e00bf3,0x3da88fc2),i(0xd5a79147,0x930aa725),i(0x6ca6351,0xe003826f),i(0x14292967,0xa0e6e70),i(0x27b70a85,0x46d22ffc),i(0x2e1b2138,0x5c26c926),i(0x4d2c6dfc,0x5ac42aed),i(0x53380d13,0x9d95b3df),i(0x650a7354,0x8baf63de),i(0x766a0abb,0x3c77b2a8),i(0x81c2c92e,0x47edaee6),i(0x92722c85,0x1482353b),i(0xa2bfe8a1,0x4cf10364),i(0xa81a664b,0xbc423001),i(0xc24b8b70,0xd0f89791),i(0xc76c51a3,0x654be30),i(0xd192e819,0xd6ef5218),i(0xd6990624,0x5565a910),i(0xf40e3585,0x5771202a),i(0x106aa070,0x32bbd1b8),i(0x19a4c116,0xb8d2d0c8),i(0x1e376c08,0x5141ab53),i(0x2748774c,0xdf8eeb99),i(0x34b0bcb5,0xe19b48a8),i(0x391c0cb3,0xc5c95a63),i(0x4ed8aa4a,0xe3418acb),i(0x5b9cca4f,0x7763e373),i(0x682e6ff3,0xd6b2b8a3),i(0x748f82ee,0x5defb2fc),i(0x78a5636f,0x43172f60),i(0x84c87814,0xa1f0ab72),i(0x8cc70208,0x1a6439ec),i(0x90befffa,0x23631e28),i(0xa4506ceb,0xde82bde9),i(0xbef9a3f7,0xb2c67915),i(0xc67178f2,0xe372532b),i(0xca273ece,0xea26619c),i(0xd186b8c7,0x21c0c207),i(0xeada7dd6,0xcde0eb1e),i(0xf57d4f7f,0xee6ed178),i(0x6f067aa,0x72176fba),i(0xa637dc5,0xa2c898a6),i(0x113f9804,0xbef90dae),i(0x1b710b35,0x131c471b),i(0x28db77f5,0x23047d84),i(0x32caab7b,0x40c72493),i(0x3c9ebe0a,0x15c9bebc),i(0x431d67c4,0x9c100d4c),i(0x4cc5d4be,0xcb3e42b6),i(0x597f299c,0xfc657e2a),i(0x5fcb6fab,0x3ad6faec),i(0x6c44198c,0x4a475817)],l=[],c=0;c<80;c++)l[c]=i();var d=a.SHA512=e.extend({_doReset:function(){this._hash=new n.init([new s.init(0x6a09e667,0xf3bcc908),new s.init(0xbb67ae85,0x84caa73b),new s.init(0x3c6ef372,0xfe94f82b),new s.init(0xa54ff53a,0x5f1d36f1),new s.init(0x510e527f,0xade682d1),new s.init(0x9b05688c,0x2b3e6c1f),new s.init(0x1f83d9ab,0xfb41bd6b),new s.init(0x5be0cd19,0x137e2179)])},_doProcessBlock:function(e,t){for(var s=this._hash.words,r=s[0],n=s[1],a=s[2],i=s[3],c=s[4],d=s[5],h=s[6],f=s[7],u=r.high,x=r.low,p=n.high,b=n.low,g=a.high,m=a.low,y=i.high,_=i.low,w=c.high,v=c.low,S=d.high,k=d.low,A=h.high,R=h.low,$=f.high,B=f.low,O=u,I=x,E=p,P=b,M=g,C=m,T=y,j=_,N=w,D=v,L=S,W=k,H=A,U=R,q=$,F=B,z=0;z<80;z++){var X,J,K=l[z];if(z<16)J=K.high=0|e[t+2*z],X=K.low=0|e[t+2*z+1];else{var V=l[z-15],G=V.high,Q=V.low,Y=(G>>>1|Q<<31)^(G>>>8|Q<<24)^G>>>7,Z=(Q>>>1|G<<31)^(Q>>>8|G<<24)^(Q>>>7|G<<25),ee=l[z-2],et=ee.high,es=ee.low,er=(et>>>19|es<<13)^(et<<3|es>>>29)^et>>>6,en=(es>>>19|et<<13)^(es<<3|et>>>29)^(es>>>6|et<<26),ea=l[z-7],ei=ea.high,eo=ea.low,el=l[z-16],ec=el.high,ed=el.low;J=Y+ei+ +((X=Z+eo)>>>0<Z>>>0),X+=en,J=J+er+ +(X>>>0<en>>>0),X+=ed,K.high=J=J+ec+ +(X>>>0<ed>>>0),K.low=X}var eh=N&L^~N&H,ef=D&W^~D&U,eu=O&E^O&M^E&M,ex=I&P^I&C^P&C,ep=(O>>>28|I<<4)^(O<<30|I>>>2)^(O<<25|I>>>7),eb=(I>>>28|O<<4)^(I<<30|O>>>2)^(I<<25|O>>>7),eg=(N>>>14|D<<18)^(N>>>18|D<<14)^(N<<23|D>>>9),em=(D>>>14|N<<18)^(D>>>18|N<<14)^(D<<23|N>>>9),ey=o[z],e_=ey.high,ew=ey.low,ev=F+em,eS=q+eg+ +(ev>>>0<F>>>0),ev=ev+ef,eS=eS+eh+ +(ev>>>0<ef>>>0),ev=ev+ew,eS=eS+e_+ +(ev>>>0<ew>>>0),ev=ev+X,eS=eS+J+ +(ev>>>0<X>>>0),ek=eb+ex,eA=ep+eu+ +(ek>>>0<eb>>>0);q=H,F=U,H=L,U=W,L=N,W=D,N=T+eS+ +((D=j+ev|0)>>>0<j>>>0)|0,T=M,j=C,M=E,C=P,E=O,P=I,O=eS+eA+ +((I=ev+ek|0)>>>0<ev>>>0)|0}x=r.low=x+I,r.high=u+O+ +(x>>>0<I>>>0),b=n.low=b+P,n.high=p+E+ +(b>>>0<P>>>0),m=a.low=m+C,a.high=g+M+ +(m>>>0<C>>>0),_=i.low=_+j,i.high=y+T+ +(_>>>0<j>>>0),v=c.low=v+D,c.high=w+N+ +(v>>>0<D>>>0),k=d.low=k+W,d.high=S+L+ +(k>>>0<W>>>0),R=h.low=R+U,h.high=A+H+ +(R>>>0<U>>>0),B=f.low=B+F,f.high=$+q+ +(B>>>0<F>>>0)},_doFinalize:function(){var e=this._data,t=e.words,s=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[(r+128>>>10<<5)+30]=Math.floor(s/0x100000000),t[(r+128>>>10<<5)+31]=s,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});r.SHA512=e._createHelper(d),r.HmacSHA512=e._createHmacHelper(d)}(),e.exports=r.SHA512},9403:function(e,t,s){var r;r=s(3652),s(5883),s(8261),s(5371),s(92),function(){var e=r.lib.StreamCipher,t=r.algo,s=[],n=[],a=[],i=t.Rabbit=e.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,s=0;s<4;s++)e[s]=(e[s]<<8|e[s]>>>24)&0xff00ff|(e[s]<<24|e[s]>>>8)&0xff00ff00;var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,0xffff0000&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,0xffff0000&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,0xffff0000&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,0xffff0000&e[3]|65535&e[0]];this._b=0;for(var s=0;s<4;s++)o.call(this);for(var s=0;s<8;s++)n[s]^=r[s+4&7];if(t){var a=t.words,i=a[0],l=a[1],c=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,d=(l<<8|l>>>24)&0xff00ff|(l<<24|l>>>8)&0xff00ff00,h=c>>>16|0xffff0000&d,f=d<<16|65535&c;n[0]^=c,n[1]^=h,n[2]^=d,n[3]^=f,n[4]^=c,n[5]^=h,n[6]^=d,n[7]^=f;for(var s=0;s<4;s++)o.call(this)}},_doProcessBlock:function(e,t){var r=this._X;o.call(this),s[0]=r[0]^r[5]>>>16^r[3]<<16,s[1]=r[2]^r[7]>>>16^r[5]<<16,s[2]=r[4]^r[1]>>>16^r[7]<<16,s[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)s[n]=(s[n]<<8|s[n]>>>24)&0xff00ff|(s[n]<<24|s[n]>>>8)&0xff00ff00,e[t+n]^=s[n]},blockSize:4,ivSize:2});function o(){for(var e=this._X,t=this._C,s=0;s<8;s++)n[s]=t[s];t[0]=t[0]+0x4d34d34d+this._b|0,t[1]=t[1]+0xd34d34d3+ +(t[0]>>>0<n[0]>>>0)|0,t[2]=t[2]+0x34d34d34+ +(t[1]>>>0<n[1]>>>0)|0,t[3]=t[3]+0x4d34d34d+ +(t[2]>>>0<n[2]>>>0)|0,t[4]=t[4]+0xd34d34d3+ +(t[3]>>>0<n[3]>>>0)|0,t[5]=t[5]+0x34d34d34+ +(t[4]>>>0<n[4]>>>0)|0,t[6]=t[6]+0x4d34d34d+ +(t[5]>>>0<n[5]>>>0)|0,t[7]=t[7]+0xd34d34d3+ +(t[6]>>>0<n[6]>>>0)|0,this._b=+(t[7]>>>0<n[7]>>>0);for(var s=0;s<8;s++){var r=e[s]+t[s],i=65535&r,o=r>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((0xffff0000&r)*r|0)+((65535&r)*r|0);a[s]=l^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}r.Rabbit=e._createHelper(i)}(),e.exports=r.Rabbit},9734:function(e,t,s){var r,n,a,i,o,l,c;a=(n=(r=s(3652)).lib).WordArray,i=n.Hasher,o=r.algo,l=[],c=o.SHA1=i.extend({_doReset:function(){this._hash=new a.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var s=this._hash.words,r=s[0],n=s[1],a=s[2],i=s[3],o=s[4],c=0;c<80;c++){if(c<16)l[c]=0|e[t+c];else{var d=l[c-3]^l[c-8]^l[c-14]^l[c-16];l[c]=d<<1|d>>>31}var h=(r<<5|r>>>27)+o+l[c];c<20?h+=(n&a|~n&i)+0x5a827999:c<40?h+=(n^a^i)+0x6ed9eba1:c<60?h+=(n&a|n&i|a&i)-0x70e44324:h+=(n^a^i)-0x359d3e2a,o=i,i=a,a=n<<30|n>>>2,n=r,r=h}s[0]=s[0]+r|0,s[1]=s[1]+n|0,s[2]=s[2]+a|0,s[3]=s[3]+i|0,s[4]=s[4]+o|0},_doFinalize:function(){var e=this._data,t=e.words,s=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[(r+64>>>9<<4)+14]=Math.floor(s/0x100000000),t[(r+64>>>9<<4)+15]=s,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=i._createHelper(c),r.HmacSHA1=i._createHmacHelper(c),e.exports=r.SHA1},9882:(e,t,s)=>{"use strict";let r,n,a;function i(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function o(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}s.d(t,{Ay:()=>s6});let l=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return l=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function c(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let d=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class h extends Error{}class f extends h{constructor(e,t,s,r){super(`${f.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){if(!e||!r)return new x({message:s,cause:d(t)});let n=t?.error;return 400===e?new b(e,n,s,r):401===e?new g(e,n,s,r):403===e?new m(e,n,s,r):404===e?new y(e,n,s,r):409===e?new _(e,n,s,r):422===e?new w(e,n,s,r):429===e?new v(e,n,s,r):e>=500?new S(e,n,s,r):new f(e,n,s,r)}}class u extends f{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class x extends f{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class p extends x{constructor({message:e}={}){super({message:e??"Request timed out."})}}class b extends f{}class g extends f{}class m extends f{}class y extends f{}class _ extends f{}class w extends f{}class v extends f{}class S extends f{}class k extends h{constructor(){super("Could not parse response content as the length limit was reached")}}class A extends h{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let R=/^[a-z][a-z0-9+.-]*:/i,$=e=>R.test(e);function B(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let O=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new h(`${e} must be an integer`);if(t<0)throw new h(`${e} must be a positive integer`);return t},I=e=>{try{return JSON.parse(e)}catch(e){return}},E=e=>new Promise(t=>setTimeout(t,e)),P={off:0,error:200,warn:300,info:400,debug:500},M=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(P,e))return e;D(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(P))}`)}};function C(){}function T(e,t,s){return!t||P[e]>P[s]?C:t[e].bind(t)}let j={error:C,warn:C,info:C,debug:C},N=new WeakMap;function D(e){let t=e.logger,s=e.logLevel??"off";if(!t)return j;let r=N.get(t);if(r&&r[0]===s)return r[1];let n={error:T("error",t,s),warn:T("warn",t,s),info:T("info",t,s),debug:T("debug",t,s)};return N.set(t,[s,n]),n}let L=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),W="5.0.1",H=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,U=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":W,"X-Stainless-OS":F(Deno.build.os),"X-Stainless-Arch":q(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":W,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":W,"X-Stainless-OS":F(globalThis.process.platform??"unknown"),"X-Stainless-Arch":q(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":W,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":W,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},q=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",F=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",z=()=>r??(r=U());function X(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function J(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return X({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function K(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function V(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let G=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),Q="RFC3986",Y={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},Z=(Object.prototype.hasOwnProperty,Array.isArray),ee=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function et(e,t){if(Z(e)){let s=[];for(let r=0;r<e.length;r+=1)s.push(t(e[r]));return s}return t(e)}let es=Object.prototype.hasOwnProperty,er={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},en=Array.isArray,ea=Array.prototype.push,ei=function(e,t){ea.apply(e,en(t)?t:[t])},eo=Date.prototype.toISOString,el={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,r,n)=>{if(0===e.length)return e;let a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===s)return escape(a).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let i="";for(let e=0;e<a.length;e+=1024){let t=a.length>=1024?a.slice(e,e+1024):a,s=[];for(let e=0;e<t.length;++e){let r=t.charCodeAt(e);if(45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||"RFC1738"===n&&(40===r||41===r)){s[s.length]=t.charAt(e);continue}if(r<128){s[s.length]=ee[r];continue}if(r<2048){s[s.length]=ee[192|r>>6]+ee[128|63&r];continue}if(r<55296||r>=57344){s[s.length]=ee[224|r>>12]+ee[128|r>>6&63]+ee[128|63&r];continue}e+=1,r=65536+((1023&r)<<10|1023&t.charCodeAt(e)),s[s.length]=ee[240|r>>18]+ee[128|r>>12&63]+ee[128|r>>6&63]+ee[128|63&r]}i+=s.join("")}return i},encodeValuesOnly:!1,format:Q,formatter:Y[Q],indices:!1,serializeDate:e=>eo.call(e),skipNulls:!1,strictNullHandling:!1},ec={};function ed(e){let t;return(n??(n=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function eh(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class ef{constructor(){tu.set(this,void 0),tx.set(this,void 0),i(this,tu,new Uint8Array,"f"),i(this,tx,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?ed(e):e;i(this,tu,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([o(this,tu,"f"),s]),"f");let r=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(o(this,tu,"f"),o(this,tx,"f")));){if(t.carriage&&null==o(this,tx,"f")){i(this,tx,t.index,"f");continue}if(null!=o(this,tx,"f")&&(t.index!==o(this,tx,"f")+1||t.carriage)){r.push(eh(o(this,tu,"f").subarray(0,o(this,tx,"f")-1))),i(this,tu,o(this,tu,"f").subarray(o(this,tx,"f")),"f"),i(this,tx,null,"f");continue}let e=null!==o(this,tx,"f")?t.preceding-1:t.preceding,s=eh(o(this,tu,"f").subarray(0,e));r.push(s),i(this,tu,o(this,tu,"f").subarray(t.index),"f"),i(this,tx,null,"f")}return r}flush(){return o(this,tu,"f").length?this.decode("\n"):[]}}tu=new WeakMap,tx=new WeakMap,ef.NEWLINE_CHARS=new Set(["\n","\r"]),ef.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class eu{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*r(){if(s)throw new h("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of ex(e,t))if(!r){if(s.data.startsWith("[DONE]")){r=!0;continue}if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if(t&&t.error)throw new f(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new f(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}}r=!0}catch(e){if(c(e))return;throw e}finally{r||t.abort()}}return new eu(r,t)}static fromReadableStream(e,t){let s=!1;async function*r(){let t=new ef;for await(let s of K(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new eu(async function*(){if(s)throw new h("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(c(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new eu(()=>r(e),this.controller),new eu(()=>r(t),this.controller)]}toReadableStream(){let e,t=this;return X({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=ed(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*ex(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new h("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new h("Attempted to iterate over a response with no body")}let s=new eb,r=new ef;for await(let t of ep(K(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*ep(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?ed(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class eb{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function eg(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(D(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):eu.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?em(await s.json(),s):await s.text()})();return D(e).debug(`[${r}] response parsed`,L({retryOfRequestLogID:n,url:s.url,status:s.status,body:i,durationMs:Date.now()-a})),i}function em(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class ey extends Promise{constructor(e,t,s=eg){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,tp.set(this,void 0),i(this,tp,e,"f")}_thenUnwrap(e){return new ey(o(this,tp,"f"),this.responsePromise,async(t,s)=>em(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(o(this,tp,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}tp=new WeakMap;class e_{constructor(e,t,s,r){tb.set(this,void 0),i(this,tb,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new h("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await o(this,tb,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(tb=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class ew extends ey{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await eg(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class ev extends e_{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class eS extends e_{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let ek=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function eA(e,t,s){return ek(),new File(e,t??"unknown_file",s)}function eR(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let e$=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],eB=async(e,t)=>({...e,body:await eI(e.body,t)}),eO=new WeakMap,eI=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=eO.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return eO.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>eC(s,e,t))),s},eE=e=>e instanceof Blob&&"name"in e,eP=e=>"object"==typeof e&&null!==e&&(e instanceof Response||e$(e)||eE(e)),eM=e=>{if(eP(e))return!0;if(Array.isArray(e))return e.some(eM);if(e&&"object"==typeof e){for(let t in e)if(eM(e[t]))return!0}return!1},eC=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,eA([await s.blob()],eR(s)));else if(e$(s))e.append(t,eA([await new Response(J(s)).blob()],eR(s)));else if(eE(s))e.append(t,s,eR(s));else if(Array.isArray(s))await Promise.all(s.map(s=>eC(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>eC(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},eT=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,ej=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&eT(e),eN=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function eD(e,t,s){if(ek(),ej(e=await e))return e instanceof File?e:eA([await e.arrayBuffer()],e.name);if(eN(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),eA(await eL(r),t,s)}let r=await eL(e);if(t||(t=eR(e)),!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return eA(r,t,s)}async function eL(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(eT(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(e$(e))for await(let s of e)t.push(...await eL(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class eW{constructor(e){this._client=e}}function eH(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let eU=((e=eH)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,r,a)=>(/[?#]/.test(r)&&(n=!0),t+r+(a===s.length?"":(n?encodeURIComponent:e)(String(s[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(i));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new h(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(eH);class eq extends eW{list(e,t={},s){return this._client.getAPIList(eU`/chat/completions/${e}/messages`,eS,{query:t,...s})}}let eF=e=>e?.role==="assistant",ez=e=>e?.role==="tool";class eX{constructor(){tg.add(this),this.controller=new AbortController,tm.set(this,void 0),ty.set(this,()=>{}),t_.set(this,()=>{}),tw.set(this,void 0),tv.set(this,()=>{}),tS.set(this,()=>{}),tk.set(this,{}),tA.set(this,!1),tR.set(this,!1),t$.set(this,!1),tB.set(this,!1),i(this,tm,new Promise((e,t)=>{i(this,ty,e,"f"),i(this,t_,t,"f")}),"f"),i(this,tw,new Promise((e,t)=>{i(this,tv,e,"f"),i(this,tS,t,"f")}),"f"),o(this,tm,"f").catch(()=>{}),o(this,tw,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},o(this,tg,"m",tO).bind(this))},0)}_connected(){this.ended||(o(this,ty,"f").call(this),this._emit("connect"))}get ended(){return o(this,tA,"f")}get errored(){return o(this,tR,"f")}get aborted(){return o(this,t$,"f")}abort(){this.controller.abort()}on(e,t){return(o(this,tk,"f")[e]||(o(this,tk,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=o(this,tk,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(o(this,tk,"f")[e]||(o(this,tk,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{i(this,tB,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){i(this,tB,!0,"f"),await o(this,tw,"f")}_emit(e,...t){if(o(this,tA,"f"))return;"end"===e&&(i(this,tA,!0,"f"),o(this,tv,"f").call(this));let s=o(this,tk,"f")[e];if(s&&(o(this,tk,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];o(this,tB,"f")||s?.length||Promise.reject(e),o(this,t_,"f").call(this,e),o(this,tS,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];o(this,tB,"f")||s?.length||Promise.reject(e),o(this,t_,"f").call(this,e),o(this,tS,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function eJ(e){return e?.$brand==="auto-parseable-response-format"}function eK(e){return e?.$brand==="auto-parseable-tool"}function eV(e,t){let s=e.choices.map(e=>{var s,r;if("length"===e.finish_reason)throw new k;if("content_filter"===e.finish_reason)throw new A;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:eK(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,r=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(r):JSON.parse(r):null):null}}});return{...e,choices:s}}function eG(e){return!!eJ(e.response_format)||(e.tools?.some(e=>eK(e)||"function"===e.type&&!0===e.function.strict)??!1)}tm=new WeakMap,ty=new WeakMap,t_=new WeakMap,tw=new WeakMap,tv=new WeakMap,tS=new WeakMap,tk=new WeakMap,tA=new WeakMap,tR=new WeakMap,t$=new WeakMap,tB=new WeakMap,tg=new WeakSet,tO=function(e){if(i(this,tR,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new u),e instanceof u)return i(this,t$,!0,"f"),this._emit("abort",e);if(e instanceof h)return this._emit("error",e);if(e instanceof Error){let t=new h(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new h(String(e)))};class eQ extends eX{constructor(){super(...arguments),tI.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),ez(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(eF(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new h("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),o(this,tI,"m",tE).call(this)}async finalMessage(){return await this.done(),o(this,tI,"m",tP).call(this)}async finalFunctionToolCall(){return await this.done(),o(this,tI,"m",tM).call(this)}async finalFunctionToolCallResult(){return await this.done(),o(this,tI,"m",tC).call(this)}async totalUsage(){return await this.done(),o(this,tI,"m",tT).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=o(this,tI,"m",tP).call(this);t&&this._emit("finalMessage",t);let s=o(this,tI,"m",tE).call(this);s&&this._emit("finalContent",s);let r=o(this,tI,"m",tM).call(this);r&&this._emit("finalFunctionToolCall",r);let n=o(this,tI,"m",tC).call(this);null!=n&&this._emit("finalFunctionToolCallResult",n),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",o(this,tI,"m",tT).call(this))}async _createChatCompletion(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),o(this,tI,"m",tj).call(this,t);let n=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(eV(n,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let r="tool",{tool_choice:n="auto",stream:a,...i}=t,l="string"!=typeof n&&n?.function?.name,{maxChatCompletions:c=10}=s||{},d=t.tools.map(e=>{if(eK(e)){if(!e.$callback)throw new h("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),f={};for(let e of d)"function"===e.type&&(f[e.function.name||e.function.function.name]=e.function);let u="tools"in t?d.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<c;++t){let t=await this._createChatCompletion(e,{...i,tool_choice:n,tools:u,messages:[...this.messages]},s),a=t.choices[0]?.message;if(!a)throw new h("missing message in ChatCompletion response");if(!a.tool_calls?.length)break;for(let e of a.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:n,arguments:a}=e.function,i=f[n];if(i){if(l&&l!==n){let e=`Invalid tool_call: ${JSON.stringify(n)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:r,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(n)}. Available options are: ${Object.keys(f).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:r,tool_call_id:s,content:e});continue}try{t="function"==typeof i.parse?await i.parse(a):a}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:r,tool_call_id:s,content:e});continue}let c=await i.function(t,this),d=o(this,tI,"m",tN).call(this,c);if(this._addMessage({role:r,tool_call_id:s,content:d}),l)return}}}}tI=new WeakSet,tE=function(){return o(this,tI,"m",tP).call(this).content??null},tP=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(eF(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new h("stream ended without producing a ChatCompletionMessage with role=assistant")},tM=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(eF(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},tC=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(ez(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},tT=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},tj=function(e){if(null!=e.n&&e.n>1)throw new h("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},tN=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class eY extends eQ{static runTools(e,t,s){let r=new eY,n={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}_addMessage(e,t=!0){super._addMessage(e,t),eF(e)&&e.content&&this._emit("content",e.content)}}let eZ={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class e0 extends Error{}class e1 extends Error{}let e2=(e,t)=>{let s=e.length,r=0,n=e=>{throw new e0(`${e} at position ${r}`)},a=e=>{throw new e1(`${e} at position ${r}`)},i=()=>(h(),r>=s&&n("Unexpected end of input"),'"'===e[r])?o():"{"===e[r]?l():"["===e[r]?c():"null"===e.substring(r,r+4)||eZ.NULL&t&&s-r<4&&"null".startsWith(e.substring(r))?(r+=4,null):"true"===e.substring(r,r+4)||eZ.BOOL&t&&s-r<4&&"true".startsWith(e.substring(r))?(r+=4,!0):"false"===e.substring(r,r+5)||eZ.BOOL&t&&s-r<5&&"false".startsWith(e.substring(r))?(r+=5,!1):"Infinity"===e.substring(r,r+8)||eZ.INFINITY&t&&s-r<8&&"Infinity".startsWith(e.substring(r))?(r+=8,1/0):"-Infinity"===e.substring(r,r+9)||eZ.MINUS_INFINITY&t&&1<s-r&&s-r<9&&"-Infinity".startsWith(e.substring(r))?(r+=9,-1/0):"NaN"===e.substring(r,r+3)||eZ.NAN&t&&s-r<3&&"NaN".startsWith(e.substring(r))?(r+=3,NaN):d(),o=()=>{let i=r,o=!1;for(r++;r<s&&('"'!==e[r]||o&&"\\"===e[r-1]);)o="\\"===e[r]&&!o,r++;if('"'==e.charAt(r))try{return JSON.parse(e.substring(i,++r-Number(o)))}catch(e){a(String(e))}else if(eZ.STR&t)try{return JSON.parse(e.substring(i,r-Number(o))+'"')}catch(t){return JSON.parse(e.substring(i,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{r++,h();let a={};try{for(;"}"!==e[r];){if(h(),r>=s&&eZ.OBJ&t)return a;let n=o();h(),r++;try{let e=i();Object.defineProperty(a,n,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(eZ.OBJ&t)return a;throw e}h(),","===e[r]&&r++}}catch(e){if(eZ.OBJ&t)return a;n("Expected '}' at end of object")}return r++,a},c=()=>{r++;let s=[];try{for(;"]"!==e[r];)s.push(i()),h(),","===e[r]&&r++}catch(e){if(eZ.ARR&t)return s;n("Expected ']' at end of array")}return r++,s},d=()=>{if(0===r){"-"===e&&eZ.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(eZ.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}a(String(s))}}let i=r;for("-"===e[r]&&r++;e[r]&&!",]}".includes(e[r]);)r++;r!=s||eZ.NUM&t||n("Unterminated number literal");try{return JSON.parse(e.substring(i,r))}catch(s){"-"===e.substring(i,r)&&eZ.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(i,e.lastIndexOf("e")))}catch(e){a(String(e))}}},h=()=>{for(;r<s&&" \n\r	".includes(e[r]);)r++};return i()},e4=e=>(function(e,t=eZ.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return e2(e.trim(),t)})(e,eZ.ALL^eZ.NUM);class e8 extends eQ{constructor(e){super(),tD.add(this),tL.set(this,void 0),tW.set(this,void 0),tH.set(this,void 0),i(this,tL,e,"f"),i(this,tW,[],"f")}get currentChatCompletionSnapshot(){return o(this,tH,"f")}static fromReadableStream(e){let t=new e8(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let r=new e8(t);return r._run(()=>r._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createChatCompletion(e,t,s){super._createChatCompletion;let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),o(this,tD,"m",tU).call(this);let n=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),n))o(this,tD,"m",tF).call(this,e);if(n.controller.signal?.aborted)throw new u;return this._addChatCompletion(o(this,tD,"m",tJ).call(this))}async _fromReadableStream(e,t){let s,r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),o(this,tD,"m",tU).call(this),this._connected();let n=eu.fromReadableStream(e,this.controller);for await(let e of n)s&&s!==e.id&&this._addChatCompletion(o(this,tD,"m",tJ).call(this)),o(this,tD,"m",tF).call(this,e),s=e.id;if(n.controller.signal?.aborted)throw new u;return this._addChatCompletion(o(this,tD,"m",tJ).call(this))}[(tL=new WeakMap,tW=new WeakMap,tH=new WeakMap,tD=new WeakSet,tU=function(){this.ended||i(this,tH,void 0,"f")},tq=function(e){let t=o(this,tW,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},o(this,tW,"f")[e.index]=t),t},tF=function(e){if(this.ended)return;let t=o(this,tD,"m",tV).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let r=o(this,tD,"m",tq).call(this,e);for(let t of(e.finish_reason&&(o(this,tD,"m",tX).call(this,e),null!=r.current_tool_call_index&&o(this,tD,"m",tz).call(this,e,r.current_tool_call_index)),s.delta.tool_calls??[]))r.current_tool_call_index!==t.index&&(o(this,tD,"m",tX).call(this,e),null!=r.current_tool_call_index&&o(this,tD,"m",tz).call(this,e,r.current_tool_call_index)),r.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},tz=function(e,t){if(o(this,tD,"m",tq).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=o(this,tL,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:eK(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},tX=function(e){let t=o(this,tD,"m",tq).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=o(this,tD,"m",tK).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},tJ=function(){if(this.ended)throw new h("stream has ended, this shouldn't happen");let e=o(this,tH,"f");if(!e)throw new h("request ended without sending any chunks");return i(this,tH,void 0,"f"),i(this,tW,[],"f"),function(e,t){var s;let{id:r,choices:n,created:a,model:i,system_fingerprint:o,...l}=e;return s={...l,id:r,choices:n.map(({message:t,finish_reason:s,index:r,logprobs:n,...a})=>{if(!s)throw new h(`missing finish_reason for choice ${r}`);let{content:i=null,function_call:o,tool_calls:l,...c}=t,d=t.role;if(!d)throw new h(`missing role for choice ${r}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new h(`missing function_call.arguments for choice ${r}`);if(!l)throw new h(`missing function_call.name for choice ${r}`);return{...a,message:{content:i,function_call:{arguments:e,name:l},role:d,refusal:t.refusal??null},finish_reason:s,index:r,logprobs:n}}return l?{...a,index:r,finish_reason:s,logprobs:n,message:{...c,role:d,content:i,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:n,type:a,id:i,...o}=t,{arguments:l,name:c,...d}=n||{};if(null==i)throw new h(`missing choices[${r}].tool_calls[${s}].id
${e6(e)}`);if(null==a)throw new h(`missing choices[${r}].tool_calls[${s}].type
${e6(e)}`);if(null==c)throw new h(`missing choices[${r}].tool_calls[${s}].function.name
${e6(e)}`);if(null==l)throw new h(`missing choices[${r}].tool_calls[${s}].function.arguments
${e6(e)}`);return{...o,id:i,type:a,function:{...d,name:c,arguments:l}}})}}:{...a,message:{...c,content:i,role:d,refusal:t.refusal??null},finish_reason:s,index:r,logprobs:n}}),created:a,model:i,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&eG(t)?eV(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,o(this,tL,"f"))},tK=function(){let e=o(this,tL,"f")?.response_format;return eJ(e)?e:null},tV=function(e){var t,s,r,n;let a=o(this,tH,"f"),{choices:l,...c}=e;for(let{delta:l,finish_reason:d,index:h,logprobs:f=null,...u}of(a?Object.assign(a,c):a=i(this,tH,{...c,choices:[]},"f"),e.choices)){let e=a.choices[h];if(e||(e=a.choices[h]={finish_reason:d,index:h,message:{},logprobs:f,...u}),f)if(e.logprobs){let{content:r,refusal:n,...a}=f;Object.assign(e.logprobs,a),r&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...r)),n&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...n))}else e.logprobs=Object.assign({},f);if(d&&(e.finish_reason=d,o(this,tL,"f")&&eG(o(this,tL,"f")))){if("length"===d)throw new k;if("content_filter"===d)throw new A}if(Object.assign(e,u),!l)continue;let{content:i,refusal:c,function_call:x,role:p,tool_calls:b,...g}=l;if(Object.assign(e.message,g),c&&(e.message.refusal=(e.message.refusal||"")+c),p&&(e.message.role=p),x&&(e.message.function_call?(x.name&&(e.message.function_call.name=x.name),x.arguments&&((r=e.message.function_call).arguments??(r.arguments=""),e.message.function_call.arguments+=x.arguments)):e.message.function_call=x),i&&(e.message.content=(e.message.content||"")+i,!e.message.refusal&&o(this,tD,"m",tK).call(this)&&(e.message.parsed=e4(e.message.content))),b)for(let{index:t,id:s,type:r,function:a,...i}of(e.message.tool_calls||(e.message.tool_calls=[]),b)){let l=(n=e.message.tool_calls)[t]??(n[t]={});Object.assign(l,i),s&&(l.id=s),r&&(l.type=r),a&&(l.function??(l.function={name:a.name??"",arguments:""})),a?.name&&(l.function.name=a.name),a?.arguments&&(l.function.arguments+=a.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return eK(s)||s?.function.strict||!1}(o(this,tL,"f"),l)&&(l.function.parsed_arguments=e4(l.function.arguments)))}}return a},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new eu(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function e6(e){return JSON.stringify(e)}class e5 extends e8{static fromReadableStream(e){let t=new e5(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let r=new e5(t),n={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,n)),r}}class e3 extends eW{constructor(){super(...arguments),this.messages=new eq(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(eU`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(eU`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",eS,{query:e,...t})}delete(e,t){return this._client.delete(eU`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new h(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new h(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>eV(t,e))}runTools(e,t){return e.stream?e5.runTools(this._client,e,t):eY.runTools(this._client,e,t)}stream(e,t){return e8.createChatCompletion(this._client,e,t)}}e3.Messages=eq;class e9 extends eW{constructor(){super(...arguments),this.completions=new e3(this._client)}}e9.Completions=e3;let e7=Symbol("brand.privateNullableHeaders"),te=Array.isArray,tt=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(e7 in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():te(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=te(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===a?(t.delete(n),s.add(r)):(t.append(n,a),s.delete(r))}}return{[e7]:!0,values:t,nulls:s}};class ts extends eW{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:tt([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class tr extends eW{create(e,t){return this._client.post("/audio/transcriptions",eB({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class tn extends eW{create(e,t){return this._client.post("/audio/translations",eB({body:e,...t,__metadata:{model:e.model}},this._client))}}class ta extends eW{constructor(){super(...arguments),this.transcriptions=new tr(this._client),this.translations=new tn(this._client),this.speech=new ts(this._client)}}ta.Transcriptions=tr,ta.Translations=tn,ta.Speech=ts;class ti extends eW{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(eU`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",eS,{query:e,...t})}cancel(e,t){return this._client.post(eU`/batches/${e}/cancel`,t)}}class to extends eW{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eU`/assistants/${e}`,{...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(eU`/assistants/${e}`,{body:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",eS,{query:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(eU`/assistants/${e}`,{...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class tl extends eW{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class tc extends eW{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class td extends eW{constructor(){super(...arguments),this.sessions=new tl(this._client),this.transcriptionSessions=new tc(this._client)}}td.Sessions=tl,td.TranscriptionSessions=tc;class th extends eW{create(e,t,s){return this._client.post(eU`/threads/${e}/messages`,{body:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:r}=t;return this._client.get(eU`/threads/${r}/messages/${e}`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:r,...n}=t;return this._client.post(eU`/threads/${r}/messages/${e}`,{body:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(eU`/threads/${e}/messages`,eS,{query:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:r}=t;return this._client.delete(eU`/threads/${r}/messages/${e}`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class tf extends eW{retrieve(e,t,s){let{thread_id:r,run_id:n,...a}=t;return this._client.get(eU`/threads/${r}/runs/${n}/steps/${e}`,{query:a,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:r,...n}=t;return this._client.getAPIList(eU`/threads/${r}/runs/${e}/steps`,eS,{query:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}var tu,tx,tp,tb,tg,tm,ty,t_,tw,tv,tS,tk,tA,tR,t$,tB,tO,tI,tE,tP,tM,tC,tT,tj,tN,tD,tL,tW,tH,tU,tq,tF,tz,tX,tJ,tK,tV,tG,tQ,tY,tZ,t0,t1,t2,t4,t8,t6,t5,t3,t9,t7,se,st,ss,sr,sn,sa,si,so,sl,sc,sd,sh,sf,su,sx,sp,sb,sg,sm,sy=s(9641).Buffer;let s_=e=>{if(void 0!==sy){let t=sy.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,r=new Uint8Array(s);for(let e=0;e<s;e++)r[e]=t.charCodeAt(e);return Array.from(new Float32Array(r.buffer))}},sw=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sv extends eX{constructor(){super(...arguments),tG.add(this),tY.set(this,[]),tZ.set(this,{}),t0.set(this,{}),t1.set(this,void 0),t2.set(this,void 0),t4.set(this,void 0),t8.set(this,void 0),t6.set(this,void 0),t5.set(this,void 0),t3.set(this,void 0),t9.set(this,void 0),t7.set(this,void 0)}[(tY=new WeakMap,tZ=new WeakMap,t0=new WeakMap,t1=new WeakMap,t2=new WeakMap,t4=new WeakMap,t8=new WeakMap,t6=new WeakMap,t5=new WeakMap,t3=new WeakMap,t9=new WeakMap,t7=new WeakMap,tG=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new tQ;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let r=eu.fromReadableStream(e,this.controller);for await(let e of r)o(this,tG,"m",se).call(this,e);if(r.controller.signal?.aborted)throw new u;return this._addRun(o(this,tG,"m",st).call(this))}toReadableStream(){return new eu(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,r){let n=new tQ;return n._run(()=>n._runToolAssistantStream(e,t,s,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createToolAssistantStream(e,t,s,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a={...s,stream:!0},i=await e.submitToolOutputs(t,a,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))o(this,tG,"m",se).call(this,e);if(i.controller.signal?.aborted)throw new u;return this._addRun(o(this,tG,"m",st).call(this))}static createThreadAssistantStream(e,t,s){let r=new tQ;return r._run(()=>r._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}static createAssistantStream(e,t,s,r){let n=new tQ;return n._run(()=>n._runAssistantStream(e,t,s,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}currentEvent(){return o(this,t3,"f")}currentRun(){return o(this,t9,"f")}currentMessageSnapshot(){return o(this,t1,"f")}currentRunStepSnapshot(){return o(this,t7,"f")}async finalRunSteps(){return await this.done(),Object.values(o(this,tZ,"f"))}async finalMessages(){return await this.done(),Object.values(o(this,t0,"f"))}async finalRun(){if(await this.done(),!o(this,t2,"f"))throw Error("Final run was not received.");return o(this,t2,"f")}async _createThreadAssistantStream(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let n={...t,stream:!0},a=await e.createAndRun(n,{...s,signal:this.controller.signal});for await(let e of(this._connected(),a))o(this,tG,"m",se).call(this,e);if(a.controller.signal?.aborted)throw new u;return this._addRun(o(this,tG,"m",st).call(this))}async _createAssistantStream(e,t,s,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a={...s,stream:!0},i=await e.create(t,a,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))o(this,tG,"m",se).call(this,e);if(i.controller.signal?.aborted)throw new u;return this._addRun(o(this,tG,"m",st).call(this))}static accumulateDelta(e,t){for(let[s,r]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=r;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=r;continue}if("string"==typeof t&&"string"==typeof r)t+=r;else if("number"==typeof t&&"number"==typeof r)t+=r;else if(B(t)&&B(r))t=this.accumulateDelta(t,r);else if(Array.isArray(t)&&Array.isArray(r)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...r);continue}for(let e of r){if(!B(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let r=t[s];null==r?t.push(e):t[s]=this.accumulateDelta(r,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${r}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,r){return await this._createAssistantStream(t,e,s,r)}async _runToolAssistantStream(e,t,s,r){return await this._createToolAssistantStream(t,e,s,r)}}tQ=sv,se=function(e){if(!this.ended)switch(i(this,t3,e,"f"),o(this,tG,"m",sn).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":o(this,tG,"m",sl).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":o(this,tG,"m",sr).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":o(this,tG,"m",ss).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},st=function(){if(this.ended)throw new h("stream has ended, this shouldn't happen");if(!o(this,t2,"f"))throw Error("Final run has not been received");return o(this,t2,"f")},ss=function(e){let[t,s]=o(this,tG,"m",si).call(this,e,o(this,t1,"f"));for(let e of(i(this,t1,t,"f"),o(this,t0,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,r=t.content[s.index];if(r&&"text"==r.type)this._emit("textDelta",e,r.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=o(this,t4,"f")){if(o(this,t8,"f"))switch(o(this,t8,"f").type){case"text":this._emit("textDone",o(this,t8,"f").text,o(this,t1,"f"));break;case"image_file":this._emit("imageFileDone",o(this,t8,"f").image_file,o(this,t1,"f"))}i(this,t4,s.index,"f")}i(this,t8,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==o(this,t4,"f")){let t=e.data.content[o(this,t4,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,o(this,t1,"f"));break;case"text":this._emit("textDone",t.text,o(this,t1,"f"))}}o(this,t1,"f")&&this._emit("messageDone",e.data),i(this,t1,void 0,"f")}},sr=function(e){let t=o(this,tG,"m",sa).call(this,e);switch(i(this,t7,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==o(this,t6,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(o(this,t5,"f")&&this._emit("toolCallDone",o(this,t5,"f")),i(this,t6,e.index,"f"),i(this,t5,t.step_details.tool_calls[e.index],"f"),o(this,t5,"f")&&this._emit("toolCallCreated",o(this,t5,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":i(this,t7,void 0,"f"),"tool_calls"==e.data.step_details.type&&o(this,t5,"f")&&(this._emit("toolCallDone",o(this,t5,"f")),i(this,t5,void 0,"f")),this._emit("runStepDone",e.data,t)}},sn=function(e){o(this,tY,"f").push(e),this._emit("event",e)},sa=function(e){switch(e.event){case"thread.run.step.created":return o(this,tZ,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=o(this,tZ,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let r=tQ.accumulateDelta(t,s.delta);o(this,tZ,"f")[e.data.id]=r}return o(this,tZ,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":o(this,tZ,"f")[e.data.id]=e.data}if(o(this,tZ,"f")[e.data.id])return o(this,tZ,"f")[e.data.id];throw Error("No snapshot available")},si=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let r=e.data;if(r.delta.content)for(let e of r.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=o(this,tG,"m",so).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},so=function(e,t){return tQ.accumulateDelta(t,e)},sl=function(e){switch(i(this,t9,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":i(this,t2,e.data,"f"),o(this,t5,"f")&&(this._emit("toolCallDone",o(this,t5,"f")),i(this,t5,void 0,"f"))}};class sS extends eW{constructor(){super(...arguments),this.steps=new tf(this._client)}create(e,t,s){let{include:r,...n}=t;return this._client.post(eU`/threads/${e}/runs`,{query:{include:r},body:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:r}=t;return this._client.get(eU`/threads/${r}/runs/${e}`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:r,...n}=t;return this._client.post(eU`/threads/${r}/runs/${e}`,{body:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(eU`/threads/${e}/runs`,eS,{query:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:r}=t;return this._client.post(eU`/threads/${r}/runs/${e}/cancel`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t,s);return await this.poll(r.id,{thread_id:e},s)}createAndStream(e,t,s){return sv.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let r=tt([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:a}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...r}}).withResponse();switch(n.status){case"queued":case"in_progress":case"cancelling":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await E(i);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return n}}}stream(e,t,s){return sv.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:r,...n}=t;return this._client.post(eU`/threads/${r}/runs/${e}/submit_tool_outputs`,{body:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let r=await this.submitToolOutputs(e,t,s);return await this.poll(r.id,t,s)}submitToolOutputsStream(e,t,s){return sv.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sS.Steps=tf;class sk extends eW{constructor(){super(...arguments),this.runs=new sS(this._client),this.messages=new th(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eU`/threads/${e}`,{...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(eU`/threads/${e}`,{body:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(eU`/threads/${e}`,{...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sv.createThreadAssistantStream(e,this._client.beta.threads,t)}}sk.Runs=sS,sk.Messages=th;class sA extends eW{constructor(){super(...arguments),this.realtime=new td(this._client),this.assistants=new to(this._client),this.threads=new sk(this._client)}}sA.Realtime=td,sA.Assistants=to,sA.Threads=sk;class sR extends eW{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class s$ extends eW{retrieve(e,t,s){let{container_id:r}=t;return this._client.get(eU`/containers/${r}/files/${e}/content`,{...s,headers:tt([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sB extends eW{constructor(){super(...arguments),this.content=new s$(this._client)}create(e,t,s){return this._client.post(eU`/containers/${e}/files`,eB({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:r}=t;return this._client.get(eU`/containers/${r}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(eU`/containers/${e}/files`,eS,{query:t,...s})}delete(e,t,s){let{container_id:r}=t;return this._client.delete(eU`/containers/${r}/files/${e}`,{...s,headers:tt([{Accept:"*/*"},s?.headers])})}}sB.Content=s$;class sO extends eW{constructor(){super(...arguments),this.files=new sB(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(eU`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",eS,{query:e,...t})}delete(e,t){return this._client.delete(eU`/containers/${e}`,{...t,headers:tt([{Accept:"*/*"},t?.headers])})}}sO.Files=sB;class sI extends eW{create(e,t){let s=!!e.encoding_format,r=s?e.encoding_format:"base64";s&&D(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let n=this._client.post("/embeddings",{body:{...e,encoding_format:r},...t});return s?n:(D(this._client).debug("embeddings/decoding base64 embeddings from base64"),n._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=s_(t)}),e)))}}class sE extends eW{retrieve(e,t,s){let{eval_id:r,run_id:n}=t;return this._client.get(eU`/evals/${r}/runs/${n}/output_items/${e}`,s)}list(e,t,s){let{eval_id:r,...n}=t;return this._client.getAPIList(eU`/evals/${r}/runs/${e}/output_items`,eS,{query:n,...s})}}class sP extends eW{constructor(){super(...arguments),this.outputItems=new sE(this._client)}create(e,t,s){return this._client.post(eU`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:r}=t;return this._client.get(eU`/evals/${r}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(eU`/evals/${e}/runs`,eS,{query:t,...s})}delete(e,t,s){let{eval_id:r}=t;return this._client.delete(eU`/evals/${r}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:r}=t;return this._client.post(eU`/evals/${r}/runs/${e}`,s)}}sP.OutputItems=sE;class sM extends eW{constructor(){super(...arguments),this.runs=new sP(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(eU`/evals/${e}`,t)}update(e,t,s){return this._client.post(eU`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",eS,{query:e,...t})}delete(e,t){return this._client.delete(eU`/evals/${e}`,t)}}sM.Runs=sP;class sC extends eW{create(e,t){return this._client.post("/files",eB({body:e,...t},this._client))}retrieve(e,t){return this._client.get(eU`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",eS,{query:e,...t})}delete(e,t){return this._client.delete(eU`/files/${e}`,t)}content(e,t){return this._client.get(eU`/files/${e}/content`,{...t,headers:tt([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let r=new Set(["processed","error","deleted"]),n=Date.now(),a=await this.retrieve(e);for(;!a.status||!r.has(a.status);)if(await E(t),a=await this.retrieve(e),Date.now()-n>s)throw new p({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return a}}class sT extends eW{}class sj extends eW{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sN extends eW{constructor(){super(...arguments),this.graders=new sj(this._client)}}sN.Graders=sj;class sD extends eW{create(e,t,s){return this._client.getAPIList(eU`/fine_tuning/checkpoints/${e}/permissions`,ev,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(eU`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:r}=t;return this._client.delete(eU`/fine_tuning/checkpoints/${r}/permissions/${e}`,s)}}class sL extends eW{constructor(){super(...arguments),this.permissions=new sD(this._client)}}sL.Permissions=sD;class sW extends eW{list(e,t={},s){return this._client.getAPIList(eU`/fine_tuning/jobs/${e}/checkpoints`,eS,{query:t,...s})}}class sH extends eW{constructor(){super(...arguments),this.checkpoints=new sW(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(eU`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",eS,{query:e,...t})}cancel(e,t){return this._client.post(eU`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(eU`/fine_tuning/jobs/${e}/events`,eS,{query:t,...s})}pause(e,t){return this._client.post(eU`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(eU`/fine_tuning/jobs/${e}/resume`,t)}}sH.Checkpoints=sW;class sU extends eW{constructor(){super(...arguments),this.methods=new sT(this._client),this.jobs=new sH(this._client),this.checkpoints=new sL(this._client),this.alpha=new sN(this._client)}}sU.Methods=sT,sU.Jobs=sH,sU.Checkpoints=sL,sU.Alpha=sN;class sq extends eW{}class sF extends eW{constructor(){super(...arguments),this.graderModels=new sq(this._client)}}sF.GraderModels=sq;class sz extends eW{createVariation(e,t){return this._client.post("/images/variations",eB({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",eB({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class sX extends eW{retrieve(e,t){return this._client.get(eU`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",ev,e)}delete(e,t){return this._client.delete(eU`/models/${e}`,t)}}class sJ extends eW{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function sK(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,r;return"output_text"===e.type?{...e,parsed:(s=t,r=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(r):JSON.parse(r))}:e});return{...e,content:s}}return e}),r=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||sV(r),Object.defineProperty(r,"output_parsed",{enumerable:!0,get(){for(let e of r.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),r}function sV(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class sG extends eX{constructor(e){super(),sc.add(this),sd.set(this,void 0),sh.set(this,void 0),sf.set(this,void 0),i(this,sd,e,"f")}static createResponse(e,t,s){let r=new sG(t);return r._run(()=>r._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createOrRetrieveResponse(e,t,s){let r,n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),o(this,sc,"m",su).call(this);let a=null;for await(let n of("response_id"in t?(r=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),a=t.starting_after??null):r=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),r))o(this,sc,"m",sx).call(this,n,a);if(r.controller.signal?.aborted)throw new u;return o(this,sc,"m",sp).call(this)}[(sd=new WeakMap,sh=new WeakMap,sf=new WeakMap,sc=new WeakSet,su=function(){this.ended||i(this,sh,void 0,"f")},sx=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},r=o(this,sc,"m",sb).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=r.output[e.output_index];if(!t)throw new h(`missing output at index ${e.output_index}`);if("message"===t.type){let r=t.content[e.content_index];if(!r)throw new h(`missing content at index ${e.content_index}`);if("output_text"!==r.type)throw new h(`expected content to be 'output_text', got ${r.type}`);s("response.output_text.delta",{...e,snapshot:r.text})}break}case"response.function_call_arguments.delta":{let t=r.output[e.output_index];if(!t)throw new h(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},sp=function(){if(this.ended)throw new h("stream has ended, this shouldn't happen");let e=o(this,sh,"f");if(!e)throw new h("request ended without sending any events");i(this,sh,void 0,"f");let t=function(e,t){var s;return t&&(s=t,eJ(s.text?.format))?sK(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,o(this,sd,"f"));return i(this,sf,t,"f"),t},sb=function(e){let t=o(this,sh,"f");if(!t){if("response.created"!==e.type)throw new h(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return i(this,sh,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new h(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new h(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new h(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new h(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new h(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":i(this,sh,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=o(this,sf,"f");if(!e)throw new h("stream ended without producing a ChatCompletion");return e}}class sQ extends eW{list(e,t={},s){return this._client.getAPIList(eU`/responses/${e}/input_items`,eS,{query:t,...s})}}class sY extends eW{constructor(){super(...arguments),this.inputItems=new sQ(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&sV(e),e))}retrieve(e,t={},s){return this._client.get(eU`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})}delete(e,t){return this._client.delete(eU`/responses/${e}`,{...t,headers:tt([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>sK(t,e))}stream(e,t){return sG.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(eU`/responses/${e}/cancel`,{...t,headers:tt([{Accept:"*/*"},t?.headers])})}}sY.InputItems=sQ;class sZ extends eW{create(e,t,s){return this._client.post(eU`/uploads/${e}/parts`,eB({body:t,...s},this._client))}}class s0 extends eW{constructor(){super(...arguments),this.parts=new sZ(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(eU`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(eU`/uploads/${e}/complete`,{body:t,...s})}}s0.Parts=sZ;let s1=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let r=[];for(let e of t)"fulfilled"===e.status&&r.push(e.value);return r};class s2 extends eW{create(e,t,s){return this._client.post(eU`/vector_stores/${e}/file_batches`,{body:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:r}=t;return this._client.get(eU`/vector_stores/${r}/file_batches/${e}`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:r}=t;return this._client.post(eU`/vector_stores/${r}/file_batches/${e}/cancel`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t);return await this.poll(e,r.id,s)}listFiles(e,t,s){let{vector_store_id:r,...n}=t;return this._client.getAPIList(eU`/vector_stores/${r}/file_batches/${e}/files`,eS,{query:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let r=tt([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:a}=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse();switch(n.status){case"in_progress":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=a.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await E(i);break;case"failed":case"cancelled":case"completed":return n}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},r){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let n=Math.min(r?.maxConcurrency??5,t.length),a=this._client,i=t.values(),o=[...s];async function l(e){for(let t of e){let e=await a.files.create({file:t,purpose:"assistants"},r);o.push(e.id)}}let c=Array(n).fill(i).map(l);return await s1(c),await this.createAndPoll(e,{file_ids:o})}}class s4 extends eW{create(e,t,s){return this._client.post(eU`/vector_stores/${e}/files`,{body:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:r}=t;return this._client.get(eU`/vector_stores/${r}/files/${e}`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:r,...n}=t;return this._client.post(eU`/vector_stores/${r}/files/${e}`,{body:n,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(eU`/vector_stores/${e}/files`,eS,{query:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:r}=t;return this._client.delete(eU`/vector_stores/${r}/files/${e}`,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let r=await this.create(e,t,s);return await this.poll(e,r.id,s)}async poll(e,t,s){let r=tt([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let n=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse(),a=n.data;switch(a.status){case"in_progress":let i=5e3;if(s?.pollIntervalMs)i=s.pollIntervalMs;else{let e=n.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(i=t)}}await E(i);break;case"failed":case"completed":return a}}}async upload(e,t,s){let r=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:r.id},s)}async uploadAndPoll(e,t,s){let r=await this.upload(e,t,s);return await this.poll(e,r.id,s)}content(e,t,s){let{vector_store_id:r}=t;return this._client.getAPIList(eU`/vector_stores/${r}/files/${e}/content`,ev,{...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s8 extends eW{constructor(){super(...arguments),this.files=new s4(this._client),this.fileBatches=new s2(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eU`/vector_stores/${e}`,{...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(eU`/vector_stores/${e}`,{body:t,...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",eS,{query:e,...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(eU`/vector_stores/${e}`,{...t,headers:tt([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(eU`/vector_stores/${e}/search`,ev,{body:t,method:"post",...s,headers:tt([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}s8.Files=s4,s8.FileBatches=s2;class s6{constructor({baseURL:e=sw("OPENAI_BASE_URL"),apiKey:t=sw("OPENAI_API_KEY"),organization:s=sw("OPENAI_ORG_ID")??null,project:r=sw("OPENAI_PROJECT_ID")??null,...n}={}){if(sm.set(this,void 0),this.completions=new sR(this),this.chat=new e9(this),this.embeddings=new sI(this),this.files=new sC(this),this.images=new sz(this),this.audio=new ta(this),this.moderations=new sJ(this),this.models=new sX(this),this.fineTuning=new sU(this),this.graders=new sF(this),this.vectorStores=new s8(this),this.beta=new sA(this),this.batches=new ti(this),this.uploads=new s0(this),this.responses=new sY(this),this.evals=new sM(this),this.containers=new sO(this),void 0===t)throw new h("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let a={apiKey:t,organization:s,project:r,...n,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&H())throw new h("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=a.baseURL,this.timeout=a.timeout??sg.DEFAULT_TIMEOUT,this.logger=a.logger??console;let o="warn";this.logLevel=o,this.logLevel=M(a.logLevel,"ClientOptions.logLevel",this)??M(sw("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??o,this.fetchOptions=a.fetchOptions,this.maxRetries=a.maxRetries??2,this.fetch=a.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),i(this,sm,G,"f"),this._options=a,this.apiKey=t,this.organization=s,this.project=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return tt([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,r,n=e,a=function(e=el){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||el.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=Q;if(void 0!==e.format){if(!es.call(Y,e.format))throw TypeError("Unknown format option provided.");r=e.format}let n=Y[r],a=el.filter;if(("function"==typeof e.filter||en(e.filter))&&(a=e.filter),t=e.arrayFormat&&e.arrayFormat in er?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":el.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let i=void 0===e.allowDots?!0==!!e.encodeDotInKeys||el.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:el.addQueryPrefix,allowDots:i,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:el.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:el.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?el.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:el.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:el.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:el.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:el.encodeValuesOnly,filter:a,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:el.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:el.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:el.strictNullHandling}}(t);"function"==typeof a.filter?n=(0,a.filter)("",n):en(a.filter)&&(s=a.filter);let i=[];if("object"!=typeof n||null===n)return"";let o=er[a.arrayFormat],l="comma"===o&&a.commaRoundTrip;s||(s=Object.keys(n)),a.sort&&s.sort(a.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];a.skipNulls&&null===n[t]||ei(i,function e(t,s,r,n,a,i,o,l,c,d,h,f,u,x,p,b,g,m){var y,_;let w,v=t,S=m,k=0,A=!1;for(;void 0!==(S=S.get(ec))&&!A;){let e=S.get(t);if(k+=1,void 0!==e)if(e===k)throw RangeError("Cyclic object value");else A=!0;void 0===S.get(ec)&&(k=0)}if("function"==typeof d?v=d(s,v):v instanceof Date?v=u?.(v):"comma"===r&&en(v)&&(v=et(v,function(e){return e instanceof Date?u?.(e):e})),null===v){if(i)return c&&!b?c(s,el.encoder,g,"key",x):s;v=""}if("string"==typeof(y=v)||"number"==typeof y||"boolean"==typeof y||"symbol"==typeof y||"bigint"==typeof y||(_=v)&&"object"==typeof _&&_.constructor&&_.constructor.isBuffer&&_.constructor.isBuffer(_)){if(c){let e=b?s:c(s,el.encoder,g,"key",x);return[p?.(e)+"="+p?.(c(v,el.encoder,g,"value",x))]}return[p?.(s)+"="+p?.(String(v))]}let R=[];if(void 0===v)return R;if("comma"===r&&en(v))b&&c&&(v=et(v,c)),w=[{value:v.length>0?v.join(",")||null:void 0}];else if(en(d))w=d;else{let e=Object.keys(v);w=h?e.sort(h):e}let $=l?String(s).replace(/\./g,"%2E"):String(s),B=n&&en(v)&&1===v.length?$+"[]":$;if(a&&en(v)&&0===v.length)return B+"[]";for(let s=0;s<w.length;++s){let y=w[s],_="object"==typeof y&&void 0!==y.value?y.value:v[y];if(o&&null===_)continue;let S=f&&l?y.replace(/\./g,"%2E"):y,A=en(v)?"function"==typeof r?r(B,S):B:B+(f?"."+S:"["+S+"]");m.set(t,k);let $=new WeakMap;$.set(ec,m),ei(R,e(_,A,r,n,a,i,o,l,"comma"===r&&b&&en(v)?null:c,d,h,f,u,x,p,b,g,$))}return R}(n[t],t,o,l,a.allowEmptyArrays,a.strictNullHandling,a.skipNulls,a.encodeDotInKeys,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset,c))}let d=i.join(a.delimiter),h=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),d.length>0?h+d:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${W}`}defaultIdempotencyKey(){return`stainless-node-retry-${l()}`}makeStatusError(e,t,s,r){return f.generate(e,t,s,r)}buildURL(e,t){let s=new URL($(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new ey(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:a,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),h=void 0===s?"":`, retryOf: ${s}`,f=Date.now();if(D(this).debug(`[${l}] sending request`,L({retryOfRequestLogID:s,method:r.method,url:i,options:r,headers:a.headers})),r.signal?.aborted)throw new u;let b=new AbortController,g=await this.fetchWithTimeout(i,a,o,b).catch(d),m=Date.now();if(g instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new u;let n=c(g)||/timed? ?out/i.test(String(g)+("cause"in g?String(g.cause):""));if(t)return D(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),D(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,L({retryOfRequestLogID:s,url:i,durationMs:m-f,message:g.message})),this.retryRequest(r,t,s??l);if(D(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),D(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,L({retryOfRequestLogID:s,url:i,durationMs:m-f,message:g.message})),n)throw new p;throw new x({cause:g})}let y=[...g.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),_=`[${l}${h}${y}] ${a.method} ${i} ${g.ok?"succeeded":"failed"} with status ${g.status} in ${m-f}ms`;if(!g.ok){let e=this.shouldRetry(g);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await V(g.body),D(this).info(`${_} - ${e}`),D(this).debug(`[${l}] response error (${e})`,L({retryOfRequestLogID:s,url:g.url,status:g.status,headers:g.headers,durationMs:m-f})),this.retryRequest(r,t,s??l,g.headers)}let n=e?"error; no more retries left":"error; not retryable";D(this).info(`${_} - ${n}`);let a=await g.text().catch(e=>d(e).message),i=I(a),o=i?void 0:a;throw D(this).debug(`[${l}] response error (${n})`,L({retryOfRequestLogID:s,url:g.url,status:g.status,headers:g.headers,message:o,durationMs:Date.now()-f})),this.makeStatusError(g.status,i,o,g.headers)}return D(this).info(_),D(this).debug(`[${l}] response start`,L({retryOfRequestLogID:s,url:g.url,status:g.status,headers:g.headers,durationMs:m-f})),{response:g,options:r,controller:b,requestLogID:l,retryOfRequestLogID:s,startTime:f}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new ew(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,r){let n,a=r?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=r?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await E(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:a}=s,i=this.buildURL(n,a);"timeout"in s&&O("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:i,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=tt([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...z(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=tt([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:J(e)}:o(this,sm,"f").call(this,{body:e,headers:s})}}sg=s6,sm=new WeakMap,s6.OpenAI=sg,s6.DEFAULT_TIMEOUT=6e5,s6.OpenAIError=h,s6.APIError=f,s6.APIConnectionError=x,s6.APIConnectionTimeoutError=p,s6.APIUserAbortError=u,s6.NotFoundError=y,s6.ConflictError=_,s6.RateLimitError=v,s6.BadRequestError=b,s6.AuthenticationError=g,s6.InternalServerError=S,s6.PermissionDeniedError=m,s6.UnprocessableEntityError=w,s6.toFile=eD,s6.Completions=sR,s6.Chat=e9,s6.Embeddings=sI,s6.Files=sC,s6.Images=sz,s6.Audio=ta,s6.Moderations=sJ,s6.Models=sX,s6.FineTuning=sU,s6.Graders=sF,s6.VectorStores=s8,s6.Beta=sA,s6.Batches=ti,s6.Uploads=s0,s6.Responses=sY,s6.Evals=sM,s6.Containers=sO,s(9509)},9930:function(e,t,s){var r;r=s(3652),s(92),r.pad.ZeroPadding={pad:function(e,t){var s=4*t;e.clamp(),e.sigBytes+=s-(e.sigBytes%s||s)},unpad:function(e){for(var t=e.words,s=e.sigBytes-1,s=e.sigBytes-1;s>=0;s--)if(t[s>>>2]>>>24-s%4*8&255){e.sigBytes=s+1;break}}},e.exports=r.pad.ZeroPadding}}]);