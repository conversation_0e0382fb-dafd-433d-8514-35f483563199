exports.id=77,exports.ids=[77],exports.modules={3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27436:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687),i=r(40303),a=r(43210);function n(){let[e,t]=(0,a.useState)(!1),[r,n]=(0,a.useState)("light"),o=(0,i.Q)(),l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};if(!e)return(0,s.jsx)("div",{className:"p-2 w-9 h-9"});let d=o?o.theme:r;return(0,s.jsx)("button",{onClick:()=>{if(o)o.toggleTheme();else{let e="light"===r?"dark":"light";n(e),l(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":`Switch to ${"light"===d?"dark":"light"} mode`,title:`Switch to ${"light"===d?"dark":"light"} mode`,children:"light"===d?(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,ThemeProvider:()=>o});var s=r(60687),i=r(43210);let a=(0,i.createContext)(null),n=()=>(0,i.useContext)(a),o=({children:e})=>{let[t,r]=(0,i.useState)("light"),[n,o]=(0,i.useState)(!1);(0,i.useEffect)(()=>{o(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return n?(0,s.jsx)(a.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>u});var s=r(60687),i=r(43210),a=r(19978),n=r(56304),o=r(75535),l=r(53836);let d=(0,i.createContext)(null),u=({children:e})=>{let[t,r]=(0,i.useState)(null),[u,c]=(0,i.useState)(null),[m,h]=(0,i.useState)(!0),[v,p]=(0,i.useState)(!1),[f,g]=(0,i.useState)(!1),[x,w]=(0,i.useState)(!1),b=async()=>{if(u)try{let e=await (0,o.x7)((0,o.H9)(n.db,"users",u.uid));if(e.exists()){let t={id:e.id,...e.data()},s=(0,l.gZ)(t);s&&!t.trialExpired&&(await (0,o.mZ)((0,o.H9)(n.db,"users",u.uid),{trialExpired:!0,updatedAt:new Date}),t.trialExpired=!0),r(t),p("admin"===t.role),g((0,l.nE)(t)),w(s)}}catch(e){console.error("Error refreshing user data:",e)}};(0,i.useEffect)(()=>{let e=(0,a.hg)(n.j2,async e=>{c(e),e?await b():(r(null),p(!1),g(!1),w(!1)),h(!1)});return()=>e()},[]),(0,i.useEffect)(()=>{u&&b()},[u]);let y=async(e,t)=>{await (0,a.x9)(n.j2,e,t)},C=async(e,t)=>{let r=await (0,a.eJ)(n.j2,e,t),s=(0,l.ow)(r.user.email||e);await (0,o.BN)((0,o.H9)(n.db,"users",r.user.uid),s)},P=async()=>{await (0,a.CI)(n.j2)},k=async()=>{let e=new a.HF,t=await (0,a.df)(n.j2,e);if(!(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()){let e=(0,l.ow)(t.user.email||"",{firstName:t.user.displayName?.split(" ")[0],lastName:t.user.displayName?.split(" ").slice(1).join(" ")});await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),e)}},A=async()=>{let e=new a.sk,t=await (0,a.df)(n.j2,e);if(!(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()){let e=(0,l.ow)(t.user.email||"",{firstName:t.user.displayName?.split(" ")[0],lastName:t.user.displayName?.split(" ").slice(1).join(" ")});await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),e)}};return(0,s.jsx)(d.Provider,{value:{user:t,firebaseUser:u,loading:m,signIn:y,signUp:C,logOut:P,signInWithGoogle:k,signInWithFacebook:A,isAdmin:v,canAccessPremiumFeatures:f,isTrialExpired:x,refreshUserData:b},children:e})},c=()=>{let e=(0,i.useContext)(d);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},53836:(e,t,r)=>{"use strict";function s(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function i(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||s(e)))}function a(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let t=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let t=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-t.getTime())/864e5))}(e);return t<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:t<=3?{message:`Your trial expires in ${t} day${1===t?"":"s"}. Upgrade now to continue.`,type:"warning",daysRemaining:t}:{message:`Your trial expires in ${t} days on ${!e.trialEndDate?"":new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}.`,type:"info",daysRemaining:t}}function n(e,t={}){return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,t=new Date(e);return t.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:t,trialExpired:!1}}(),...t}}r.d(t,{Mo:()=>a,gZ:()=>s,nE:()=>i,ow:()=>n})},56304:(e,t,r)=>{"use strict";r.d(t,{IG:()=>u,db:()=>d,j2:()=>l});var s=r(67989),i=r(19978),a=r(75535),n=r(70146);let o=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,i.xI)(o),d=(0,a.aU)(o),u=(0,n.c7)(o)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(37413),i=r(61421),a=r.n(i);r(82704);var n=r(94442),o=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsx)(o.ThemeProvider,{children:(0,s.jsx)(n.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},82704:()=>{},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")}};