import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import { AIService } from './services/aiService';
import { APIConfigService } from './services/apiConfigService';
import { ApiRateLimiter } from './utils/apiRateLimiter';

/**
 * Cloud Function to test API connections for AI services
 */
export const testAPIConnections = async (data: any, context: functions.https.CallableContext) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError(
        'unauthenticated',
        'Authentication required'
      );
    }

    // Check if user is admin
    const userSnapshot = await admin.firestore()
      .collection('users')
      .doc(context.auth.uid)
      .get();
    
    const userData = userSnapshot.data();
    if (!userData || userData.role !== 'admin') {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Admin access required'
      );
    }

    // Initialize AI services
    await AIService.initialize();

    // Test API connections with retry logic for rate limits
    const results = {
      claudeStatus: false,
      openaiStatus: false
    };

    // Test Claude API
    try {
      await ApiRateLimiter.executeWithRetry(
        async () => await AIService.testClaudeConnection()
      );
      results.claudeStatus = true;
    } catch (error) {
      console.error('Claude API test failed:', error);
      results.claudeStatus = false;
    }

    // Test OpenAI API
    try {
      await ApiRateLimiter.executeWithRetry(
        async () => await AIService.testOpenAIConnection()
      );
      results.openaiStatus = true;
    } catch (error) {
      console.error('OpenAI API test failed:', error);
      results.openaiStatus = false;
    }

    // Update the API configuration with test results
    try {
      await admin.firestore()
        .collection('apiConfig')
        .doc('main')
        .update({
          claudeStatus: results.claudeStatus,
          openaiStatus: results.openaiStatus,
          lastTestedAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
    } catch (error) {
      console.error('Error updating API config with test results:', error);
    }

    return results;
  } catch (error) {
    console.error('Error testing API connections:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to test API connections');
  }
};