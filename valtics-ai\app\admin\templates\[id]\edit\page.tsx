'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { doc, getDoc, updateDoc, Timestamp, addDoc, collection, onSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Template, Brand } from '@/types';
import Navigation from '@/components/Navigation';
import RichTextEditor from '@/components/RichTextEditor';
import SimpleRichTextEditor from '@/components/SimpleRichTextEditor';
import FileUpload from '@/components/FileUpload';
import { StorageService } from '@/lib/firebase/storage';

export default function EditTemplate() {
  const { user, loading, isAdmin } = useAuth();
  const router = useRouter();
  const params = useParams();
  const templateId = params.id as string;

  const [template, setTemplate] = useState<Template | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<'step1' | 'step2' | 'step3'>('step2');
  const [useSimpleEditor, setUseSimpleEditor] = useState(true); // Default to simple editor for React 19 compatibility

  const [formData, setFormData] = useState({
    enterpriseNeed: '',
    solutionDescriptionDocument: '',
    riskOfNoInvestment: '',
    enterpriseNeedFileUrl: '',
    solutionDescriptionFileUrl: '',
    riskOfNoInvestmentFileUrl: ''
  });

  // Step 3 - AI Artifact Generation state
  const [selectedLLM, setSelectedLLM] = useState<'claude' | 'openai'>('claude');
  const [companyName, setCompanyName] = useState('');
  const [aiPrompt, setAiPrompt] = useState('');
  const [generatingArtifacts, setGeneratingArtifacts] = useState(false);
  const [generatedArtifacts, setGeneratedArtifacts] = useState<any>(null);

  const [uploadingFiles, setUploadingFiles] = useState({
    enterpriseNeed: false,
    solutionDescription: false,
    riskOfNoInvestment: false
  });

  const [step1Data, setStep1Data] = useState({
    solutionProviderName: '',
    solutionName: '',
    solutionDescription: '',
    templateVersion: '',
    templateVersionDate: '',
    category: '',
    price: 0
  });

  useEffect(() => {
    if (!loading && !isAdmin) {
      router.push('/');
      return;
    }
    if (isAdmin && templateId) {
      fetchTemplateData();
    }
  }, [loading, isAdmin, router, templateId]);

  // Real-time listener for template status changes
  useEffect(() => {
    if (!templateId) return;

    const unsubscribe = onSnapshot(doc(db, 'templates', templateId), (doc) => {
      if (doc.exists()) {
        const templateData = { id: doc.id, ...doc.data() } as Template;

        // Update template state
        setTemplate(templateData);

        // Handle status changes for AI generation
        if (templateData.status === 'artifacts_generating') {
          setGeneratingArtifacts(true);
        } else if (templateData.status === 'draft' && generatingArtifacts) {
          // Generation completed successfully
          setGeneratingArtifacts(false);
          if (templateData.generatedArtifacts) {
            setGeneratedArtifacts(templateData.generatedArtifacts);
            setAiPrompt(templateData.aiPrompt || '');
            alert('AI artifacts generated successfully!');
          }
        } else if (templateData.status === 'generation-failed' && generatingArtifacts) {
          // Generation failed
          setGeneratingArtifacts(false);
          const errorMessage = (templateData as any).generationError || 'AI generation failed';
          alert(`AI generation failed: ${errorMessage}`);
        }
      }
    });

    return () => unsubscribe();
  }, [templateId, generatingArtifacts]);

  const fetchTemplateData = async () => {
    try {
      setLoadingData(true);

      // Fetch template
      const templateDoc = await getDoc(doc(db, 'templates', templateId));
      if (templateDoc.exists()) {
        const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;
        setTemplate(templateData);

        // Set form data
        setFormData({
          enterpriseNeed: templateData.enterpriseNeed || '',
          solutionDescriptionDocument: templateData.solutionDescriptionDocument || '',
          riskOfNoInvestment: templateData.riskOfNoInvestment || '',
          enterpriseNeedFileUrl: templateData.enterpriseNeedFileUrl || '',
          solutionDescriptionFileUrl: templateData.solutionDescriptionFileUrl || '',
          riskOfNoInvestmentFileUrl: templateData.riskOfNoInvestmentFileUrl || ''
        });

        setStep1Data({
          solutionProviderName: templateData.solutionProviderName || '',
          solutionName: templateData.solutionName || '',
          solutionDescription: templateData.solutionDescription || '',
          templateVersion: templateData.templateVersion || '',
          templateVersionDate: templateData.templateVersionDate ?
            (templateData.templateVersionDate instanceof Date
              ? templateData.templateVersionDate.toISOString().split('T')[0]
              : new Date((templateData.templateVersionDate as any).seconds * 1000).toISOString().split('T')[0]
            ) : '',
          category: templateData.category || '',
          price: templateData.price || 0
        });

        // Set Step 3 data
        setSelectedLLM(templateData.selectedLLM || 'claude');
        setAiPrompt(templateData.aiPrompt || '');
        setGeneratedArtifacts(templateData.generatedArtifacts || null);
        setGeneratingArtifacts(templateData.status === 'artifacts_generating');

        // Fetch brand
        const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));
        if (brandDoc.exists()) {
          setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);
        }
      } else {
        router.push('/admin');
      }
    } catch (error) {
      console.error('Error fetching template data:', error);
      router.push('/admin');
    } finally {
      setLoadingData(false);
    }
  };

  const handleStep1Update = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        solutionProviderName: step1Data.solutionProviderName,
        solutionName: step1Data.solutionName,
        solutionDescription: step1Data.solutionDescription,
        templateVersion: step1Data.templateVersion,
        templateVersionDate: Timestamp.fromDate(new Date(step1Data.templateVersionDate)),
        category: step1Data.category,
        price: step1Data.price,
        name: step1Data.solutionName, // Update template name
        description: step1Data.solutionDescription, // Update template description
        updatedAt: Timestamp.now()
      });

      alert('Step 1 information updated successfully!');
      await fetchTemplateData(); // Refresh data

    } catch (error) {
      console.error('Error updating template:', error);
      alert('Error updating template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleFileUpload = async (
    file: File,
    documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment'
  ) => {
    if (!template) return;

    try {
      setUploadingFiles(prev => ({ ...prev, [documentType]: true }));

      const fileUrl = await StorageService.uploadTemplateDocument(file, templateId, documentType);

      // Update form data
      const fieldName = `${documentType}FileUrl` as keyof typeof formData;
      setFormData(prev => ({ ...prev, [fieldName]: fileUrl }));

      // Update database immediately
      await updateDoc(doc(db, 'templates', templateId), {
        [`${documentType}FileUrl`]: fileUrl,
        updatedAt: Timestamp.now()
      });

      alert('File uploaded successfully!');

    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Error uploading file. Please try again.');
    } finally {
      setUploadingFiles(prev => ({ ...prev, [documentType]: false }));
    }
  };

  const handleFileRemove = async (documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment') => {
    if (!template) return;

    try {
      const fieldName = `${documentType}FileUrl` as keyof typeof formData;
      const currentUrl = formData[fieldName];

      if (currentUrl) {
        // Delete from storage
        await StorageService.deleteFile(currentUrl);
      }

      // Update form data
      setFormData(prev => ({ ...prev, [fieldName]: '' }));

      // Update database
      await updateDoc(doc(db, 'templates', templateId), {
        [`${documentType}FileUrl`]: '',
        updatedAt: Timestamp.now()
      });

      alert('File removed successfully!');

    } catch (error) {
      console.error('Error removing file:', error);
      alert('Error removing file. Please try again.');
    }
  };

  const handleStep2Submit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    // Validate that all files are uploaded
    if (!formData.enterpriseNeedFileUrl || !formData.solutionDescriptionFileUrl || !formData.riskOfNoInvestmentFileUrl) {
      alert('Please upload all three document files before proceeding.');
      return;
    }

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        enterpriseNeedFileUrl: formData.enterpriseNeedFileUrl,
        solutionDescriptionFileUrl: formData.solutionDescriptionFileUrl,
        riskOfNoInvestmentFileUrl: formData.riskOfNoInvestmentFileUrl,
        step2Completed: true,
        updatedAt: Timestamp.now()
      });

      alert('Documents uploaded successfully! You can now proceed to Step 3 to generate AI artifacts.');
      await fetchTemplateData(); // Refresh data

    } catch (error) {
      console.error('Error updating template:', error);
      alert('Error updating template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handlePublishTemplate = async () => {
    if (!template) return;

    if (!template.step1Completed || !template.step2Completed || !template.step3Completed) {
      alert('Please complete all three steps before publishing.');
      return;
    }

    if (!formData.enterpriseNeedFileUrl || !formData.solutionDescriptionFileUrl || !formData.riskOfNoInvestmentFileUrl) {
      alert('Please upload all three document files before publishing.');
      return;
    }

    if (!generatedArtifacts) {
      alert('Please generate AI artifacts in Step 3 before publishing.');
      return;
    }

    const confirmPublish = window.confirm(
      'Are you sure you want to publish this template? Once published, it cannot be edited. You can only create new versions from published templates.'
    );

    if (!confirmPublish) return;

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        status: 'published',
        isActive: true,
        publishedAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      alert('Template published successfully! It can no longer be edited.');
      await fetchTemplateData(); // Refresh to show published state

    } catch (error) {
      console.error('Error publishing template:', error);
      alert('Error publishing template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCreateNewVersion = async () => {
    if (!template || !brand) return;

    const confirmCreate = window.confirm(
      'This will create a new draft version of this template that you can edit. Continue?'
    );

    if (!confirmCreate) return;

    try {
      setSubmitting(true);

      // Create new template with incremented version
      const currentVersion = template.templateVersion || '1.0';
      const versionParts = currentVersion.split('.');
      const majorVersion = parseInt(versionParts[0]) || 1;
      const minorVersion = parseInt(versionParts[1]) || 0;
      const newVersion = `${majorVersion}.${minorVersion + 1}`;

      const newTemplateData = {
        name: template.name,
        description: template.description,
        brandId: template.brandId,
        category: template.category,
        price: template.price,
        solutionProviderName: template.solutionProviderName,
        solutionName: template.solutionName,
        solutionDescription: template.solutionDescription,
        templateVersion: newVersion,
        templateVersionDate: Timestamp.now(),
        status: 'draft' as const,
        step1Completed: true,
        step2Completed: false,
        step3Completed: false,
        isActive: false,
        fileUrls: {},
        // Copy document content but not file URLs (they can upload new files)
        enterpriseNeed: template.enterpriseNeed || '',
        solutionDescriptionDocument: template.solutionDescriptionDocument || '',
        riskOfNoInvestment: template.riskOfNoInvestment || '',
        // Don't copy file URLs - let them upload new files
        enterpriseNeedFileUrl: '',
        solutionDescriptionFileUrl: '',
        riskOfNoInvestmentFileUrl: '',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        // Reference to original template
        basedOnTemplateId: templateId,
        basedOnVersion: template.templateVersion
      };

      const docRef = await addDoc(collection(db, 'templates'), newTemplateData);

      alert(`New version ${newVersion} created successfully!`);
      router.push(`/admin/templates/${docRef.id}/edit`);

    } catch (error) {
      console.error('Error creating new version:', error);
      alert('Error creating new version. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSaveDraft = async () => {
    if (!template) return;

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        enterpriseNeed: formData.enterpriseNeed,
        solutionDescriptionDocument: formData.solutionDescriptionDocument,
        riskOfNoInvestment: formData.riskOfNoInvestment,
        enterpriseNeedFileUrl: formData.enterpriseNeedFileUrl,
        solutionDescriptionFileUrl: formData.solutionDescriptionFileUrl,
        riskOfNoInvestmentFileUrl: formData.riskOfNoInvestmentFileUrl,
        status: 'draft',
        updatedAt: Timestamp.now()
      });

      alert('Template saved as draft successfully!');

    } catch (error) {
      console.error('Error saving template:', error);
      alert('Error saving template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleGenerateArtifacts = async () => {
    if (!template) return;

    if (!selectedLLM) {
      alert('Please select an AI model.');
      return;
    }

    // Validate that all files are uploaded
    if (!formData.enterpriseNeedFileUrl || !formData.solutionDescriptionFileUrl || !formData.riskOfNoInvestmentFileUrl) {
      alert('Please upload all three document files before generating AI artifacts.');
      return;
    }

    try {
      setGeneratingArtifacts(true);

      // Update template status to trigger Firebase Function
      await updateDoc(doc(db, 'templates', templateId), {
        status: 'artifacts_generating',
        selectedLLM,
        companyName: companyName || brand?.name || 'Company Name',
        updatedAt: Timestamp.now()
      });

      console.log('AI artifact generation started via Firebase Function');

    } catch (error) {
      console.error('Error starting AI generation:', error);
      alert('Error starting AI generation. Please try again.');
      setGeneratingArtifacts(false);
    }
  };



  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!isAdmin || !template) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />

      <div className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {template.status === 'published' ? 'View Template' : 'Edit Template'}
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-300">
                  {template.solutionName} - {brand?.name}
                </p>
                <div className="mt-2 flex items-center space-x-4">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    template.status === 'published'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {template.status === 'published' ? 'Published' : 'Draft'}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Version {template.templateVersion}
                  </span>
                  {template.basedOnTemplateId && (
                    <span className="text-xs text-blue-600 dark:text-blue-400">
                      Based on v{template.basedOnVersion}
                    </span>
                  )}
                </div>
                {template.status === 'published' && (
                  <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Note:</strong> This template is published and cannot be edited. You can create a new version to make changes.
                    </p>
                  </div>
                )}
              </div>
              <div className="flex space-x-3">
                {template.status === 'published' && (
                  <button
                    onClick={handleCreateNewVersion}
                    disabled={submitting}
                    className="bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {submitting && (
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    <span>{submitting ? 'Creating...' : 'Create New Version'}</span>
                  </button>
                )}
                <Link
                  href="/admin"
                  className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  Back to Admin
                </Link>
              </div>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center">
              <div className={`flex items-center ${template.step1Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
                <div className={`flex items-center justify-center w-8 h-8 ${template.step1Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>
                  {template.step1Completed ? '✓' : '1'}
                </div>
                <span className="ml-2 text-sm font-medium">Basic Information</span>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"></div>
              <div className={`flex items-center ${template.step2Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
                <div className={`flex items-center justify-center w-8 h-8 ${template.step2Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>
                  {template.step2Completed ? '✓' : '2'}
                </div>
                <span className="ml-2 text-sm font-medium">Documents</span>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"></div>
              <div className={`flex items-center ${template.step3Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
                <div className={`flex items-center justify-center w-8 h-8 ${template.step3Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>
                  {template.step3Completed ? '✓' : '3'}
                </div>
                <span className="ml-2 text-sm font-medium">AI Artifacts</span>
              </div>
            </div>
          </div>

          {/* Step Navigation */}
          <div className="mb-6">
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
              <button
                onClick={() => setCurrentStep('step1')}
                disabled={template.status === 'published'}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentStep === 'step1'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                } ${template.status === 'published' ? 'cursor-not-allowed opacity-60' : ''}`}
              >
                {template.status === 'published' ? 'View Basic Information' : 'Edit Basic Information'}
              </button>
              <button
                onClick={() => setCurrentStep('step2')}
                disabled={template.status === 'published'}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentStep === 'step2'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                } ${template.status === 'published' ? 'cursor-not-allowed opacity-60' : ''}`}
              >
                {template.status === 'published' ? 'View Documents' : 'Edit Documents'}
              </button>
              <button
                onClick={() => setCurrentStep('step3')}
                disabled={template.status === 'published'}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentStep === 'step3'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                } ${template.status === 'published' ? 'cursor-not-allowed opacity-60' : ''}`}
              >
                {template.status === 'published' ? 'View AI Artifacts' : 'Generate AI Artifacts'}
              </button>
            </div>
          </div>

          {/* Step 1 Content */}
          {currentStep === 'step1' && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <form onSubmit={handleStep1Update} className="p-6 space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {template.status === 'published' ? 'Basic Information (Read Only)' : 'Basic Information'}
                </h3>

                {/* Solution Provider Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Solution Provider Name *
                  </label>
                  <input
                    type="text"
                    value={step1Data.solutionProviderName}
                    onChange={(e) => setStep1Data(prev => ({ ...prev, solutionProviderName: e.target.value }))}
                    disabled={template.status === 'published'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
                    required
                  />
                </div>

                {/* Solution Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Solution Name *
                  </label>
                  <input
                    type="text"
                    value={step1Data.solutionName}
                    onChange={(e) => setStep1Data(prev => ({ ...prev, solutionName: e.target.value }))}
                    disabled={template.status === 'published'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
                    required
                  />
                </div>

                {/* Solution Description */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Solution Description *
                    </label>
                    <button
                      type="button"
                      onClick={() => setUseSimpleEditor(!useSimpleEditor)}
                      className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}
                    </button>
                  </div>
                  {useSimpleEditor ? (
                    <SimpleRichTextEditor
                      value={step1Data.solutionDescription}
                      onChange={(value) => setStep1Data(prev => ({ ...prev, solutionDescription: value }))}
                      placeholder="Enter detailed solution description..."
                      className="border border-gray-300 dark:border-gray-600 rounded-md"
                      disabled={template.status === 'published'}
                    />
                  ) : (
                    <RichTextEditor
                      value={step1Data.solutionDescription}
                      onChange={(value) => setStep1Data(prev => ({ ...prev, solutionDescription: value }))}
                      placeholder="Enter detailed solution description..."
                      className="border border-gray-300 dark:border-gray-600 rounded-md"
                      disabled={template.status === 'published'}
                    />
                  )}
                </div>

                {/* Template Version and Date */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Template Version *
                    </label>
                    <input
                      type="text"
                      value={step1Data.templateVersion}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, templateVersion: e.target.value }))}
                      disabled={template.status === 'published'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Template Version Date *
                    </label>
                    <input
                      type="date"
                      value={step1Data.templateVersionDate}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, templateVersionDate: e.target.value }))}
                      disabled={template.status === 'published'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
                      required
                    />
                  </div>
                </div>

                {/* Category and Price */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Category
                    </label>
                    <select
                      value={step1Data.category}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, category: e.target.value }))}
                      disabled={template.status === 'published'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
                    >
                      <option value="Business Analysis">Business Analysis</option>
                      <option value="Financial Planning">Financial Planning</option>
                      <option value="Technology Assessment">Technology Assessment</option>
                      <option value="Risk Management">Risk Management</option>
                      <option value="Strategic Planning">Strategic Planning</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Price ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={step1Data.price}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                      disabled={template.status === 'published'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
                    />
                  </div>
                </div>

                {/* Submit Button */}
                {template.status !== 'published' && (
                  <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button
                      type="submit"
                      disabled={submitting}
                      className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {submitting ? 'Updating...' : 'Update Basic Information'}
                    </button>
                  </div>
                )}
              </form>
            </div>
          )}

          {/* Step 2 Content */}
          {currentStep === 'step2' && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <form onSubmit={handleStep2Submit} className="p-6 space-y-8">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {template.status === 'published' ? 'View Documents (Read Only)' : 'Upload Documents'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                    {template.status === 'published'
                      ? 'View the three essential documents for this Business Value Analysis template.'
                      : 'Upload the three essential documents for your Business Value Analysis template. These files will be used to generate AI artifacts in Step 3.'
                    }
                  </p>
                </div>

                {/* Enterprise Need */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Enterprise Need Document *
                  </label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    Upload a document that describes the business need or challenge that this solution addresses.
                  </p>

                  <FileUpload
                    onFileSelect={(file) => handleFileUpload(file, 'enterpriseNeed')}
                    onFileRemove={() => handleFileRemove('enterpriseNeed')}
                    currentFileUrl={formData.enterpriseNeedFileUrl}
                    currentFileName={formData.enterpriseNeedFileUrl ? StorageService.getFileNameFromUrl(formData.enterpriseNeedFileUrl) : undefined}
                    disabled={uploadingFiles.enterpriseNeed || template.status === 'published'}
                    className="mt-2"
                  />
                </div>

                {/* Solution Description Document */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Solution Description Document *
                  </label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    Upload a document that provides a comprehensive description of how your solution works and its key features.
                  </p>

                  <FileUpload
                    onFileSelect={(file) => handleFileUpload(file, 'solutionDescription')}
                    onFileRemove={() => handleFileRemove('solutionDescription')}
                    currentFileUrl={formData.solutionDescriptionFileUrl}
                    currentFileName={formData.solutionDescriptionFileUrl ? StorageService.getFileNameFromUrl(formData.solutionDescriptionFileUrl) : undefined}
                    disabled={uploadingFiles.solutionDescription || template.status === 'published'}
                    className="mt-2"
                  />
                </div>

                {/* Risk of No Investment */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Risk of No Investment Document *
                  </label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    Upload a document that explains the potential risks and consequences of not implementing this solution.
                  </p>

                  <FileUpload
                    onFileSelect={(file) => handleFileUpload(file, 'riskOfNoInvestment')}
                    onFileRemove={() => handleFileRemove('riskOfNoInvestment')}
                    currentFileUrl={formData.riskOfNoInvestmentFileUrl}
                    currentFileName={formData.riskOfNoInvestmentFileUrl ? StorageService.getFileNameFromUrl(formData.riskOfNoInvestmentFileUrl) : undefined}
                    disabled={uploadingFiles.riskOfNoInvestment || template.status === 'published'}
                    className="mt-2"
                  />
                </div>

                {/* Action Buttons */}
                {template.status !== 'published' && (
                  <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button
                      type="submit"
                      disabled={submitting}
                      className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {submitting && (
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      )}
                      <span>{submitting ? 'Saving...' : 'Save & Continue to Step 3'}</span>
                    </button>
                  </div>
                )}
              </form>
            </div>
          )}

          {/* Step 3 Content - AI Artifact Generation */}
          {currentStep === 'step3' && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="p-6 space-y-8">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {template.status === 'published' ? 'View AI Artifacts (Read Only)' : 'Generate AI Artifacts'}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                    {template.status === 'published'
                      ? 'View the AI-generated artifacts for this Business Value Analysis template.'
                      : 'Generate AI artifacts based on the uploaded documents. These artifacts will be used in the final Business Value Analysis template.'
                    }
                  </p>
                </div>

                {/* Prerequisites Check */}
                {(!formData.enterpriseNeedFileUrl || !formData.solutionDescriptionFileUrl || !formData.riskOfNoInvestmentFileUrl) && (
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                          Prerequisites Required
                        </h3>
                        <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                          <p>Please complete Step 2 by uploading all three document files before generating AI artifacts.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* LLM Selection */}
                {template.status !== 'published' && formData.enterpriseNeedFileUrl && formData.solutionDescriptionFileUrl && formData.riskOfNoInvestmentFileUrl && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Select AI Model *
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <div
                        className={`relative rounded-lg border p-4 cursor-pointer transition-colors ${
                          selectedLLM === 'claude'
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                        }`}
                        onClick={() => setSelectedLLM('claude')}
                      >
                        <div className="flex items-center">
                          <input
                            type="radio"
                            name="llm"
                            value="claude"
                            checked={selectedLLM === 'claude'}
                            onChange={() => setSelectedLLM('claude')}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <div className="ml-3">
                            <label className="block text-sm font-medium text-gray-900 dark:text-white">
                              Claude AI API
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Anthropic's Claude for advanced reasoning
                            </p>
                          </div>
                        </div>
                      </div>
                      <div
                        className={`relative rounded-lg border p-4 cursor-pointer transition-colors ${
                          selectedLLM === 'openai'
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                        }`}
                        onClick={() => setSelectedLLM('openai')}
                      >
                        <div className="flex items-center">
                          <input
                            type="radio"
                            name="llm"
                            value="openai"
                            checked={selectedLLM === 'openai'}
                            onChange={() => setSelectedLLM('openai')}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <div className="ml-3">
                            <label className="block text-sm font-medium text-gray-900 dark:text-white">
                              OpenAI API
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              OpenAI's GPT for creative content generation
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Company Name Input */}
                {template.status !== 'published' && formData.enterpriseNeedFileUrl && formData.solutionDescriptionFileUrl && formData.riskOfNoInvestmentFileUrl && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Company Name (Optional)
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                      Enter the company name to be used in the artifacts. If left blank, the brand name will be used.
                    </p>
                    <input
                      type="text"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      placeholder={`Default: ${brand?.name || 'Company Name'}`}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                )}

                {/* AI Prompt Display */}
                {aiPrompt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      AI Generation Prompt
                    </label>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                      This is the exact prompt that will be sent to the selected AI model.
                    </p>
                    <textarea
                      value={aiPrompt}
                      readOnly
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                    />
                  </div>
                )}

                {/* Generate Button */}
                {template.status !== 'published' && formData.enterpriseNeedFileUrl && formData.solutionDescriptionFileUrl && formData.riskOfNoInvestmentFileUrl && (
                  <div className="flex justify-center">
                    <button
                      type="button"
                      onClick={handleGenerateArtifacts}
                      disabled={generatingArtifacts}
                      className="bg-purple-600 dark:bg-purple-700 text-white px-8 py-3 rounded-md font-medium hover:bg-purple-700 dark:hover:bg-purple-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {generatingArtifacts && (
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      )}
                      <span>{generatingArtifacts ? 'Generating AI Artifacts...' : 'Generate AI Artifacts'}</span>
                    </button>
                  </div>
                )}

                {/* Processing Status */}
                {(generatingArtifacts || template.status === 'artifacts_generating') && (
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <div className="flex items-center">
                      <svg className="animate-spin h-5 w-5 text-blue-600 dark:text-blue-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <div>
                        <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          AI Artifacts Being Generated
                        </h3>
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          Please wait while the AI processes your documents and generates the artifacts. This may take a few minutes.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Generation Failed Status */}
                {template.status === 'generation-failed' && (
                  <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                          AI Generation Failed
                        </h3>
                        <p className="text-sm text-red-700 dark:text-red-300">
                          {(template as any).generationError || 'An error occurred while generating AI artifacts. Please try again.'}
                        </p>
                        <div className="mt-2">
                          <button
                            onClick={() => {
                              // Reset status to draft so user can try again
                              updateDoc(doc(db, 'templates', templateId), {
                                status: 'draft',
                                updatedAt: Timestamp.now()
                              });
                            }}
                            className="text-sm text-red-800 dark:text-red-200 underline hover:no-underline"
                          >
                            Try Again
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Generated Artifacts Display */}
                {generatedArtifacts && (
                  <div className="space-y-6">
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                            AI Artifacts Generated Successfully
                          </h3>
                          <p className="text-sm text-green-700 dark:text-green-300">
                            The AI has successfully generated three artifacts based on your uploaded documents.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Enterprise Need Artifact */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                        Enterprise Need Artifact
                      </h4>
                      <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono">
                          {generatedArtifacts.enterpriseNeedArtifact}
                        </pre>
                      </div>
                    </div>

                    {/* Solution Artifact */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                        Proposed Solution Artifact
                      </h4>
                      <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono">
                          {generatedArtifacts.solutionArtifact}
                        </pre>
                      </div>
                    </div>

                    {/* Risk Artifact */}
                    <div>
                      <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                        Risk of No Investment Artifact
                      </h4>
                      <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono">
                          {generatedArtifacts.riskArtifact}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}

                {/* Publish Template Button */}
                {template.status !== 'published' && template.step3Completed && generatedArtifacts && (
                  <div className="flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700">
                    <button
                      type="button"
                      onClick={handlePublishTemplate}
                      disabled={submitting}
                      className="bg-green-600 dark:bg-green-700 text-white px-8 py-3 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {submitting && (
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      )}
                      <span>{submitting ? 'Publishing...' : 'Publish Template'}</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
