(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5481:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var s=t(60687),a=t(85814),l=t.n(a),d=t(30474),i=t(51108),n=t(16189),o=t(27436),c=t(31769);function x({title:e="VALTICS AI",showBackButton:r=!1,backUrl:t="/dashboard",backText:a="← Back to Dashboard"}){let{user:x,logOut:u,isAdmin:m}=(0,i.A)(),h=(0,n.useRouter)(),g=async()=>{try{await u(),h.push("/")}catch(e){console.error("Error logging out:",e)}};return x?(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)(l(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,s.jsx)(d.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,s.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[r&&(0,s.jsx)(l(),{href:t,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:a}),!r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,s.jsx)(l(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),m&&(0,s.jsx)(l(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,s.jsx)(l(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,s.jsx)(c.I,{}),(0,s.jsx)(o.default,{}),(0,s.jsx)("button",{onClick:g,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,r,t)=>{"use strict";t.d(r,{default:()=>a.a});var s=t(31261),a=t.n(s)},31261:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return n},getImageProps:function(){return i}});let s=t(37366),a=t(44953),l=t(46533),d=s._(t(1933));function i(e){let{props:r}=(0,a.getImgProps)(e,{defaultLoader:d.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let n=l.Image},31769:(e,r,t)=>{"use strict";t.d(r,{A:()=>o,I:()=>c});var s=t(60687),a=t(43210),l=t(85814),d=t.n(l),i=t(51108),n=t(53836);function o(){let{user:e}=(0,i.A)(),[r,t]=(0,a.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||r)return null;let{message:l,type:o,daysRemaining:c}=(0,n.Mo)(e);if(!l)return null;let x=()=>{switch(o){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,s.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(o){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:"error"===o?(0,s.jsx)("svg",{className:`h-5 w-5 ${x()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===o?(0,s.jsx)("svg",{className:`h-5 w-5 ${x()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{className:`h-5 w-5 ${x()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:l})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(d(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(o){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,s.jsx)("button",{onClick:()=>t(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,s.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function c(){let{user:e}=(0,i.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:r}=(0,n.Mo)(e);return r<=0?(0,s.jsx)(d(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,s.jsxs)(d(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${r<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[r," day",1===r?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},46836:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(60687),a=t(43210),l=t(51108),d=t(16189),i=t(85814),n=t.n(i);t(75535),t(56304);var o=t(5481),c=t(31769);function x(){let{user:e,firebaseUser:r,loading:t,logOut:i,isAdmin:x}=(0,l.A)();(0,d.useRouter)();let[u,m]=(0,a.useState)([]),[h,g]=(0,a.useState)([]),[p,b]=(0,a.useState)([]),[v,f]=(0,a.useState)(!0);if(t||v)return(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})});if(!e)return null;let y=e=>{switch(e){case"completed":return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";case"in-progress":return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,s.jsx)(o.A,{title:"VALTICS AI"}),(0,s.jsx)(c.A,{}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,s.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:["Welcome back, ",e.email,"!"]}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Ready to create powerful Business Value Analysis reports?"}),(0,s.jsx)(n(),{href:"/bva/new",className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block",children:"Create New BVA"})]})})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,s.jsx)("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:"BVA"})})}),(0,s.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total BVAs"}),(0,s.jsx)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:u.length})]})})]})})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,s.jsx)("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-green-500 dark:bg-green-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:"✓"})})}),(0,s.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Completed"}),(0,s.jsx)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:u.filter(e=>"completed"===e.status).length})]})})]})})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,s.jsx)("div",{className:"p-5",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-yellow-500 dark:bg-yellow-600 rounded-md flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:"⏳"})})}),(0,s.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,s.jsxs)("dl",{children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"In Progress"}),(0,s.jsx)("dd",{className:"text-lg font-medium text-gray-900 dark:text-white",children:u.filter(e=>"in-progress"===e.status).length})]})})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,s.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,s.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:"Recent BVAs"}),u.length>0?(0,s.jsx)("div",{className:"space-y-3",children:u.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.clientName})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${y(e.status)}`,children:e.status}),(0,s.jsx)(n(),{href:`/bva/${e.id}`,className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View"})]})]},e.id))}):(0,s.jsxs)("p",{className:"text-gray-500 dark:text-gray-400 text-center py-4",children:["No BVAs created yet. ",(0,s.jsx)(n(),{href:"/bva/new",className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300",children:"Create your first one!"})]})]})}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,s.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,s.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:"Available Templates"}),h.length>0?(0,s.jsx)("div",{className:"space-y-3",children:h.slice(0,4).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.category})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:["$",e.price]}),(0,s.jsx)(n(),{href:`/templates/${e.id}`,className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"Use"})]})]},e.id))}):(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-center py-4",children:"No templates available"}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(n(),{href:"/templates",className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View all templates →"})})]})})]})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},58848:(e,r,t)=>{Promise.resolve().then(t.bind(t,64118))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\dashboard\\page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85480:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=t(65239),a=t(48088),l=t(88170),d=t.n(l),i=t(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let o={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64118)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},88224:(e,r,t)=>{Promise.resolve().then(t.bind(t,46836))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,823,567,533,77],()=>t(85480));module.exports=s})();