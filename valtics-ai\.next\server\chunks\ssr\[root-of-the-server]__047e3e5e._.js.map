{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,sCAAsC;QACtC,IAAI,CAAC,cAAc;YACjB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,8OAAC,0HAAA,CAAA,iBAAc;;;;;0CAGf,8OAAC,0HAAA,CAAA,UAAW;;;;;0CAEZ,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/services/aiService.ts"], "sourcesContent": ["import Anthropic from '@anthropic-ai/sdk';\nimport OpenAI from 'openai';\nimport { DocumentFile } from '../utils/fileProcessor';\n\nexport interface AIResponse {\n  enterpriseNeedArtifact: string;\n  solutionArtifact: string;\n  riskArtifact: string;\n}\n\nexport interface DocumentContext {\n  enterpriseNeed: string;\n  solution: string;\n  risk: string;\n}\n\nexport interface DocumentContextWithFiles {\n  enterpriseNeedFile: DocumentFile;\n  solutionFile: DocumentFile;\n  riskFile: DocumentFile;\n}\n\nexport class AIService {\n  private static claudeClient: Anthropic | null = null;\n  private static openaiClient: OpenAI | null = null;\n\n  /**\n   * Initialize Claude client\n   */\n  static initializeClaude(apiKey: string): void {\n    this.claudeClient = new Anthropic({\n      apiKey: apiKey,\n    });\n  }\n\n  /**\n   * Initialize OpenAI client\n   */\n  static initializeOpenAI(apiKey: string): void {\n    this.openaiClient = new OpenAI({\n      apiKey: apiKey,\n    });\n  }\n\n  /**\n   * Test Claude API connection\n   */\n  static async testClaudeConnection(apiKey: string): Promise<boolean> {\n    try {\n      const client = new Anthropic({ apiKey });\n      \n      const response = await client.messages.create({\n        model: 'claude-3-haiku-20240307',\n        max_tokens: 10,\n        messages: [\n          {\n            role: 'user',\n            content: 'Test connection. Respond with \"OK\".'\n          }\n        ]\n      });\n\n      return response.content[0].type === 'text' && response.content[0].text.includes('OK');\n    } catch (error) {\n      console.error('Claude connection test failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Test OpenAI API connection\n   */\n  static async testOpenAIConnection(apiKey: string): Promise<boolean> {\n    try {\n      const client = new OpenAI({ apiKey });\n      \n      const response = await client.chat.completions.create({\n        model: 'gpt-3.5-turbo',\n        max_tokens: 10,\n        messages: [\n          {\n            role: 'user',\n            content: 'Test connection. Respond with \"OK\".'\n          }\n        ]\n      });\n\n      return response.choices[0]?.message?.content?.includes('OK') || false;\n    } catch (error) {\n      console.error('OpenAI connection test failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Generate artifacts using Claude\n   */\n  static async generateWithClaude(\n    prompt: string,\n    documentContext: DocumentContext,\n    solutionName: string,\n    companyName: string\n  ): Promise<AIResponse> {\n    if (!this.claudeClient) {\n      throw new Error('Claude client not initialized');\n    }\n\n    try {\n      const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);\n\n      const response = await this.claudeClient.messages.create({\n        model: 'claude-3-sonnet-20240229',\n        max_tokens: 4000,\n        temperature: 0.7,\n        messages: [\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n\n      if (response.content[0].type !== 'text') {\n        throw new Error('Unexpected response format from Claude');\n      }\n\n      return this.parseAIResponse(response.content[0].text);\n    } catch (error) {\n      console.error('Claude generation failed:', error);\n      throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Generate artifacts using OpenAI\n   */\n  static async generateWithOpenAI(\n    prompt: string,\n    documentContext: DocumentContext,\n    solutionName: string,\n    companyName: string\n  ): Promise<AIResponse> {\n    if (!this.openaiClient) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    try {\n      const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);\n\n      const response = await this.openaiClient.chat.completions.create({\n        model: 'gpt-4',\n        max_tokens: 4000,\n        temperature: 0.7,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert business analyst creating professional Business Value Assessment artifacts.'\n          },\n          {\n            role: 'user',\n            content: fullPrompt\n          }\n        ]\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        throw new Error('No content received from OpenAI');\n      }\n\n      return this.parseAIResponse(content);\n    } catch (error) {\n      console.error('OpenAI generation failed:', error);\n      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Build the full prompt with document context\n   */\n  private static buildFullPrompt(\n    basePrompt: string,\n    documentContext: DocumentContext,\n    solutionName: string,\n    companyName: string\n  ): string {\n    return `${basePrompt}\n\nDOCUMENT CONTENTS:\n\nEnterprise Need Document:\n${documentContext.enterpriseNeed}\n\nSolution Description Document:\n${documentContext.solution}\n\nRisk of No Investment Document:\n${documentContext.risk}\n\nPlease generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).`;\n  }\n\n  /**\n   * Parse AI response into structured artifacts\n   */\n  private static parseAIResponse(response: string): AIResponse {\n    // Try to split the response into three artifacts\n    const sections = response.split(/(?=#{1,3}\\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i);\n    \n    let enterpriseNeedArtifact = '';\n    let solutionArtifact = '';\n    let riskArtifact = '';\n\n    for (const section of sections) {\n      const trimmedSection = section.trim();\n      if (!trimmedSection) continue;\n\n      if (trimmedSection.toLowerCase().includes('enterprise need')) {\n        enterpriseNeedArtifact = trimmedSection;\n      } else if (trimmedSection.toLowerCase().includes('proposed solution')) {\n        solutionArtifact = trimmedSection;\n      } else if (trimmedSection.toLowerCase().includes('risk of no investment')) {\n        riskArtifact = trimmedSection;\n      }\n    }\n\n    // If parsing failed, try alternative approach\n    if (!enterpriseNeedArtifact || !solutionArtifact || !riskArtifact) {\n      const parts = response.split(/\\n\\s*\\n/);\n      if (parts.length >= 3) {\n        enterpriseNeedArtifact = parts[0] || 'Enterprise Need artifact could not be parsed.';\n        solutionArtifact = parts[1] || 'Solution artifact could not be parsed.';\n        riskArtifact = parts[2] || 'Risk artifact could not be parsed.';\n      } else {\n        // Fallback: use the entire response for each artifact with appropriate headers\n        const fallbackContent = response;\n        enterpriseNeedArtifact = `# Enterprise Need Artifact\\n\\n${fallbackContent}`;\n        solutionArtifact = `# Proposed Solution Artifact\\n\\n${fallbackContent}`;\n        riskArtifact = `# Risk of No Investment Artifact\\n\\n${fallbackContent}`;\n      }\n    }\n\n    return {\n      enterpriseNeedArtifact: enterpriseNeedArtifact.trim(),\n      solutionArtifact: solutionArtifact.trim(),\n      riskArtifact: riskArtifact.trim()\n    };\n  }\n\n  /**\n   * Generate artifacts using Claude with direct file attachments\n   */\n  static async generateWithClaudeFiles(\n    prompt: string,\n    documentFiles: DocumentContextWithFiles,\n    solutionName: string,\n    companyName: string\n  ): Promise<AIResponse> {\n    if (!this.claudeClient) {\n      throw new Error('Claude client not initialized');\n    }\n\n    try {\n      // Build content array with direct file attachments\n      const content: any[] = [];\n\n      // Helper function to add document content\n      const addDocumentContent = (file: DocumentFile, documentName: string) => {\n        const fileType = file.fileType.toLowerCase();\n\n        if (fileType === 'pdf') {\n          // Use document attachment for PDF files\n          content.push({\n            type: 'document',\n            source: {\n              type: 'base64',\n              media_type: 'application/pdf',\n              data: file.base64Data\n            }\n          });\n          content.push({\n            type: 'text',\n            text: `The above document contains the ${documentName} information.`\n          });\n        }\n        else if (['png', 'jpg', 'jpeg'].includes(fileType)) {\n          // Use image attachment for image files\n          const mimeType = fileType === 'jpg' ? 'image/jpeg' : `image/${fileType}`;\n          content.push({\n            type: 'image',\n            source: {\n              type: 'base64',\n              media_type: mimeType,\n              data: file.base64Data\n            }\n          });\n          content.push({\n            type: 'text',\n            text: `The above image contains the ${documentName} information.`\n          });\n        }\n        else {\n          // For other file types, use document attachment with appropriate MIME type\n          content.push({\n            type: 'document',\n            source: {\n              type: 'base64',\n              media_type: this.getMimeType(fileType),\n              data: file.base64Data\n            }\n          });\n          content.push({\n            type: 'text',\n            text: `The above document contains the ${documentName} information.`\n          });\n        }\n      };\n\n      // Add each document\n      addDocumentContent(documentFiles.enterpriseNeedFile, 'Enterprise Need');\n      addDocumentContent(documentFiles.solutionFile, 'Solution Description');\n      addDocumentContent(documentFiles.riskFile, 'Risk of No Investment');\n\n      // Add the text prompt\n      content.push({\n        type: 'text',\n        text: `${prompt}\n\nPlease analyze the three attached documents and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).\n\nThe first document contains the Enterprise Need information.\nThe second document contains the Solution Description information.\nThe third document contains the Risk of No Investment information.\n\nUse only the content from these attached documents and do not add any other information other than the items requested in this prompt.`\n      });\n\n      const response = await this.claudeClient.messages.create({\n        model: 'claude-3-5-sonnet-20241022',\n        max_tokens: 4000,\n        temperature: 0.7,\n        messages: [\n          {\n            role: 'user',\n            content\n          }\n        ]\n      });\n\n      if (response.content[0].type !== 'text') {\n        throw new Error('Unexpected response format from Claude');\n      }\n\n      return this.parseAIResponse(response.content[0].text);\n    } catch (error) {\n      console.error('Claude file generation failed:', error);\n      throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Generate artifacts using OpenAI with file attachments\n   * Uses GPT-4 Vision for images and includes file content for other types\n   */\n  static async generateWithOpenAIFiles(\n    prompt: string,\n    documentFiles: DocumentContextWithFiles,\n    solutionName: string,\n    companyName: string\n  ): Promise<AIResponse> {\n    if (!this.openaiClient) {\n      throw new Error('OpenAI client not initialized');\n    }\n\n    try {\n      // Build content array for OpenAI\n      const content: any[] = [];\n\n      // Add the main prompt first\n      content.push({\n        type: 'text',\n        text: `${prompt}\n\nI have three documents that I need you to analyze. Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).\n\nDocuments to analyze:`\n      });\n\n      // Helper function to add document content for OpenAI\n      const addDocumentContentOpenAI = (file: DocumentFile, documentName: string) => {\n        const fileType = file.fileType.toLowerCase();\n\n        if (['png', 'jpg', 'jpeg'].includes(fileType)) {\n          // Use image attachment for image files with GPT-4 Vision\n          content.push({\n            type: 'text',\n            text: `\\n${documentName} Document (${file.fileName}):`\n          });\n          content.push({\n            type: 'image_url',\n            image_url: {\n              url: `data:image/${fileType === 'jpg' ? 'jpeg' : fileType};base64,${file.base64Data}`\n            }\n          });\n        }\n        else {\n          // For non-image files, include filename and note about content\n          content.push({\n            type: 'text',\n            text: `\\n${documentName} Document: ${file.fileName} (${fileType.toUpperCase()} file - please note that the content extraction may be limited for this file type)`\n          });\n        }\n      };\n\n      // Add each document\n      addDocumentContentOpenAI(documentFiles.enterpriseNeedFile, 'Enterprise Need');\n      addDocumentContentOpenAI(documentFiles.solutionFile, 'Solution Description');\n      addDocumentContentOpenAI(documentFiles.riskFile, 'Risk of No Investment');\n\n      const response = await this.openaiClient.chat.completions.create({\n        model: 'gpt-4-vision-preview',\n        max_tokens: 4000,\n        temperature: 0.7,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert business analyst creating professional Business Value Assessment artifacts. You can analyze both text and image content to create comprehensive business documents.'\n          },\n          {\n            role: 'user',\n            content: content\n          }\n        ]\n      });\n\n      const responseContent = response.choices[0]?.message?.content;\n      if (!responseContent) {\n        throw new Error('No content received from OpenAI');\n      }\n\n      return this.parseAIResponse(responseContent);\n    } catch (error) {\n      console.error('OpenAI file generation failed:', error);\n      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  }\n\n  /**\n   * Generate artifacts with retry logic using file attachments\n   */\n  static async generateArtifactsWithFilesRetry(\n    llmType: 'claude' | 'openai',\n    prompt: string,\n    documentFiles: DocumentContextWithFiles,\n    solutionName: string,\n    companyName: string,\n    maxRetries: number = 3\n  ): Promise<AIResponse> {\n    let lastError: Error | null = null;\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        if (llmType === 'claude') {\n          return await this.generateWithClaudeFiles(prompt, documentFiles, solutionName, companyName);\n        } else {\n          return await this.generateWithOpenAIFiles(prompt, documentFiles, solutionName, companyName);\n        }\n      } catch (error) {\n        lastError = error instanceof Error ? error : new Error('Unknown error');\n        console.error(`AI file generation attempt ${attempt} failed:`, lastError);\n\n        if (attempt < maxRetries) {\n          // Wait before retrying (exponential backoff)\n          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));\n        }\n      }\n    }\n\n    throw new Error(`AI file generation failed after ${maxRetries} attempts: ${lastError?.message}`);\n  }\n\n  /**\n   * Get the MIME type for a file based on its extension\n   */\n  private static getMimeType(fileExtension: string): string {\n    const mimeTypes: { [key: string]: string } = {\n      'pdf': 'application/pdf',\n      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n      'doc': 'application/msword',\n      'txt': 'text/plain',\n      'png': 'image/png',\n      'jpg': 'image/jpeg',\n      'jpeg': 'image/jpeg'\n    };\n\n    return mimeTypes[fileExtension.toLowerCase()] || 'application/octet-stream';\n  }\n\n  /**\n   * Generate artifacts with retry logic\n   */\n  static async generateArtifactsWithRetry(\n    llmType: 'claude' | 'openai',\n    prompt: string,\n    documentContext: DocumentContext,\n    solutionName: string,\n    companyName: string,\n    maxRetries: number = 3\n  ): Promise<AIResponse> {\n    let lastError: Error | null = null;\n\n    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n      try {\n        if (llmType === 'claude') {\n          return await this.generateWithClaude(prompt, documentContext, solutionName, companyName);\n        } else {\n          return await this.generateWithOpenAI(prompt, documentContext, solutionName, companyName);\n        }\n      } catch (error) {\n        lastError = error instanceof Error ? error : new Error('Unknown error');\n        console.error(`AI generation attempt ${attempt} failed:`, lastError);\n        \n        if (attempt < maxRetries) {\n          // Wait before retrying (exponential backoff)\n          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));\n        }\n      }\n    }\n\n    throw new Error(`AI generation failed after ${maxRetries} attempts: ${lastError?.message}`);\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAqBO,MAAM;IACX,OAAe,eAAiC,KAAK;IACrD,OAAe,eAA8B,KAAK;IAElD;;GAEC,GACD,OAAO,iBAAiB,MAAc,EAAQ;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,2LAAA,CAAA,UAAS,CAAC;YAChC,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,MAAc,EAAQ;QAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,sKAAA,CAAA,UAAM,CAAC;YAC7B,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,aAAa,qBAAqB,MAAc,EAAoB;QAClE,IAAI;YACF,MAAM,SAAS,IAAI,2LAAA,CAAA,UAAS,CAAC;gBAAE;YAAO;YAEtC,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;gBAC5C,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YAEA,OAAO,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;QAClF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAa,qBAAqB,MAAc,EAAoB;QAClE,IAAI;YACF,MAAM,SAAS,IAAI,sKAAA,CAAA,UAAM,CAAC;gBAAE;YAAO;YAEnC,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,YAAY;gBACZ,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YAEA,OAAO,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,SAAS,SAAS,SAAS;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAa,mBACX,MAAc,EACd,eAAgC,EAChC,YAAoB,EACpB,WAAmB,EACE;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,QAAQ,iBAAiB,cAAc;YAE/E,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACvD,OAAO;gBACP,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YAEA,IAAI,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,QAAQ;gBACvC,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACjG;IACF;IAEA;;GAEC,GACD,aAAa,mBACX,MAAc,EACd,eAAgC,EAChC,YAAoB,EACpB,WAAmB,EACE;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,QAAQ,iBAAiB,cAAc;YAE/E,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC/D,OAAO;gBACP,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACjG;IACF;IAEA;;GAEC,GACD,OAAe,gBACb,UAAkB,EAClB,eAAgC,EAChC,YAAoB,EACpB,WAAmB,EACX;QACR,OAAO,GAAG,WAAW;;;;;AAKzB,EAAE,gBAAgB,cAAc,CAAC;;;AAGjC,EAAE,gBAAgB,QAAQ,CAAC;;;AAG3B,EAAE,gBAAgB,IAAI,CAAC;;sQAE+O,CAAC;IACrQ;IAEA;;GAEC,GACD,OAAe,gBAAgB,QAAgB,EAAc;QAC3D,iDAAiD;QACjD,MAAM,WAAW,SAAS,KAAK,CAAC;QAEhC,IAAI,yBAAyB;QAC7B,IAAI,mBAAmB;QACvB,IAAI,eAAe;QAEnB,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI,CAAC,gBAAgB;YAErB,IAAI,eAAe,WAAW,GAAG,QAAQ,CAAC,oBAAoB;gBAC5D,yBAAyB;YAC3B,OAAO,IAAI,eAAe,WAAW,GAAG,QAAQ,CAAC,sBAAsB;gBACrE,mBAAmB;YACrB,OAAO,IAAI,eAAe,WAAW,GAAG,QAAQ,CAAC,0BAA0B;gBACzE,eAAe;YACjB;QACF;QAEA,8CAA8C;QAC9C,IAAI,CAAC,0BAA0B,CAAC,oBAAoB,CAAC,cAAc;YACjE,MAAM,QAAQ,SAAS,KAAK,CAAC;YAC7B,IAAI,MAAM,MAAM,IAAI,GAAG;gBACrB,yBAAyB,KAAK,CAAC,EAAE,IAAI;gBACrC,mBAAmB,KAAK,CAAC,EAAE,IAAI;gBAC/B,eAAe,KAAK,CAAC,EAAE,IAAI;YAC7B,OAAO;gBACL,+EAA+E;gBAC/E,MAAM,kBAAkB;gBACxB,yBAAyB,CAAC,8BAA8B,EAAE,iBAAiB;gBAC3E,mBAAmB,CAAC,gCAAgC,EAAE,iBAAiB;gBACvE,eAAe,CAAC,oCAAoC,EAAE,iBAAiB;YACzE;QACF;QAEA,OAAO;YACL,wBAAwB,uBAAuB,IAAI;YACnD,kBAAkB,iBAAiB,IAAI;YACvC,cAAc,aAAa,IAAI;QACjC;IACF;IAEA;;GAEC,GACD,aAAa,wBACX,MAAc,EACd,aAAuC,EACvC,YAAoB,EACpB,WAAmB,EACE;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,mDAAmD;YACnD,MAAM,UAAiB,EAAE;YAEzB,0CAA0C;YAC1C,MAAM,qBAAqB,CAAC,MAAoB;gBAC9C,MAAM,WAAW,KAAK,QAAQ,CAAC,WAAW;gBAE1C,IAAI,aAAa,OAAO;oBACtB,wCAAwC;oBACxC,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,QAAQ;4BACN,MAAM;4BACN,YAAY;4BACZ,MAAM,KAAK,UAAU;wBACvB;oBACF;oBACA,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,MAAM,CAAC,gCAAgC,EAAE,aAAa,aAAa,CAAC;oBACtE;gBACF,OACK,IAAI;oBAAC;oBAAO;oBAAO;iBAAO,CAAC,QAAQ,CAAC,WAAW;oBAClD,uCAAuC;oBACvC,MAAM,WAAW,aAAa,QAAQ,eAAe,CAAC,MAAM,EAAE,UAAU;oBACxE,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,QAAQ;4BACN,MAAM;4BACN,YAAY;4BACZ,MAAM,KAAK,UAAU;wBACvB;oBACF;oBACA,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,MAAM,CAAC,6BAA6B,EAAE,aAAa,aAAa,CAAC;oBACnE;gBACF,OACK;oBACH,2EAA2E;oBAC3E,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,QAAQ;4BACN,MAAM;4BACN,YAAY,IAAI,CAAC,WAAW,CAAC;4BAC7B,MAAM,KAAK,UAAU;wBACvB;oBACF;oBACA,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,MAAM,CAAC,gCAAgC,EAAE,aAAa,aAAa,CAAC;oBACtE;gBACF;YACF;YAEA,oBAAoB;YACpB,mBAAmB,cAAc,kBAAkB,EAAE;YACrD,mBAAmB,cAAc,YAAY,EAAE;YAC/C,mBAAmB,cAAc,QAAQ,EAAE;YAE3C,sBAAsB;YACtB,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,MAAM,GAAG,OAAO;;;;;;;;sIAQ8G,CAAC;YACjI;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACvD,OAAO;gBACP,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR;wBACE,MAAM;wBACN;oBACF;iBACD;YACH;YAEA,IAAI,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK,QAAQ;gBACvC,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,IAAI;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACjG;IACF;IAEA;;;GAGC,GACD,aAAa,wBACX,MAAc,EACd,aAAuC,EACvC,YAAoB,EACpB,WAAmB,EACE;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,UAAiB,EAAE;YAEzB,4BAA4B;YAC5B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,MAAM,GAAG,OAAO;;;;qBAIH,CAAC;YAChB;YAEA,qDAAqD;YACrD,MAAM,2BAA2B,CAAC,MAAoB;gBACpD,MAAM,WAAW,KAAK,QAAQ,CAAC,WAAW;gBAE1C,IAAI;oBAAC;oBAAO;oBAAO;iBAAO,CAAC,QAAQ,CAAC,WAAW;oBAC7C,yDAAyD;oBACzD,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,MAAM,CAAC,EAAE,EAAE,aAAa,WAAW,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;oBACxD;oBACA,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,WAAW;4BACT,KAAK,CAAC,WAAW,EAAE,aAAa,QAAQ,SAAS,SAAS,QAAQ,EAAE,KAAK,UAAU,EAAE;wBACvF;oBACF;gBACF,OACK;oBACH,+DAA+D;oBAC/D,QAAQ,IAAI,CAAC;wBACX,MAAM;wBACN,MAAM,CAAC,EAAE,EAAE,aAAa,WAAW,EAAE,KAAK,QAAQ,CAAC,EAAE,EAAE,SAAS,WAAW,GAAG,kFAAkF,CAAC;oBACnK;gBACF;YACF;YAEA,oBAAoB;YACpB,yBAAyB,cAAc,kBAAkB,EAAE;YAC3D,yBAAyB,cAAc,YAAY,EAAE;YACrD,yBAAyB,cAAc,QAAQ,EAAE;YAEjD,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC/D,OAAO;gBACP,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;YACH;YAEA,MAAM,kBAAkB,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YACtD,IAAI,CAAC,iBAAiB;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QACjG;IACF;IAEA;;GAEC,GACD,aAAa,gCACX,OAA4B,EAC5B,MAAc,EACd,aAAuC,EACvC,YAAoB,EACpB,WAAmB,EACnB,aAAqB,CAAC,EACD;QACrB,IAAI,YAA0B;QAE9B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI,YAAY,UAAU;oBACxB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,eAAe,cAAc;gBACjF,OAAO;oBACL,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,eAAe,cAAc;gBACjF;YACF,EAAE,OAAO,OAAO;gBACd,YAAY,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;gBACvD,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,QAAQ,QAAQ,CAAC,EAAE;gBAE/D,IAAI,UAAU,YAAY;oBACxB,6CAA6C;oBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,GAAG,CAAC,GAAG,WAAW;gBAC1E;YACF;QACF;QAEA,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,WAAW,WAAW,EAAE,WAAW,SAAS;IACjG;IAEA;;GAEC,GACD,OAAe,YAAY,aAAqB,EAAU;QACxD,MAAM,YAAuC;YAC3C,OAAO;YACP,QAAQ;YACR,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QAEA,OAAO,SAAS,CAAC,cAAc,WAAW,GAAG,IAAI;IACnD;IAEA;;GAEC,GACD,aAAa,2BACX,OAA4B,EAC5B,MAAc,EACd,eAAgC,EAChC,YAAoB,EACpB,WAAmB,EACnB,aAAqB,CAAC,EACD;QACrB,IAAI,YAA0B;QAE9B,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,IAAI,YAAY,UAAU;oBACxB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,iBAAiB,cAAc;gBAC9E,OAAO;oBACL,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,iBAAiB,cAAc;gBAC9E;YACF,EAAE,OAAO,OAAO;gBACd,YAAY,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;gBACvD,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,QAAQ,QAAQ,CAAC,EAAE;gBAE1D,IAAI,UAAU,YAAY;oBACxB,6CAA6C;oBAC7C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,GAAG,CAAC,GAAG,WAAW;gBAC1E;YACF;QACF;QAEA,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,WAAW,WAAW,EAAE,WAAW,SAAS;IAC5F;AACF", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/services/apiConfigService.ts"], "sourcesContent": ["import { doc, getDoc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { APIConfiguration } from '@/types';\nimport { AIService } from './aiService';\nimport CryptoJS from 'crypto-js';\n\nconst API_CONFIG_DOC_ID = 'main';\nconst ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'valtics-ai-default-key';\n\nexport class APIConfigService {\n  /**\n   * Encrypt API key for storage\n   */\n  private static encryptApiKey(apiKey: string): string {\n    return CryptoJS.AES.encrypt(apiKey, ENCRYPTION_KEY).toString();\n  }\n\n  /**\n   * Decrypt API key from storage\n   */\n  private static decryptApiKey(encryptedKey: string): string {\n    const bytes = CryptoJS.AES.decrypt(encryptedKey, ENCRYPTION_KEY);\n    return bytes.toString(CryptoJS.enc.Utf8);\n  }\n\n  /**\n   * Get API configuration\n   */\n  static async getAPIConfiguration(): Promise<APIConfiguration | null> {\n    try {\n      const configDoc = await getDoc(doc(db, 'apiConfig', API_CONFIG_DOC_ID));\n      \n      if (!configDoc.exists()) {\n        return null;\n      }\n\n      const data = configDoc.data();\n      return {\n        id: configDoc.id,\n        claudeApiKey: data.claudeApiKey ? this.decryptApiKey(data.claudeApiKey) : undefined,\n        openaiApiKey: data.openaiApiKey ? this.decryptApiKey(data.openaiApiKey) : undefined,\n        createdAt: data.createdAt?.toDate() || new Date(),\n        updatedAt: data.updatedAt?.toDate() || new Date(),\n        claudeStatus: data.claudeStatus || 'untested',\n        openaiStatus: data.openaiStatus || 'untested',\n        lastTestedAt: data.lastTestedAt?.toDate(),\n        claudeModelConfig: data.claudeModelConfig || {\n          model: 'claude-3-5-sonnet-20241022',\n          maxTokens: 4000,\n          temperature: 0.7\n        },\n        openaiModelConfig: data.openaiModelConfig || {\n          model: 'gpt-4-vision-preview',\n          maxTokens: 4000,\n          temperature: 0.7\n        }\n      };\n    } catch (error) {\n      console.error('Error fetching API configuration:', error);\n      throw new Error('Failed to fetch API configuration');\n    }\n  }\n\n  /**\n   * Save API configuration\n   */\n  static async saveAPIConfiguration(config: Partial<APIConfiguration>): Promise<void> {\n    try {\n      const configRef = doc(db, 'apiConfig', API_CONFIG_DOC_ID);\n      const existingDoc = await getDoc(configRef);\n\n      const updateData: any = {\n        updatedAt: Timestamp.now()\n      };\n\n      if (config.claudeApiKey) {\n        updateData.claudeApiKey = this.encryptApiKey(config.claudeApiKey);\n        updateData.claudeStatus = 'untested';\n      }\n\n      if (config.openaiApiKey) {\n        updateData.openaiApiKey = this.encryptApiKey(config.openaiApiKey);\n        updateData.openaiStatus = 'untested';\n      }\n\n      if (config.claudeModelConfig) {\n        updateData.claudeModelConfig = config.claudeModelConfig;\n      }\n\n      if (config.openaiModelConfig) {\n        updateData.openaiModelConfig = config.openaiModelConfig;\n      }\n\n      if (existingDoc.exists()) {\n        await updateDoc(configRef, updateData);\n      } else {\n        await setDoc(configRef, {\n          ...updateData,\n          createdAt: Timestamp.now()\n        });\n      }\n    } catch (error) {\n      console.error('Error saving API configuration:', error);\n      throw new Error('Failed to save API configuration');\n    }\n  }\n\n  /**\n   * Test API connections using Firebase Function (secure server-side testing)\n   */\n  static async testAPIConnections(): Promise<{\n    claudeStatus: 'connected' | 'error' | 'untested';\n    openaiStatus: 'connected' | 'error' | 'untested';\n    claudeError?: string;\n    openaiError?: string;\n  }> {\n    try {\n      // Import Firebase Functions\n      const { getFunctions, httpsCallable } = await import('firebase/functions');\n      const functions = getFunctions();\n      const testAPIConnectionsFunction = httpsCallable(functions, 'testAPIConnectionsFunction');\n\n      // Call the Firebase Function to test API connections\n      const result = await testAPIConnectionsFunction();\n      return result.data as {\n        claudeStatus: 'connected' | 'error' | 'untested';\n        openaiStatus: 'connected' | 'error' | 'untested';\n        claudeError?: string;\n        openaiError?: string;\n      };\n    } catch (error) {\n      console.error('Error testing API connections:', error);\n      return {\n        claudeStatus: 'error',\n        openaiStatus: 'error',\n        claudeError: error instanceof Error ? error.message : 'Failed to test connections',\n        openaiError: error instanceof Error ? error.message : 'Failed to test connections'\n      };\n    }\n  }\n\n  /**\n   * Get decrypted API keys for server-side use\n   */\n  static async getDecryptedAPIKeys(): Promise<{\n    claudeApiKey?: string;\n    openaiApiKey?: string;\n  }> {\n    try {\n      const config = await this.getAPIConfiguration();\n      return {\n        claudeApiKey: config?.claudeApiKey,\n        openaiApiKey: config?.openaiApiKey\n      };\n    } catch (error) {\n      console.error('Error getting decrypted API keys:', error);\n      throw new Error('Failed to get API keys');\n    }\n  }\n\n  /**\n   * Check if API keys are configured\n   */\n  static async areAPIKeysConfigured(): Promise<{\n    claudeConfigured: boolean;\n    openaiConfigured: boolean;\n    anyConfigured: boolean;\n  }> {\n    try {\n      const config = await this.getAPIConfiguration();\n      const claudeConfigured = !!(config?.claudeApiKey);\n      const openaiConfigured = !!(config?.openaiApiKey);\n      \n      return {\n        claudeConfigured,\n        openaiConfigured,\n        anyConfigured: claudeConfigured || openaiConfigured\n      };\n    } catch (error) {\n      console.error('Error checking API key configuration:', error);\n      return {\n        claudeConfigured: false,\n        openaiConfigured: false,\n        anyConfigured: false\n      };\n    }\n  }\n\n  /**\n   * Initialize AI services with stored API keys\n   */\n  static async initializeAIServices(): Promise<void> {\n    try {\n      const keys = await this.getDecryptedAPIKeys();\n      \n      if (keys.claudeApiKey) {\n        AIService.initializeClaude(keys.claudeApiKey);\n      }\n      \n      if (keys.openaiApiKey) {\n        AIService.initializeOpenAI(keys.openaiApiKey);\n      }\n    } catch (error) {\n      console.error('Error initializing AI services:', error);\n      throw new Error('Failed to initialize AI services');\n    }\n  }\n\n  /**\n   * Validate API key format\n   */\n  static validateAPIKey(provider: 'claude' | 'openai', apiKey: string): { isValid: boolean; error?: string } {\n    if (!apiKey || apiKey.trim().length === 0) {\n      return { isValid: false, error: 'API key cannot be empty' };\n    }\n\n    if (provider === 'claude') {\n      if (!apiKey.startsWith('sk-ant-')) {\n        return { isValid: false, error: 'Claude API key should start with \"sk-ant-\"' };\n      }\n    } else if (provider === 'openai') {\n      if (!apiKey.startsWith('sk-')) {\n        return { isValid: false, error: 'OpenAI API key should start with \"sk-\"' };\n      }\n    }\n\n    if (apiKey.length < 20) {\n      return { isValid: false, error: 'API key appears to be too short' };\n    }\n\n    return { isValid: true };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAEA;AACA;;;;;AAEA,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB,gDAA0C;AAE1D,MAAM;IACX;;GAEC,GACD,OAAe,cAAc,MAAc,EAAU;QACnD,OAAO,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,gBAAgB,QAAQ;IAC9D;IAEA;;GAEC,GACD,OAAe,cAAc,YAAoB,EAAU;QACzD,MAAM,QAAQ,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc;QACjD,OAAO,MAAM,QAAQ,CAAC,qIAAA,CAAA,UAAQ,CAAC,GAAG,CAAC,IAAI;IACzC;IAEA;;GAEC,GACD,aAAa,sBAAwD;QACnE,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa;YAEpD,IAAI,CAAC,UAAU,MAAM,IAAI;gBACvB,OAAO;YACT;YAEA,MAAM,OAAO,UAAU,IAAI;YAC3B,OAAO;gBACL,IAAI,UAAU,EAAE;gBAChB,cAAc,KAAK,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,YAAY,IAAI;gBAC1E,cAAc,KAAK,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,YAAY,IAAI;gBAC1E,WAAW,KAAK,SAAS,EAAE,YAAY,IAAI;gBAC3C,WAAW,KAAK,SAAS,EAAE,YAAY,IAAI;gBAC3C,cAAc,KAAK,YAAY,IAAI;gBACnC,cAAc,KAAK,YAAY,IAAI;gBACnC,cAAc,KAAK,YAAY,EAAE;gBACjC,mBAAmB,KAAK,iBAAiB,IAAI;oBAC3C,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;gBACA,mBAAmB,KAAK,iBAAiB,IAAI;oBAC3C,OAAO;oBACP,WAAW;oBACX,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,qBAAqB,MAAiC,EAAiB;QAClF,IAAI;YACF,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa;YACvC,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;YAEjC,MAAM,aAAkB;gBACtB,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,IAAI,OAAO,YAAY,EAAE;gBACvB,WAAW,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,YAAY;gBAChE,WAAW,YAAY,GAAG;YAC5B;YAEA,IAAI,OAAO,YAAY,EAAE;gBACvB,WAAW,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,YAAY;gBAChE,WAAW,YAAY,GAAG;YAC5B;YAEA,IAAI,OAAO,iBAAiB,EAAE;gBAC5B,WAAW,iBAAiB,GAAG,OAAO,iBAAiB;YACzD;YAEA,IAAI,OAAO,iBAAiB,EAAE;gBAC5B,WAAW,iBAAiB,GAAG,OAAO,iBAAiB;YACzD;YAEA,IAAI,YAAY,MAAM,IAAI;gBACxB,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YAC7B,OAAO;gBACL,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,WAAW;oBACtB,GAAG,UAAU;oBACb,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,qBAKV;QACD,IAAI;YACF,4BAA4B;YAC5B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;YACxC,MAAM,YAAY;YAClB,MAAM,6BAA6B,cAAc,WAAW;YAE5D,qDAAqD;YACrD,MAAM,SAAS,MAAM;YACrB,OAAO,OAAO,IAAI;QAMpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,cAAc;gBACd,cAAc;gBACd,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACtD,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD;QACF;IACF;IAEA;;GAEC,GACD,aAAa,sBAGV;QACD,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,mBAAmB;YAC7C,OAAO;gBACL,cAAc,QAAQ;gBACtB,cAAc,QAAQ;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,aAAa,uBAIV;QACD,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,mBAAmB;YAC7C,MAAM,mBAAmB,CAAC,CAAE,QAAQ;YACpC,MAAM,mBAAmB,CAAC,CAAE,QAAQ;YAEpC,OAAO;gBACL;gBACA;gBACA,eAAe,oBAAoB;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,kBAAkB;gBAClB,kBAAkB;gBAClB,eAAe;YACjB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,uBAAsC;QACjD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,mBAAmB;YAE3C,IAAI,KAAK,YAAY,EAAE;gBACrB,4HAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,YAAY;YAC9C;YAEA,IAAI,KAAK,YAAY,EAAE;gBACrB,4HAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,YAAY;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,OAAO,eAAe,QAA6B,EAAE,MAAc,EAAwC;QACzG,IAAI,CAAC,UAAU,OAAO,IAAI,GAAG,MAAM,KAAK,GAAG;YACzC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,IAAI,aAAa,UAAU;YACzB,IAAI,CAAC,OAAO,UAAU,CAAC,YAAY;gBACjC,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAA6C;YAC/E;QACF,OAAO,IAAI,aAAa,UAAU;YAChC,IAAI,CAAC,OAAO,UAAU,CAAC,QAAQ;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAyC;YAC3E;QACF;QAEA,IAAI,OAAO,MAAM,GAAG,IAAI;YACtB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAkC;QACpE;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF", "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/admin/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Navigation from '@/components/Navigation';\nimport { APIConfigService } from '@/lib/services/apiConfigService';\nimport { APIConfiguration, LLMModelConfiguration } from '@/types';\n\nexport default function AdminSettings() {\n  const { user, loading, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const [apiConfig, setApiConfig] = useState<APIConfiguration | null>(null);\n  const [loadingConfig, setLoadingConfig] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [testing, setTesting] = useState(false);\n  const [showApiKeys, setShowApiKeys] = useState(false);\n\n  const [formData, setFormData] = useState({\n    claudeApiKey: '',\n    openaiApiKey: '',\n    claudeModelConfig: {\n      model: 'claude-3-5-sonnet-20241022',\n      maxTokens: 4000,\n      temperature: 0.7\n    } as LLMModelConfiguration,\n    openaiModelConfig: {\n      model: 'gpt-4-vision-preview',\n      maxTokens: 4000,\n      temperature: 0.7\n    } as LLMModelConfiguration\n  });\n\n  const [testResults, setTestResults] = useState<{\n    claudeStatus?: 'connected' | 'error' | 'untested';\n    openaiStatus?: 'connected' | 'error' | 'untested';\n  }>({});\n\n  useEffect(() => {\n    if (!loading && !isAdmin) {\n      router.push('/');\n      return;\n    }\n    if (isAdmin) {\n      fetchAPIConfig();\n    }\n  }, [loading, isAdmin, router]);\n\n  const fetchAPIConfig = async () => {\n    try {\n      setLoadingConfig(true);\n      const config = await APIConfigService.getAPIConfiguration();\n      setApiConfig(config);\n      \n      if (config) {\n        setFormData({\n          claudeApiKey: config.claudeApiKey || '',\n          openaiApiKey: config.openaiApiKey || '',\n          claudeModelConfig: config.claudeModelConfig || {\n            model: 'claude-3-5-sonnet-20241022',\n            maxTokens: 4000,\n            temperature: 0.7\n          },\n          openaiModelConfig: config.openaiModelConfig || {\n            model: 'gpt-4-vision-preview',\n            maxTokens: 4000,\n            temperature: 0.7\n          }\n        });\n        setTestResults({\n          claudeStatus: config.claudeStatus,\n          openaiStatus: config.openaiStatus\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching API config:', error);\n      alert('Error loading API configuration');\n    } finally {\n      setLoadingConfig(false);\n    }\n  };\n\n  const handleSaveConfig = async () => {\n    try {\n      setSaving(true);\n\n      // Validate API keys\n      if (formData.claudeApiKey) {\n        const claudeValidation = APIConfigService.validateAPIKey('claude', formData.claudeApiKey);\n        if (!claudeValidation.isValid) {\n          alert(`Claude API Key Error: ${claudeValidation.error}`);\n          return;\n        }\n      }\n\n      if (formData.openaiApiKey) {\n        const openaiValidation = APIConfigService.validateAPIKey('openai', formData.openaiApiKey);\n        if (!openaiValidation.isValid) {\n          alert(`OpenAI API Key Error: ${openaiValidation.error}`);\n          return;\n        }\n      }\n\n      await APIConfigService.saveAPIConfiguration({\n        claudeApiKey: formData.claudeApiKey || undefined,\n        openaiApiKey: formData.openaiApiKey || undefined,\n        claudeModelConfig: formData.claudeModelConfig,\n        openaiModelConfig: formData.openaiModelConfig\n      });\n\n      alert('API configuration saved successfully!');\n      await fetchAPIConfig(); // Refresh data\n\n    } catch (error) {\n      console.error('Error saving API config:', error);\n      alert('Error saving API configuration');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleTestConnections = async () => {\n    try {\n      setTesting(true);\n      const results = await APIConfigService.testAPIConnections();\n      setTestResults(results);\n\n      const messages = [];\n      if (results.claudeStatus === 'connected') {\n        messages.push('✅ Claude API: Connected successfully');\n      } else if (results.claudeStatus === 'error') {\n        messages.push(`❌ Claude API: ${results.claudeError || 'Connection failed'}`);\n      } else {\n        messages.push('⚪ Claude API: Not configured');\n      }\n\n      if (results.openaiStatus === 'connected') {\n        messages.push('✅ OpenAI API: Connected successfully');\n      } else if (results.openaiStatus === 'error') {\n        messages.push(`❌ OpenAI API: ${results.openaiError || 'Connection failed'}`);\n      } else {\n        messages.push('⚪ OpenAI API: Not configured');\n      }\n\n      alert('API Connection Test Results:\\n\\n' + messages.join('\\n'));\n\n    } catch (error) {\n      console.error('Error testing connections:', error);\n      alert('Error testing API connections. Please try again.');\n    } finally {\n      setTesting(false);\n    }\n  };\n\n  const getStatusIcon = (status?: 'connected' | 'error' | 'untested') => {\n    switch (status) {\n      case 'connected':\n        return <span className=\"text-green-500\">✓ Connected</span>;\n      case 'error':\n        return <span className=\"text-red-500\">✗ Error</span>;\n      case 'untested':\n      default:\n        return <span className=\"text-gray-500\">○ Untested</span>;\n    }\n  };\n\n  if (loading || loadingConfig) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!isAdmin) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation />\n\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">Admin Settings</h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n              Configure API keys and system settings for VALTICS AI.\n            </p>\n          </div>\n\n          {/* API Configuration Section */}\n          <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                    AI API Configuration\n                  </h2>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                    Configure API keys for Claude AI and OpenAI to enable AI artifact generation.\n                  </p>\n                </div>\n                <button\n                  onClick={() => setShowApiKeys(!showApiKeys)}\n                  className=\"text-sm text-blue-600 dark:text-blue-400 hover:underline\"\n                >\n                  {showApiKeys ? 'Hide' : 'Show'} API Keys\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Claude API Key */}\n                <div>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Claude AI API Key\n                    </label>\n                    {getStatusIcon(testResults.claudeStatus)}\n                  </div>\n                  <input\n                    type={showApiKeys ? 'text' : 'password'}\n                    value={formData.claudeApiKey}\n                    onChange={(e) => setFormData(prev => ({ ...prev, claudeApiKey: e.target.value }))}\n                    placeholder=\"sk-ant-...\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    Get your API key from <a href=\"https://console.anthropic.com/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 dark:text-blue-400 hover:underline\">Anthropic Console</a>\n                  </p>\n                </div>\n\n                {/* OpenAI API Key */}\n                <div>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      OpenAI API Key\n                    </label>\n                    {getStatusIcon(testResults.openaiStatus)}\n                  </div>\n                  <input\n                    type={showApiKeys ? 'text' : 'password'}\n                    value={formData.openaiApiKey}\n                    onChange={(e) => setFormData(prev => ({ ...prev, openaiApiKey: e.target.value }))}\n                    placeholder=\"sk-...\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  />\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    Get your API key from <a href=\"https://platform.openai.com/api-keys\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 dark:text-blue-400 hover:underline\">OpenAI Platform</a>\n                  </p>\n                </div>\n\n                {/* Model Configuration Section */}\n                <div className=\"pt-6 border-t border-gray-200 dark:border-gray-700\">\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                    Model Configuration\n                  </h3>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\n                    Configure the AI models and parameters used for artifact generation.\n                  </p>\n\n                  {/* Claude Model Configuration */}\n                  <div className=\"mb-6\">\n                    <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n                      Claude AI Configuration\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          Model\n                        </label>\n                        <select\n                          value={formData.claudeModelConfig.model}\n                          onChange={(e) => setFormData(prev => ({\n                            ...prev,\n                            claudeModelConfig: { ...prev.claudeModelConfig, model: e.target.value }\n                          }))}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                        >\n                          <option value=\"claude-3-5-sonnet-20241022\">Claude 3.5 Sonnet (Latest)</option>\n                          <option value=\"claude-3-sonnet-20240229\">Claude 3 Sonnet</option>\n                          <option value=\"claude-3-haiku-20240307\">Claude 3 Haiku</option>\n                          <option value=\"claude-3-opus-20240229\">Claude 3 Opus</option>\n                        </select>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          Max Tokens\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"100\"\n                          max=\"8000\"\n                          value={formData.claudeModelConfig.maxTokens}\n                          onChange={(e) => setFormData(prev => ({\n                            ...prev,\n                            claudeModelConfig: { ...prev.claudeModelConfig, maxTokens: parseInt(e.target.value) }\n                          }))}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          Temperature\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"0\"\n                          max=\"1\"\n                          step=\"0.1\"\n                          value={formData.claudeModelConfig.temperature}\n                          onChange={(e) => setFormData(prev => ({\n                            ...prev,\n                            claudeModelConfig: { ...prev.claudeModelConfig, temperature: parseFloat(e.target.value) }\n                          }))}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* OpenAI Model Configuration */}\n                  <div className=\"mb-6\">\n                    <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n                      OpenAI Configuration\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          Model\n                        </label>\n                        <select\n                          value={formData.openaiModelConfig.model}\n                          onChange={(e) => setFormData(prev => ({\n                            ...prev,\n                            openaiModelConfig: { ...prev.openaiModelConfig, model: e.target.value }\n                          }))}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                        >\n                          <option value=\"gpt-4-vision-preview\">GPT-4 Vision Preview</option>\n                          <option value=\"gpt-4-turbo-preview\">GPT-4 Turbo Preview</option>\n                          <option value=\"gpt-4\">GPT-4</option>\n                          <option value=\"gpt-4-32k\">GPT-4 32K</option>\n                          <option value=\"gpt-3.5-turbo\">GPT-3.5 Turbo</option>\n                        </select>\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          Max Tokens\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"100\"\n                          max=\"8000\"\n                          value={formData.openaiModelConfig.maxTokens}\n                          onChange={(e) => setFormData(prev => ({\n                            ...prev,\n                            openaiModelConfig: { ...prev.openaiModelConfig, maxTokens: parseInt(e.target.value) }\n                          }))}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          Temperature\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"0\"\n                          max=\"1\"\n                          step=\"0.1\"\n                          value={formData.openaiModelConfig.temperature}\n                          onChange={(e) => setFormData(prev => ({\n                            ...prev,\n                            openaiModelConfig: { ...prev.openaiModelConfig, temperature: parseFloat(e.target.value) }\n                          }))}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n                  <button\n                    onClick={handleSaveConfig}\n                    disabled={saving}\n                    className=\"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    {saving && (\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                    )}\n                    <span>{saving ? 'Saving...' : 'Save Configuration'}</span>\n                  </button>\n\n                  <button\n                    onClick={handleTestConnections}\n                    disabled={testing || (!formData.claudeApiKey && !formData.openaiApiKey)}\n                    className=\"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    {testing && (\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                    )}\n                    <span>{testing ? 'Testing...' : 'Test Connections'}</span>\n                  </button>\n                </div>\n\n                {/* Last Tested Info */}\n                {apiConfig?.lastTestedAt && (\n                  <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    Last tested: {apiConfig.lastTestedAt.toLocaleString()}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Security Notice */}\n          <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-yellow-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                  Security Notice\n                </h3>\n                <div className=\"mt-2 text-sm text-yellow-700 dark:text-yellow-300\">\n                  <p>\n                    API keys are encrypted before storage and are only accessible to admin users. \n                    Keep your API keys secure and rotate them regularly. Never share your API keys with unauthorized users.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc;QACd,cAAc;QACd,mBAAmB;YACjB,OAAO;YACP,WAAW;YACX,aAAa;QACf;QACA,mBAAmB;YACjB,OAAO;YACP,WAAW;YACX,aAAa;QACf;IACF;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG1C,CAAC;IAEJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,SAAS;YACxB,OAAO,IAAI,CAAC;YACZ;QACF;QACA,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAS;QAAS;KAAO;IAE7B,MAAM,iBAAiB;QACrB,IAAI;YACF,iBAAiB;YACjB,MAAM,SAAS,MAAM,mIAAA,CAAA,mBAAgB,CAAC,mBAAmB;YACzD,aAAa;YAEb,IAAI,QAAQ;gBACV,YAAY;oBACV,cAAc,OAAO,YAAY,IAAI;oBACrC,cAAc,OAAO,YAAY,IAAI;oBACrC,mBAAmB,OAAO,iBAAiB,IAAI;wBAC7C,OAAO;wBACP,WAAW;wBACX,aAAa;oBACf;oBACA,mBAAmB,OAAO,iBAAiB,IAAI;wBAC7C,OAAO;wBACP,WAAW;wBACX,aAAa;oBACf;gBACF;gBACA,eAAe;oBACb,cAAc,OAAO,YAAY;oBACjC,cAAc,OAAO,YAAY;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,UAAU;YAEV,oBAAoB;YACpB,IAAI,SAAS,YAAY,EAAE;gBACzB,MAAM,mBAAmB,mIAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,UAAU,SAAS,YAAY;gBACxF,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,MAAM,CAAC,sBAAsB,EAAE,iBAAiB,KAAK,EAAE;oBACvD;gBACF;YACF;YAEA,IAAI,SAAS,YAAY,EAAE;gBACzB,MAAM,mBAAmB,mIAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,UAAU,SAAS,YAAY;gBACxF,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,MAAM,CAAC,sBAAsB,EAAE,iBAAiB,KAAK,EAAE;oBACvD;gBACF;YACF;YAEA,MAAM,mIAAA,CAAA,mBAAgB,CAAC,oBAAoB,CAAC;gBAC1C,cAAc,SAAS,YAAY,IAAI;gBACvC,cAAc,SAAS,YAAY,IAAI;gBACvC,mBAAmB,SAAS,iBAAiB;gBAC7C,mBAAmB,SAAS,iBAAiB;YAC/C;YAEA,MAAM;YACN,MAAM,kBAAkB,eAAe;QAEzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,WAAW;YACX,MAAM,UAAU,MAAM,mIAAA,CAAA,mBAAgB,CAAC,kBAAkB;YACzD,eAAe;YAEf,MAAM,WAAW,EAAE;YACnB,IAAI,QAAQ,YAAY,KAAK,aAAa;gBACxC,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,QAAQ,YAAY,KAAK,SAAS;gBAC3C,SAAS,IAAI,CAAC,CAAC,cAAc,EAAE,QAAQ,WAAW,IAAI,qBAAqB;YAC7E,OAAO;gBACL,SAAS,IAAI,CAAC;YAChB;YAEA,IAAI,QAAQ,YAAY,KAAK,aAAa;gBACxC,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,QAAQ,YAAY,KAAK,SAAS;gBAC3C,SAAS,IAAI,CAAC,CAAC,cAAc,EAAE,QAAQ,WAAW,IAAI,qBAAqB;YAC7E,OAAO;gBACL,SAAS,IAAI,CAAC;YAChB;YAEA,MAAM,qCAAqC,SAAS,IAAI,CAAC;QAE3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAiB;;;;;;YAC1C,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAe;;;;;;YACxC,KAAK;YACL;gBACE,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;QAC3C;IACF;IAEA,IAAI,WAAW,eAAe;QAC5B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAMvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEAGpE,8OAAC;wDAAE,WAAU;kEAAgD;;;;;;;;;;;;0DAI/D,8OAAC;gDACC,SAAS,IAAM,eAAe,CAAC;gDAC/B,WAAU;;oDAET,cAAc,SAAS;oDAAO;;;;;;;;;;;;;kDAInC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA6D;;;;;;4DAG7E,cAAc,YAAY,YAAY;;;;;;;kEAEzC,8OAAC;wDACC,MAAM,cAAc,SAAS;wDAC7B,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC/E,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;;4DAAgD;0EACrC,8OAAC;gEAAE,MAAK;gEAAiC,QAAO;gEAAS,KAAI;gEAAsB,WAAU;0EAAmD;;;;;;;;;;;;;;;;;;0DAK1K,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA6D;;;;;;4DAG7E,cAAc,YAAY,YAAY;;;;;;;kEAEzC,8OAAC;wDACC,MAAM,cAAc,SAAS;wDAC7B,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC/E,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;;4DAAgD;0EACrC,8OAAC;gEAAE,MAAK;gEAAuC,QAAO;gEAAS,KAAI;gEAAsB,WAAU;0EAAmD;;;;;;;;;;;;;;;;;;0DAKhL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyD;;;;;;kEAGvE,8OAAC;wDAAE,WAAU;kEAAgD;;;;;;kEAK7D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyD;;;;;;0EAGvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,8OAAC;gFACC,OAAO,SAAS,iBAAiB,CAAC,KAAK;gFACvC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4FACpC,GAAG,IAAI;4FACP,mBAAmB;gGAAE,GAAG,KAAK,iBAAiB;gGAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4FAAC;wFACxE,CAAC;gFACD,WAAU;;kGAEV,8OAAC;wFAAO,OAAM;kGAA6B;;;;;;kGAC3C,8OAAC;wFAAO,OAAM;kGAA2B;;;;;;kGACzC,8OAAC;wFAAO,OAAM;kGAA0B;;;;;;kGACxC,8OAAC;wFAAO,OAAM;kGAAyB;;;;;;;;;;;;;;;;;;kFAG3C,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,8OAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,KAAI;gFACJ,OAAO,SAAS,iBAAiB,CAAC,SAAS;gFAC3C,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4FACpC,GAAG,IAAI;4FACP,mBAAmB;gGAAE,GAAG,KAAK,iBAAiB;gGAAE,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;4FAAE;wFACtF,CAAC;gFACD,WAAU;;;;;;;;;;;;kFAGd,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,8OAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,KAAI;gFACJ,MAAK;gFACL,OAAO,SAAS,iBAAiB,CAAC,WAAW;gFAC7C,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4FACpC,GAAG,IAAI;4FACP,mBAAmB;gGAAE,GAAG,KAAK,iBAAiB;gGAAE,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;4FAAE;wFAC1F,CAAC;gFACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kEAOlB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyD;;;;;;0EAGvE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,8OAAC;gFACC,OAAO,SAAS,iBAAiB,CAAC,KAAK;gFACvC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4FACpC,GAAG,IAAI;4FACP,mBAAmB;gGAAE,GAAG,KAAK,iBAAiB;gGAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4FAAC;wFACxE,CAAC;gFACD,WAAU;;kGAEV,8OAAC;wFAAO,OAAM;kGAAuB;;;;;;kGACrC,8OAAC;wFAAO,OAAM;kGAAsB;;;;;;kGACpC,8OAAC;wFAAO,OAAM;kGAAQ;;;;;;kGACtB,8OAAC;wFAAO,OAAM;kGAAY;;;;;;kGAC1B,8OAAC;wFAAO,OAAM;kGAAgB;;;;;;;;;;;;;;;;;;kFAGlC,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,8OAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,KAAI;gFACJ,OAAO,SAAS,iBAAiB,CAAC,SAAS;gFAC3C,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4FACpC,GAAG,IAAI;4FACP,mBAAmB;gGAAE,GAAG,KAAK,iBAAiB;gGAAE,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;4FAAE;wFACtF,CAAC;gFACD,WAAU;;;;;;;;;;;;kFAGd,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAAkE;;;;;;0FAGnF,8OAAC;gFACC,MAAK;gFACL,KAAI;gFACJ,KAAI;gFACJ,MAAK;gFACL,OAAO,SAAS,iBAAiB,CAAC,WAAW;gFAC7C,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4FACpC,GAAG,IAAI;4FACP,mBAAmB;gGAAE,GAAG,KAAK,iBAAiB;gGAAE,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;4FAAE;wFAC1F,CAAC;gFACD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAU;;4DAET,wBACC,8OAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,8OAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,8OAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;0EAGvD,8OAAC;0EAAM,SAAS,cAAc;;;;;;;;;;;;kEAGhC,8OAAC;wDACC,SAAS;wDACT,UAAU,WAAY,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,YAAY;wDACtE,WAAU;;4DAET,yBACC,8OAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,8OAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,8OAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;0EAGvD,8OAAC;0EAAM,UAAU,eAAe;;;;;;;;;;;;;;;;;;4CAKnC,WAAW,8BACV,8OAAC;gDAAI,WAAU;;oDAA2C;oDAC1C,UAAU,YAAY,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA0B,OAAM;4CAA6B,SAAQ;4CAAY,MAAK;sDACnG,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAoN,UAAS;;;;;;;;;;;;;;;;kDAG5P,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}