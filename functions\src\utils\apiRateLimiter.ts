/**
 * Utility for handling API rate limits with exponential backoff
 */
export class ApiRateLimiter {
  /**
   * Executes a function with retry logic for handling rate limits
   * @param fn Function to execute
   * @param maxRetries Maximum number of retries
   * @param initialDelayMs Initial delay in milliseconds
   * @returns Promise with the function result
   */
  static async executeWithRetry<T>(
    fn: () => Promise<T>,
    maxRetries = 5,
    initialDelayMs = 1000
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        lastError = error;
        
        // Check if it's a quota exceeded error (429)
        const isQuotaError = 
          error.code === 429 || 
          error.message?.includes('429') || 
          error.message?.includes('Quota exceeded');
        
        if (!isQuotaError || attempt === maxRetries) {
          throw error;
        }
        
        // Calculate backoff delay with exponential increase and jitter
        const delayMs = initialDelayMs * Math.pow(2, attempt) * (0.5 + Math.random());
        console.log(`API rate limit hit. Retrying in ${Math.round(delayMs)}ms (attempt ${attempt + 1}/${maxRetries})`);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }
    
    throw lastError || new Error('Maximum retries exceeded');
  }
}