(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3074:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var l=r(5155),a=r(3391),s=r(2115);function o(){let[e,t]=(0,s.useState)(!1),[r,o]=(0,s.useState)("light"),i=(0,a.Q)();(0,s.useEffect)(()=>{if(t(!0),!i){let e=localStorage.getItem("theme");if(e)o(e),d(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";o(e),d(e)}}},[i]);let d=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};if(!e)return(0,l.jsx)("div",{className:"p-2 w-9 h-9"});let n=i?i.theme:r;return(0,l.jsx)("button",{onClick:()=>{if(i)i.toggleTheme();else{let e="light"===r?"dark":"light";o(e),d(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":"Switch to ".concat("light"===n?"dark":"light"," mode"),title:"Switch to ".concat("light"===n?"dark":"light"," mode"),children:"light"===n?(0,l.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,l.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},3391:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o,ThemeProvider:()=>i});var l=r(5155),a=r(2115);let s=(0,a.createContext)(null),o=()=>(0,a.useContext)(s),i=e=>{let{children:t}=e,[r,o]=(0,a.useState)("light"),[i,d]=(0,a.useState)(!1);(0,a.useEffect)(()=>{d(!0);let e=localStorage.getItem("theme");if(e)o(e),n(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";o(e),n(e)}},[]);let n=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return i?(0,l.jsx)(s.Provider,{value:{theme:r,toggleTheme:()=>{let e="light"===r?"dark":"light";o(e),n(e),localStorage.setItem("theme",e)}},children:t}):(0,l.jsx)(l.Fragment,{children:t})}},9081:(e,t,r)=>{Promise.resolve().then(r.bind(r,3074)),Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.t.bind(r,3063,23))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,63,441,684,358],()=>t(9081)),_N_E=e.O()}]);