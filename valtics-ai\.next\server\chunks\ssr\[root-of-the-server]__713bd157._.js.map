{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/firebase/config.ts"], "sourcesContent": ["import { initializeApp, getApps, getApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,\n};\n\n// Initialize Firebase\nconst app = !getApps().length ? initializeApp(firebaseConfig) : getApp();\nconst auth = getAuth(app);\nconst db = getFirestore(app);\nconst storage = getStorage(app);\n\nexport { app, auth, db, storage };"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAC,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,GAAG,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,oLAAA,CAAA,SAAM,AAAD;AACrE,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/lib/trial.ts"], "sourcesContent": ["import { User } from '@/types';\n\n// Trial duration in days\nexport const TRIAL_DURATION_DAYS = 10;\n\n/**\n * Creates trial data for a new user\n */\nexport function createTrialData() {\n  const now = new Date();\n  const trialEndDate = new Date(now);\n  trialEndDate.setDate(now.getDate() + TRIAL_DURATION_DAYS);\n\n  return {\n    isTrialUser: true,\n    trialStartDate: now,\n    trialEndDate: trialEndDate,\n    trialExpired: false\n  };\n}\n\n/**\n * Checks if a user's trial has expired\n */\nexport function isTrialExpired(user: User): boolean {\n  if (!user.isTrialUser || !user.trialEndDate) {\n    return false;\n  }\n\n  const now = new Date();\n  const trialEnd = new Date(user.trialEndDate);\n  return now > trialEnd;\n}\n\n/**\n * Gets the number of days remaining in trial\n */\nexport function getTrialDaysRemaining(user: User): number {\n  if (!user.isTrialUser || !user.trialEndDate) {\n    return 0;\n  }\n\n  const now = new Date();\n  const trialEnd = new Date(user.trialEndDate);\n  const diffTime = trialEnd.getTime() - now.getTime();\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  \n  return Math.max(0, diffDays);\n}\n\n/**\n * Formats trial end date for display\n */\nexport function formatTrialEndDate(user: User): string {\n  if (!user.trialEndDate) {\n    return '';\n  }\n\n  return new Date(user.trialEndDate).toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\n/**\n * Checks if user can access premium features\n */\nexport function canAccessPremiumFeatures(user: User): boolean {\n  // Admins always have access\n  if (user.role === 'admin') {\n    return true;\n  }\n\n  // Users with active subscriptions have access\n  if (user.subscription && user.subscription.status === 'active') {\n    return true;\n  }\n\n  // Trial users have access if trial hasn't expired\n  if (user.isTrialUser && !isTrialExpired(user)) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Gets trial status message for UI display\n */\nexport function getTrialStatusMessage(user: User): {\n  message: string;\n  type: 'info' | 'warning' | 'error';\n  daysRemaining: number;\n} {\n  if (!user.isTrialUser) {\n    return {\n      message: '',\n      type: 'info',\n      daysRemaining: 0\n    };\n  }\n\n  const daysRemaining = getTrialDaysRemaining(user);\n  \n  if (daysRemaining <= 0) {\n    return {\n      message: 'Your trial has expired. Upgrade to continue using premium features.',\n      type: 'error',\n      daysRemaining: 0\n    };\n  }\n\n  if (daysRemaining <= 3) {\n    return {\n      message: `Your trial expires in ${daysRemaining} day${daysRemaining === 1 ? '' : 's'}. Upgrade now to continue.`,\n      type: 'warning',\n      daysRemaining\n    };\n  }\n\n  return {\n    message: `Your trial expires in ${daysRemaining} days on ${formatTrialEndDate(user)}.`,\n    type: 'info',\n    daysRemaining\n  };\n}\n\n/**\n * Creates user data with trial information for new registrations\n */\nexport function createNewUserWithTrial(email: string, additionalData: Partial<User> = {}): Omit<User, 'id'> {\n  const now = new Date();\n  const trialData = createTrialData();\n\n  return {\n    email,\n    role: 'user' as const,\n    isActive: true,\n    createdAt: now,\n    ...trialData,\n    ...additionalData\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAGO,MAAM,sBAAsB;AAK5B,SAAS;IACd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI,KAAK;IAC9B,aAAa,OAAO,CAAC,IAAI,OAAO,KAAK;IAErC,OAAO;QACL,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,cAAc;IAChB;AACF;AAKO,SAAS,eAAe,IAAU;IACvC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,YAAY,EAAE;QAC3C,OAAO;IACT;IAEA,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK,KAAK,YAAY;IAC3C,OAAO,MAAM;AACf;AAKO,SAAS,sBAAsB,IAAU;IAC9C,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,YAAY,EAAE;QAC3C,OAAO;IACT;IAEA,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK,KAAK,YAAY;IAC3C,MAAM,WAAW,SAAS,OAAO,KAAK,IAAI,OAAO;IACjD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE1D,OAAO,KAAK,GAAG,CAAC,GAAG;AACrB;AAKO,SAAS,mBAAmB,IAAU;IAC3C,IAAI,CAAC,KAAK,YAAY,EAAE;QACtB,OAAO;IACT;IAEA,OAAO,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,CAAC,SAAS;QAC7D,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,yBAAyB,IAAU;IACjD,4BAA4B;IAC5B,IAAI,KAAK,IAAI,KAAK,SAAS;QACzB,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,KAAK,UAAU;QAC9D,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,KAAK,WAAW,IAAI,CAAC,eAAe,OAAO;QAC7C,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,sBAAsB,IAAU;IAK9C,IAAI,CAAC,KAAK,WAAW,EAAE;QACrB,OAAO;YACL,SAAS;YACT,MAAM;YACN,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,sBAAsB;IAE5C,IAAI,iBAAiB,GAAG;QACtB,OAAO;YACL,SAAS;YACT,MAAM;YACN,eAAe;QACjB;IACF;IAEA,IAAI,iBAAiB,GAAG;QACtB,OAAO;YACL,SAAS,CAAC,sBAAsB,EAAE,cAAc,IAAI,EAAE,kBAAkB,IAAI,KAAK,IAAI,0BAA0B,CAAC;YAChH,MAAM;YACN;QACF;IACF;IAEA,OAAO;QACL,SAAS,CAAC,sBAAsB,EAAE,cAAc,SAAS,EAAE,mBAAmB,MAAM,CAAC,CAAC;QACtF,MAAM;QACN;IACF;AACF;AAKO,SAAS,uBAAuB,KAAa,EAAE,iBAAgC,CAAC,CAAC;IACtF,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY;IAElB,OAAO;QACL;QACA,MAAM;QACN,UAAU;QACV,WAAW;QACX,GAAG,SAAS;QACZ,GAAG,cAAc;IACnB;AACF", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport {\n  User as FirebaseUser,\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  GoogleAuthProvider,\n  signInWithPopup,\n  FacebookAuthProvider\n} from 'firebase/auth';\nimport { auth, db } from '@/lib/firebase/config';\nimport { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';\nimport { User } from '@/types';\nimport { createNewUserWithTrial, isTrialExpired, canAccessPremiumFeatures } from '@/lib/trial';\n\ninterface AuthContextType {\n  user: User | null;\n  firebaseUser: FirebaseUser | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string) => Promise<void>;\n  logOut: () => Promise<void>;\n  signInWithGoogle: () => Promise<void>;\n  signInWithFacebook: () => Promise<void>;\n  isAdmin: boolean;\n  canAccessPremiumFeatures: boolean;\n  isTrialExpired: boolean;\n  refreshUserData: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | null>(null);\n\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [canAccessPremium, setCanAccessPremium] = useState(false);\n  const [trialExpired, setTrialExpired] = useState(false);\n\n  const refreshUserData = async () => {\n    if (!firebaseUser) return;\n\n    try {\n      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));\n      if (userDoc.exists()) {\n        const userData = { id: userDoc.id, ...userDoc.data() } as User;\n\n        // Update trial status if needed\n        const expired = isTrialExpired(userData);\n        if (expired && !userData.trialExpired) {\n          await updateDoc(doc(db, 'users', firebaseUser.uid), {\n            trialExpired: true,\n            updatedAt: new Date()\n          });\n          userData.trialExpired = true;\n        }\n\n        setUser(userData);\n        setIsAdmin(userData.role === 'admin');\n        setCanAccessPremium(canAccessPremiumFeatures(userData));\n        setTrialExpired(expired);\n      }\n    } catch (error) {\n      console.error('Error refreshing user data:', error);\n    }\n  };\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\n      setFirebaseUser(firebaseUser);\n\n      if (firebaseUser) {\n        await refreshUserData();\n      } else {\n        setUser(null);\n        setIsAdmin(false);\n        setCanAccessPremium(false);\n        setTrialExpired(false);\n      }\n\n      setLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  // Update user data when firebaseUser changes\n  useEffect(() => {\n    if (firebaseUser) {\n      refreshUserData();\n    }\n  }, [firebaseUser]);\n\n  const signIn = async (email: string, password: string) => {\n    await signInWithEmailAndPassword(auth, email, password);\n  };\n\n  const signUp = async (email: string, password: string) => {\n    const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n\n    // Create user document in Firestore with trial data\n    const newUserData = createNewUserWithTrial(userCredential.user.email || email);\n    await setDoc(doc(db, 'users', userCredential.user.uid), newUserData);\n  };\n\n  const logOut = async () => {\n    await signOut(auth);\n  };\n\n  const signInWithGoogle = async () => {\n    const provider = new GoogleAuthProvider();\n    const userCredential = await signInWithPopup(auth, provider);\n\n    // Check if user exists in Firestore, if not create a new document with trial\n    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));\n\n    if (!userDoc.exists()) {\n      const newUserData = createNewUserWithTrial(\n        userCredential.user.email || '',\n        {\n          firstName: userCredential.user.displayName?.split(' ')[0],\n          lastName: userCredential.user.displayName?.split(' ').slice(1).join(' ')\n        }\n      );\n      await setDoc(doc(db, 'users', userCredential.user.uid), newUserData);\n    }\n  };\n\n  const signInWithFacebook = async () => {\n    const provider = new FacebookAuthProvider();\n    const userCredential = await signInWithPopup(auth, provider);\n\n    // Check if user exists in Firestore, if not create a new document with trial\n    const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));\n\n    if (!userDoc.exists()) {\n      const newUserData = createNewUserWithTrial(\n        userCredential.user.email || '',\n        {\n          firstName: userCredential.user.displayName?.split(' ')[0],\n          lastName: userCredential.user.displayName?.split(' ').slice(1).join(' ')\n        }\n      );\n      await setDoc(doc(db, 'users', userCredential.user.uid), newUserData);\n    }\n  };\n\n  return (\n    <AuthContext.Provider value={{\n      user,\n      firebaseUser,\n      loading,\n      signIn,\n      signUp,\n      logOut,\n      signInWithGoogle,\n      signInWithFacebook,\n      isAdmin,\n      canAccessPremiumFeatures: canAccessPremium,\n      isTrialExpired: trialExpired,\n      refreshUserData\n    }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAAA;AAEA;AAhBA;;;;;;;AAiCA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA0B;AAEnD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,aAAa,GAAG;YAC9D,IAAI,QAAQ,MAAM,IAAI;gBACpB,MAAM,WAAW;oBAAE,IAAI,QAAQ,EAAE;oBAAE,GAAG,QAAQ,IAAI,EAAE;gBAAC;gBAErD,gCAAgC;gBAChC,MAAM,UAAU,CAAA,GAAA,4GAAA,CAAA,iBAAc,AAAD,EAAE;gBAC/B,IAAI,WAAW,CAAC,SAAS,YAAY,EAAE;oBACrC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,aAAa,GAAG,GAAG;wBAClD,cAAc;wBACd,WAAW,IAAI;oBACjB;oBACA,SAAS,YAAY,GAAG;gBAC1B;gBAEA,QAAQ;gBACR,WAAW,SAAS,IAAI,KAAK;gBAC7B,oBAAoB,CAAA,GAAA,4GAAA,CAAA,2BAAwB,AAAD,EAAE;gBAC7C,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,OAAO;YAClD,gBAAgB;YAEhB,IAAI,cAAc;gBAChB,MAAM;YACR,OAAO;gBACL,QAAQ;gBACR,WAAW;gBACX,oBAAoB;gBACpB,gBAAgB;YAClB;YAEA,WAAW;QACb;QAEA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,OAAO;IAChD;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,OAAO;QAEzE,oDAAoD;QACpD,MAAM,cAAc,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,IAAI,CAAC,KAAK,IAAI;QACxE,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;IAC1D;IAEA,MAAM,SAAS;QACb,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,yHAAA,CAAA,OAAI;IACpB;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,IAAI,wNAAA,CAAA,qBAAkB;QACvC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE;QAEnD,6EAA6E;QAC7E,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG;QAErE,IAAI,CAAC,QAAQ,MAAM,IAAI;YACrB,MAAM,cAAc,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD,EACvC,eAAe,IAAI,CAAC,KAAK,IAAI,IAC7B;gBACE,WAAW,eAAe,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE;gBACzD,UAAU,eAAe,IAAI,CAAC,WAAW,EAAE,MAAM,KAAK,MAAM,GAAG,KAAK;YACtE;YAEF,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;QAC1D;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,WAAW,IAAI,0NAAA,CAAA,uBAAoB;QACzC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE;QAEnD,6EAA6E;QAC7E,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG;QAErE,IAAI,CAAC,QAAQ,MAAM,IAAI;YACrB,MAAM,cAAc,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD,EACvC,eAAe,IAAI,CAAC,KAAK,IAAI,IAC7B;gBACE,WAAW,eAAe,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,EAAE;gBACzD,UAAU,eAAe,IAAI,CAAC,WAAW,EAAE,MAAM,KAAK,MAAM,GAAG,KAAK;YACtE;YAEF,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,eAAe,IAAI,CAAC,GAAG,GAAG;QAC1D;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,0BAA0B;YAC1B,gBAAgB;YAChB;QACF;kBACG;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | null>(null);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\n// Safe version that returns null instead of throwing\nexport const useThemeSafe = () => {\n  const context = useContext(ThemeContext);\n  return context;\n};\n\nexport const ThemeProvider = ({ children }: { children: React.ReactNode }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Handle mounting to prevent hydration mismatch\n  useEffect(() => {\n    setMounted(true);\n\n    // Get theme from localStorage or system preference\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      setTheme(savedTheme);\n      applyTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      const initialTheme = systemPrefersDark ? 'dark' : 'light';\n      setTheme(initialTheme);\n      applyTheme(initialTheme);\n    }\n  }, []);\n\n  // Apply theme to document\n  const applyTheme = (newTheme: Theme) => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const toggleTheme = () => {\n    const newTheme = theme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n    applyTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return <>{children}</>;\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAWA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA2B;AAErD,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,MAAM,eAAe;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,OAAO;AACT;AAEO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,mDAAmD;QACnD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,SAAS;YACT,WAAW;QACb,OAAO;YACL,0BAA0B;YAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;YACnF,MAAM,eAAe,oBAAoB,SAAS;YAClD,SAAS;YACT,WAAW;QACb;IACF,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,UAAU,SAAS;QAC9C,SAAS;QACT,WAAW;QACX,aAAa,OAAO,CAAC,SAAS;IAChC;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP", "debugId": null}}]}