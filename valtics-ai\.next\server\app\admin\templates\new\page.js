(()=>{var e={};e.id=843,e.ids=[843],e.modules={1864:(e,r,t)=>{Promise.resolve().then(t.bind(t,97588))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(r,d);let o={children:["",{children:["admin",{children:["templates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97588)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/templates/new/page",pathname:"/admin/templates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66162:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var a=t(60687),s=t(43210),i=t(51108),n=t(16189),l=t(85814),d=t.n(l),o=t(75535),c=t(56304),u=t(5481),m=t(49615),x=t(30457);function g(){let{user:e,loading:r,isAdmin:t}=(0,i.A)(),l=(0,n.useRouter)(),[g,p]=(0,s.useState)([]),[b,h]=(0,s.useState)(!0),[y,v]=(0,s.useState)(!1),[f,k]=(0,s.useState)(!1),[j,w]=(0,s.useState)(!0),[N,C]=(0,s.useState)({brandId:"",solutionProviderName:"",solutionName:"",solutionDescription:"",templateVersion:"1.0",templateVersionDate:new Date().toISOString().split("T")[0],category:"Business Analysis",price:0}),[P,q]=(0,s.useState)({name:"",description:"",logoUrl:""}),D=async()=>{try{h(!0);let e=(0,o.P)((0,o.rJ)(c.db,"brands"),(0,o._M)("isActive","==",!0)),r=(await (0,o.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));p(r)}catch(e){console.error("Error fetching brands:",e)}finally{h(!1)}},S=(e,r)=>{if(C(t=>({...t,[e]:r})),"brandId"===e&&r){let e=g.find(e=>e.id===r);e&&C(r=>({...r,solutionProviderName:e.name}))}},A=async e=>{e.preventDefault();try{let e=await (0,o.gS)((0,o.rJ)(c.db,"brands"),{...P,isActive:!0,createdAt:o.Dc.now()});await D(),C(r=>({...r,brandId:e.id,solutionProviderName:P.name})),q({name:"",description:"",logoUrl:""}),v(!1),alert("Brand created successfully!")}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},E=async e=>{if(e.preventDefault(),!N.brandId||!N.solutionName||!N.solutionDescription)return void alert("Please fill in all required fields.");try{k(!0);let e={name:N.solutionName,description:N.solutionDescription,brandId:N.brandId,category:N.category,price:N.price,solutionProviderName:N.solutionProviderName,solutionName:N.solutionName,solutionDescription:N.solutionDescription,templateVersion:N.templateVersion,templateVersionDate:o.Dc.fromDate(new Date(N.templateVersionDate)),status:"draft",step1Completed:!0,step2Completed:!1,step3Completed:!1,isActive:!1,fileUrls:{},createdAt:o.Dc.now(),updatedAt:o.Dc.now()},r=await (0,o.gS)((0,o.rJ)(c.db,"templates"),e);l.push(`/admin/templates/${r.id}/edit`)}catch(e){console.error("Error creating template:",e),alert("Error creating template. Please try again.")}finally{k(!1)}};return r||b?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(u.A,{}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create New Template"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Step 1: Basic Information"})]}),(0,a.jsx)(d(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Back to Admin"})]})}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex items-center text-blue-600 dark:text-blue-400",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full text-sm font-medium",children:"1"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Basic Information"})]}),(0,a.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"flex items-center text-gray-400 dark:text-gray-500",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-sm font-medium",children:"2"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Documents"})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:E,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Company/Brand *"}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("select",{value:N.brandId,onChange:e=>S("brandId",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a brand..."}),g.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,a.jsx)("button",{type:"button",onClick:()=>v(!0),className:"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 whitespace-nowrap",children:"Create Brand"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Provider Name *"}),(0,a.jsx)("input",{type:"text",value:N.solutionProviderName,onChange:e=>S("solutionProviderName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter solution provider name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Name *"}),(0,a.jsx)("input",{type:"text",value:N.solutionName,onChange:e=>S("solutionName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter solution name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Solution Description *"}),(0,a.jsx)("button",{type:"button",onClick:()=>w(!j),className:"text-xs text-blue-600 dark:text-blue-400 hover:underline",children:j?"Try Advanced Editor":"Use Simple Editor"})]}),j?(0,a.jsx)(x.A,{value:N.solutionDescription,onChange:e=>S("solutionDescription",e),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md"}):(0,a.jsx)(m.A,{value:N.solutionDescription,onChange:e=>S("solutionDescription",e),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version *"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:N.templateVersion,onChange:e=>S("templateVersion",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 1.0",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>{let e=new Date,r=`${e.getFullYear()}.${(e.getMonth()+1).toString().padStart(2,"0")}.${e.getDate().toString().padStart(2,"0")}`;C(e=>({...e,templateVersion:r}))},className:"bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 whitespace-nowrap",children:"Auto Generate"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version Date *"}),(0,a.jsx)("input",{type:"date",value:N.templateVersionDate,onChange:e=>S("templateVersionDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:N.category,onChange:e=>S("category",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"Business Analysis",children:"Business Analysis"}),(0,a.jsx)("option",{value:"Financial Planning",children:"Financial Planning"}),(0,a.jsx)("option",{value:"Technology Assessment",children:"Technology Assessment"}),(0,a.jsx)("option",{value:"Risk Management",children:"Risk Management"}),(0,a.jsx)("option",{value:"Strategic Planning",children:"Strategic Planning"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Price ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:N.price,onChange:e=>S("price",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)(d(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",disabled:f,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[f&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:f?"Creating Template...":"Continue to Step 2"})]})]})]})})]})}),y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Create New Brand"}),(0,a.jsxs)("form",{onSubmit:A,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Brand Name *"}),(0,a.jsx)("input",{type:"text",value:P.name,onChange:e=>q({...P,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:P.description,onChange:e=>q({...P,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:P.logoUrl,onChange:e=>q({...P,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Create Brand"}),(0,a.jsx)("button",{type:"button",onClick:()=>v(!1),className:"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Cancel"})]})]})]})})})]}):null}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88712:(e,r,t)=>{Promise.resolve().then(t.bind(t,66162))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97588:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\templates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\new\\page.tsx","default")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,823,567,533,111,77,948],()=>t(34328));module.exports=a})();