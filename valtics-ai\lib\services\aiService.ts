import Anthropic from '@anthropic-ai/sdk';
import OpenAI from 'openai';
import { DocumentFile } from '../utils/fileProcessor';

export interface AIResponse {
  enterpriseNeedArtifact: string;
  solutionArtifact: string;
  riskArtifact: string;
}

export interface DocumentContext {
  enterpriseNeed: string;
  solution: string;
  risk: string;
}

export interface DocumentContextWithFiles {
  enterpriseNeedFile: DocumentFile;
  solutionFile: DocumentFile;
  riskFile: DocumentFile;
}

export class AIService {
  private static claudeClient: Anthropic | null = null;
  private static openaiClient: OpenAI | null = null;

  /**
   * Initialize Claude client
   */
  static initializeClaude(apiKey: string): void {
    this.claudeClient = new Anthropic({
      apiKey: apiKey,
    });
  }

  /**
   * Initialize OpenAI client
   */
  static initializeOpenAI(apiKey: string): void {
    this.openaiClient = new OpenAI({
      apiKey: apiKey,
    });
  }

  /**
   * Test Claude API connection
   */
  static async testClaudeConnection(apiKey: string): Promise<boolean> {
    try {
      const client = new Anthropic({ apiKey });
      
      const response = await client.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 10,
        messages: [
          {
            role: 'user',
            content: 'Test connection. Respond with "OK".'
          }
        ]
      });

      return response.content[0].type === 'text' && response.content[0].text.includes('OK');
    } catch (error) {
      console.error('Claude connection test failed:', error);
      return false;
    }
  }

  /**
   * Test OpenAI API connection
   */
  static async testOpenAIConnection(apiKey: string): Promise<boolean> {
    try {
      const client = new OpenAI({ apiKey });
      
      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        max_tokens: 10,
        messages: [
          {
            role: 'user',
            content: 'Test connection. Respond with "OK".'
          }
        ]
      });

      return response.choices[0]?.message?.content?.includes('OK') || false;
    } catch (error) {
      console.error('OpenAI connection test failed:', error);
      return false;
    }
  }

  /**
   * Generate artifacts using Claude
   */
  static async generateWithClaude(
    prompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.claudeClient) {
      throw new Error('Claude client not initialized');
    }

    try {
      const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);

      const response = await this.claudeClient.messages.create({
        model: 'claude-3-5-sonnet-latest',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });

      if (response.content[0].type !== 'text') {
        throw new Error('Unexpected response format from Claude');
      }

      return this.parseAIResponse(response.content[0].text);
    } catch (error) {
      console.error('Claude generation failed:', error);
      throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts using OpenAI
   */
  static async generateWithOpenAI(
    prompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const fullPrompt = this.buildFullPrompt(prompt, documentContext, solutionName, companyName);

      const response = await this.openaiClient.chat.completions.create({
        model: 'gpt-4',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'system',
            content: 'You are an expert business analyst creating professional Business Value Assessment artifacts.'
          },
          {
            role: 'user',
            content: fullPrompt
          }
        ]
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      return this.parseAIResponse(content);
    } catch (error) {
      console.error('OpenAI generation failed:', error);
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the full prompt with document context
   */
  private static buildFullPrompt(
    basePrompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string
  ): string {
    return `${basePrompt}

DOCUMENT CONTENTS:

Enterprise Need Document:
${documentContext.enterpriseNeed}

Solution Description Document:
${documentContext.solution}

Risk of No Investment Document:
${documentContext.risk}

Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).`;
  }

  /**
   * Parse AI response into structured artifacts
   */
  private static parseAIResponse(response: string): AIResponse {
    // Try to split the response into three artifacts
    const sections = response.split(/(?=#{1,3}\s*(?:Enterprise Need|Proposed Solution|Risk of No Investment))/i);
    
    let enterpriseNeedArtifact = '';
    let solutionArtifact = '';
    let riskArtifact = '';

    for (const section of sections) {
      const trimmedSection = section.trim();
      if (!trimmedSection) continue;

      if (trimmedSection.toLowerCase().includes('enterprise need')) {
        enterpriseNeedArtifact = trimmedSection;
      } else if (trimmedSection.toLowerCase().includes('proposed solution')) {
        solutionArtifact = trimmedSection;
      } else if (trimmedSection.toLowerCase().includes('risk of no investment')) {
        riskArtifact = trimmedSection;
      }
    }

    // If parsing failed, try alternative approach
    if (!enterpriseNeedArtifact || !solutionArtifact || !riskArtifact) {
      const parts = response.split(/\n\s*\n/);
      if (parts.length >= 3) {
        enterpriseNeedArtifact = parts[0] || 'Enterprise Need artifact could not be parsed.';
        solutionArtifact = parts[1] || 'Solution artifact could not be parsed.';
        riskArtifact = parts[2] || 'Risk artifact could not be parsed.';
      } else {
        // Fallback: use the entire response for each artifact with appropriate headers
        const fallbackContent = response;
        enterpriseNeedArtifact = `# Enterprise Need Artifact\n\n${fallbackContent}`;
        solutionArtifact = `# Proposed Solution Artifact\n\n${fallbackContent}`;
        riskArtifact = `# Risk of No Investment Artifact\n\n${fallbackContent}`;
      }
    }

    return {
      enterpriseNeedArtifact: enterpriseNeedArtifact.trim(),
      solutionArtifact: solutionArtifact.trim(),
      riskArtifact: riskArtifact.trim()
    };
  }

  /**
   * Generate artifacts using Claude with direct file attachments
   */
  static async generateWithClaudeFiles(
    prompt: string,
    documentFiles: DocumentContextWithFiles,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.claudeClient) {
      throw new Error('Claude client not initialized');
    }

    try {
      // Build content array with direct file attachments
      const content: any[] = [];

      // Helper function to add document content
      const addDocumentContent = (file: DocumentFile, documentName: string) => {
        const fileType = file.fileType.toLowerCase();

        if (fileType === 'pdf') {
          // Use document attachment for PDF files
          content.push({
            type: 'document',
            source: {
              type: 'base64',
              media_type: 'application/pdf',
              data: file.base64Data
            }
          });
          content.push({
            type: 'text',
            text: `The above document contains the ${documentName} information.`
          });
        }
        else if (['png', 'jpg', 'jpeg'].includes(fileType)) {
          // Use image attachment for image files
          const mimeType = fileType === 'jpg' ? 'image/jpeg' : `image/${fileType}`;
          content.push({
            type: 'image',
            source: {
              type: 'base64',
              media_type: mimeType,
              data: file.base64Data
            }
          });
          content.push({
            type: 'text',
            text: `The above image contains the ${documentName} information.`
          });
        }
        else {
          // For other file types, use document attachment with appropriate MIME type
          content.push({
            type: 'document',
            source: {
              type: 'base64',
              media_type: this.getMimeType(fileType),
              data: file.base64Data
            }
          });
          content.push({
            type: 'text',
            text: `The above document contains the ${documentName} information.`
          });
        }
      };

      // Add each document
      addDocumentContent(documentFiles.enterpriseNeedFile, 'Enterprise Need');
      addDocumentContent(documentFiles.solutionFile, 'Solution Description');
      addDocumentContent(documentFiles.riskFile, 'Risk of No Investment');

      // Add the text prompt
      content.push({
        type: 'text',
        text: `${prompt}

Please analyze the three attached documents and generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

The first document contains the Enterprise Need information.
The second document contains the Solution Description information.
The third document contains the Risk of No Investment information.

Use only the content from these attached documents and do not add any other information other than the items requested in this prompt.`
      });

      const response = await this.claudeClient.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'user',
            content
          }
        ]
      });

      if (response.content[0].type !== 'text') {
        throw new Error('Unexpected response format from Claude');
      }

      return this.parseAIResponse(response.content[0].text);
    } catch (error) {
      console.error('Claude file generation failed:', error);
      throw new Error(`Claude API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts using OpenAI with file attachments
   * Uses GPT-4 Vision for images and includes file content for other types
   */
  static async generateWithOpenAIFiles(
    prompt: string,
    documentFiles: DocumentContextWithFiles,
    solutionName: string,
    companyName: string
  ): Promise<AIResponse> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      // Build content array for OpenAI
      const content: any[] = [];

      // Add the main prompt first
      content.push({
        type: 'text',
        text: `${prompt}

I have three documents that I need you to analyze. Please generate exactly three separate artifacts as requested. Each artifact should be formatted as markdown and clearly separated. Start each artifact with a clear header indicating which one it is (Enterprise Need, Proposed Solution, or Risk of No Investment).

Documents to analyze:`
      });

      // Helper function to add document content for OpenAI
      const addDocumentContentOpenAI = (file: DocumentFile, documentName: string) => {
        const fileType = file.fileType.toLowerCase();

        if (['png', 'jpg', 'jpeg'].includes(fileType)) {
          // Use image attachment for image files with GPT-4 Vision
          content.push({
            type: 'text',
            text: `\n${documentName} Document (${file.fileName}):`
          });
          content.push({
            type: 'image_url',
            image_url: {
              url: `data:image/${fileType === 'jpg' ? 'jpeg' : fileType};base64,${file.base64Data}`
            }
          });
        }
        else {
          // For non-image files, include filename and note about content
          content.push({
            type: 'text',
            text: `\n${documentName} Document: ${file.fileName} (${fileType.toUpperCase()} file - please note that the content extraction may be limited for this file type)`
          });
        }
      };

      // Add each document
      addDocumentContentOpenAI(documentFiles.enterpriseNeedFile, 'Enterprise Need');
      addDocumentContentOpenAI(documentFiles.solutionFile, 'Solution Description');
      addDocumentContentOpenAI(documentFiles.riskFile, 'Risk of No Investment');

      const response = await this.openaiClient.chat.completions.create({
        model: 'gpt-4-vision-preview',
        max_tokens: 4000,
        temperature: 0.7,
        messages: [
          {
            role: 'system',
            content: 'You are an expert business analyst creating professional Business Value Assessment artifacts. You can analyze both text and image content to create comprehensive business documents.'
          },
          {
            role: 'user',
            content: content
          }
        ]
      });

      const responseContent = response.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error('No content received from OpenAI');
      }

      return this.parseAIResponse(responseContent);
    } catch (error) {
      console.error('OpenAI file generation failed:', error);
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate artifacts with retry logic using file attachments
   */
  static async generateArtifactsWithFilesRetry(
    llmType: 'claude' | 'openai',
    prompt: string,
    documentFiles: DocumentContextWithFiles,
    solutionName: string,
    companyName: string,
    maxRetries: number = 3
  ): Promise<AIResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (llmType === 'claude') {
          return await this.generateWithClaudeFiles(prompt, documentFiles, solutionName, companyName);
        } else {
          return await this.generateWithOpenAIFiles(prompt, documentFiles, solutionName, companyName);
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`AI file generation attempt ${attempt} failed:`, lastError);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw new Error(`AI file generation failed after ${maxRetries} attempts: ${lastError?.message}`);
  }

  /**
   * Get the MIME type for a file based on its extension
   */
  private static getMimeType(fileExtension: string): string {
    const mimeTypes: { [key: string]: string } = {
      'pdf': 'application/pdf',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'doc': 'application/msword',
      'txt': 'text/plain',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg'
    };

    return mimeTypes[fileExtension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Generate artifacts with retry logic
   */
  static async generateArtifactsWithRetry(
    llmType: 'claude' | 'openai',
    prompt: string,
    documentContext: DocumentContext,
    solutionName: string,
    companyName: string,
    maxRetries: number = 3
  ): Promise<AIResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (llmType === 'claude') {
          return await this.generateWithClaude(prompt, documentContext, solutionName, companyName);
        } else {
          return await this.generateWithOpenAI(prompt, documentContext, solutionName, companyName);
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`AI generation attempt ${attempt} failed:`, lastError);
        
        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw new Error(`AI generation failed after ${maxRetries} attempts: ${lastError?.message}`);
  }
}
