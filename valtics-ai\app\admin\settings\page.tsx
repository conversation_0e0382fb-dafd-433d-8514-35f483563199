'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Navigation from '@/components/Navigation';
import { APIConfigService } from '@/lib/services/apiConfigService';
import { APIConfiguration, LLMModelConfiguration } from '@/types';

export default function AdminSettings() {
  const { user, loading, isAdmin } = useAuth();
  const router = useRouter();

  const [apiConfig, setApiConfig] = useState<APIConfiguration | null>(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [showApiKeys, setShowApiKeys] = useState(false);

  const [formData, setFormData] = useState({
    claudeApiKey: '',
    openaiApiKey: '',
    claudeModelConfig: {
      model: 'claude-3-5-sonnet-20241022',
      maxTokens: 4000,
      temperature: 0.7
    } as LLMModelConfiguration,
    openaiModelConfig: {
      model: 'gpt-4-vision-preview',
      maxTokens: 4000,
      temperature: 0.7
    } as LLMModelConfiguration
  });

  const [testResults, setTestResults] = useState<{
    claudeStatus?: 'connected' | 'error' | 'untested';
    openaiStatus?: 'connected' | 'error' | 'untested';
  }>({});

  useEffect(() => {
    if (!loading && !isAdmin) {
      router.push('/');
      return;
    }
    if (isAdmin) {
      fetchAPIConfig();
    }
  }, [loading, isAdmin, router]);

  const fetchAPIConfig = async () => {
    try {
      setLoadingConfig(true);
      const config = await APIConfigService.getAPIConfiguration();
      setApiConfig(config);
      
      if (config) {
        setFormData({
          claudeApiKey: config.claudeApiKey || '',
          openaiApiKey: config.openaiApiKey || '',
          claudeModelConfig: config.claudeModelConfig || {
            model: 'claude-3-5-sonnet-20241022',
            maxTokens: 4000,
            temperature: 0.7
          },
          openaiModelConfig: config.openaiModelConfig || {
            model: 'gpt-4-vision-preview',
            maxTokens: 4000,
            temperature: 0.7
          }
        });
        setTestResults({
          claudeStatus: config.claudeStatus,
          openaiStatus: config.openaiStatus
        });
      }
    } catch (error) {
      console.error('Error fetching API config:', error);
      alert('Error loading API configuration');
    } finally {
      setLoadingConfig(false);
    }
  };

  const handleSaveConfig = async () => {
    try {
      setSaving(true);

      // Validate API keys
      if (formData.claudeApiKey) {
        const claudeValidation = APIConfigService.validateAPIKey('claude', formData.claudeApiKey);
        if (!claudeValidation.isValid) {
          alert(`Claude API Key Error: ${claudeValidation.error}`);
          return;
        }
      }

      if (formData.openaiApiKey) {
        const openaiValidation = APIConfigService.validateAPIKey('openai', formData.openaiApiKey);
        if (!openaiValidation.isValid) {
          alert(`OpenAI API Key Error: ${openaiValidation.error}`);
          return;
        }
      }

      await APIConfigService.saveAPIConfiguration({
        claudeApiKey: formData.claudeApiKey || undefined,
        openaiApiKey: formData.openaiApiKey || undefined,
        claudeModelConfig: formData.claudeModelConfig,
        openaiModelConfig: formData.openaiModelConfig
      });

      alert('API configuration saved successfully!');
      await fetchAPIConfig(); // Refresh data

    } catch (error) {
      console.error('Error saving API config:', error);
      alert('Error saving API configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleTestConnections = async () => {
    try {
      setTesting(true);
      const results = await APIConfigService.testAPIConnections();
      setTestResults(results);

      const messages = [];
      if (results.claudeStatus === 'connected') {
        messages.push('✅ Claude API: Connected successfully');
      } else if (results.claudeStatus === 'error') {
        messages.push(`❌ Claude API: ${results.claudeError || 'Connection failed'}`);
      } else {
        messages.push('⚪ Claude API: Not configured');
      }

      if (results.openaiStatus === 'connected') {
        messages.push('✅ OpenAI API: Connected successfully');
      } else if (results.openaiStatus === 'error') {
        messages.push(`❌ OpenAI API: ${results.openaiError || 'Connection failed'}`);
      } else {
        messages.push('⚪ OpenAI API: Not configured');
      }

      alert('API Connection Test Results:\n\n' + messages.join('\n'));

    } catch (error) {
      console.error('Error testing connections:', error);
      alert('Error testing API connections. Please try again.');
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status?: 'connected' | 'error' | 'untested') => {
    switch (status) {
      case 'connected':
        return <span className="text-green-500">✓ Connected</span>;
      case 'error':
        return <span className="text-red-500">✗ Error</span>;
      case 'untested':
      default:
        return <span className="text-gray-500">○ Untested</span>;
    }
  };

  if (loading || loadingConfig) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Admin Settings</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              Configure API keys and system settings for VALTICS AI.
            </p>
          </div>

          {/* API Configuration Section */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    AI API Configuration
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Configure API keys for Claude AI and OpenAI to enable AI artifact generation.
                  </p>
                </div>
                <button
                  onClick={() => setShowApiKeys(!showApiKeys)}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                >
                  {showApiKeys ? 'Hide' : 'Show'} API Keys
                </button>
              </div>

              <div className="space-y-6">
                {/* Claude API Key */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Claude AI API Key
                    </label>
                    {getStatusIcon(testResults.claudeStatus)}
                  </div>
                  <input
                    type={showApiKeys ? 'text' : 'password'}
                    value={formData.claudeApiKey}
                    onChange={(e) => setFormData(prev => ({ ...prev, claudeApiKey: e.target.value }))}
                    placeholder="sk-ant-..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Get your API key from <a href="https://console.anthropic.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">Anthropic Console</a>
                  </p>
                </div>

                {/* OpenAI API Key */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      OpenAI API Key
                    </label>
                    {getStatusIcon(testResults.openaiStatus)}
                  </div>
                  <input
                    type={showApiKeys ? 'text' : 'password'}
                    value={formData.openaiApiKey}
                    onChange={(e) => setFormData(prev => ({ ...prev, openaiApiKey: e.target.value }))}
                    placeholder="sk-..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">OpenAI Platform</a>
                  </p>
                </div>

                {/* Model Configuration Section */}
                <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Model Configuration
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                    Configure the AI models and parameters used for artifact generation.
                  </p>

                  {/* Claude Model Configuration */}
                  <div className="mb-6">
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                      Claude AI Configuration
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Model
                        </label>
                        <select
                          value={formData.claudeModelConfig.model}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            claudeModelConfig: { ...prev.claudeModelConfig, model: e.target.value }
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet (Latest)</option>
                          <option value="claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                          <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                          <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Max Tokens
                        </label>
                        <input
                          type="number"
                          min="100"
                          max="8000"
                          value={formData.claudeModelConfig.maxTokens}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            claudeModelConfig: { ...prev.claudeModelConfig, maxTokens: parseInt(e.target.value) }
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Temperature
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="1"
                          step="0.1"
                          value={formData.claudeModelConfig.temperature}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            claudeModelConfig: { ...prev.claudeModelConfig, temperature: parseFloat(e.target.value) }
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>

                  {/* OpenAI Model Configuration */}
                  <div className="mb-6">
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                      OpenAI Configuration
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Model
                        </label>
                        <select
                          value={formData.openaiModelConfig.model}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            openaiModelConfig: { ...prev.openaiModelConfig, model: e.target.value }
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="gpt-4-vision-preview">GPT-4 Vision Preview</option>
                          <option value="gpt-4-turbo-preview">GPT-4 Turbo Preview</option>
                          <option value="gpt-4">GPT-4</option>
                          <option value="gpt-4-32k">GPT-4 32K</option>
                          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Max Tokens
                        </label>
                        <input
                          type="number"
                          min="100"
                          max="8000"
                          value={formData.openaiModelConfig.maxTokens}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            openaiModelConfig: { ...prev.openaiModelConfig, maxTokens: parseInt(e.target.value) }
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Temperature
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="1"
                          step="0.1"
                          value={formData.openaiModelConfig.temperature}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            openaiModelConfig: { ...prev.openaiModelConfig, temperature: parseFloat(e.target.value) }
                          }))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={handleSaveConfig}
                    disabled={saving}
                    className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {saving && (
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    <span>{saving ? 'Saving...' : 'Save Configuration'}</span>
                  </button>

                  <button
                    onClick={handleTestConnections}
                    disabled={testing || (!formData.claudeApiKey && !formData.openaiApiKey)}
                    className="bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {testing && (
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    <span>{testing ? 'Testing...' : 'Test Connections'}</span>
                  </button>
                </div>

                {/* Last Tested Info */}
                {apiConfig?.lastTestedAt && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Last tested: {apiConfig.lastTestedAt.toLocaleString()}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Security Notice
                </h3>
                <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>
                    API keys are encrypted before storage and are only accessible to admin users. 
                    Keep your API keys secure and rotate them regularly. Never share your API keys with unauthorized users.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
