(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9],{573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(5155),s=r(2115),l=r(3274),i=r(5695),n=r(6874),o=r.n(n),d=r(5317),c=r(1138),m=r(1573),u=r(2547),x=r(1901);let p=e=>{let{onFileSelect:t,onFileRemove:r,currentFileUrl:l,currentFileName:i,accept:n=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png",maxSize:o=10,disabled:d=!1,className:c=""}=e,[m,u]=(0,s.useState)(!1),[x,p]=(0,s.useState)(!1),g=(0,s.useRef)(null),b=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?u(!0):"dragleave"===e.type&&u(!1)},h=e=>{var r;if(e.size>1024*o*1024)return void alert("File size must be less than ".concat(o,"MB"));let a=n.split(",").map(e=>e.trim()),s="."+(null==(r=e.name.split(".").pop())?void 0:r.toLowerCase());if(!(a.some(t=>t===s||t.includes("/")&&e.type===t)||["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","image/jpeg","image/jpg","image/png"].includes(e.type)))return void alert("Please select a valid file type: ".concat(n));t(e)},y=e=>{var t;return[".jpg",".jpeg",".png"].includes("."+(null==(t=e.split(".").pop())?void 0:t.toLowerCase()))};return(0,a.jsxs)("div",{className:"file-upload ".concat(c),children:[(0,a.jsx)("input",{ref:g,type:"file",accept:n,onChange:e=>{let t=e.target.files;t&&t[0]&&h(t[0])},className:"hidden",disabled:d}),l||i?(0,a.jsx)("div",{className:"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-4 bg-green-50 dark:bg-green-900/20",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:i&&y(i)&&l?(0,a.jsxs)("div",{className:"w-16 h-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("img",{src:l,alt:i,className:"w-full h-full object-cover",onError:e=>{var t;let r=e.target;r.style.display="none",null==(t=r.nextElementSibling)||t.classList.remove("hidden")}}),(0,a.jsx)("svg",{className:"hidden h-16 w-16 text-green-600 dark:text-green-400 p-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})]}):(0,a.jsx)("svg",{className:"h-8 w-8 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:i||"File uploaded"}),(0,a.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400",children:i&&y(i)?"Image uploaded successfully":"Document uploaded successfully"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[l&&(0,a.jsx)("a",{href:l,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View"}),(0,a.jsx)("button",{type:"button",onClick:()=>{g.current&&(g.current.value=""),r()},disabled:d,className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium disabled:opacity-50",children:"Remove"})]})]})}):(0,a.jsx)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-colors ".concat(m?"border-blue-400 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"," ").concat(d?"opacity-50 cursor-not-allowed":"cursor-pointer"),onDragEnter:b,onDragLeave:b,onDragOver:b,onDrop:e=>{if(e.preventDefault(),e.stopPropagation(),u(!1),d)return;let t=e.dataTransfer.files;t&&t[0]&&h(t[0])},onClick:()=>{!d&&g.current&&g.current.click()},children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,a.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("span",{className:"font-medium text-blue-600 dark:text-blue-400",children:"Click to upload"})," ","or drag and drop"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Documents & Images (",n.replace(/\./g,"").toUpperCase(),") up to ",o,"MB"]})]})}),x&&(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400",children:[(0,a.jsxs)("svg",{className:"animate-spin h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:"Uploading..."})]})]})};var g=r(858);class b{static async uploadFile(e,t,r){try{let r=(0,g.KR)(c.IG,t),a=await (0,g.D)(r,e);return await (0,g.qk)(a.ref)}catch(e){throw console.error("Error uploading file:",e),Error("Failed to upload file")}}static async uploadTemplateDocument(e,t,r){let a=Date.now(),s=e.name.split(".").pop(),l="".concat(r,"_").concat(a,".").concat(s),i="templates/".concat(t,"/documents/").concat(l);return this.uploadFile(e,i)}static async deleteFile(e){try{let t=e.split("/"),r=t.findIndex(e=>"o"===e)+1;if(0===r)throw Error("Invalid Firebase Storage URL");let a=t[r].split("?")[0],s=decodeURIComponent(a),l=(0,g.KR)(c.IG,s);await (0,g.XR)(l)}catch(e){throw console.error("Error deleting file:",e),Error("Failed to delete file")}}static getFileNameFromUrl(e){try{let t=e.split("/"),r=t.findIndex(e=>"o"===e)+1;if(0===r)return"Unknown file";let a=t[r].split("?")[0],s=decodeURIComponent(a).split("/").pop()||"Unknown file",l=s.split("_");if(l.length>1&&!isNaN(Number(l[l.length-1].split(".")[0])))return l.slice(0,-1).join("_")+"."+s.split(".").pop();return s}catch(e){return console.error("Error parsing file name from URL:",e),"Unknown file"}}static async uploadAIArtifact(e,t,r){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Date.now();try{let s="".concat(r,"-").concat(a,".md"),l="templates/".concat(t,"/artifacts/").concat(s),i=new Blob([e],{type:"text/markdown"}),n=(0,g.KR)(c.IG,l),o=await (0,g.D)(n,i);return await (0,g.qk)(o.ref)}catch(e){throw console.error("Error uploading AI artifact:",e),Error("Failed to upload AI artifact")}}static async uploadAIArtifacts(e,t){try{let r=Date.now(),[a,s,l]=await Promise.all([this.uploadAIArtifact(e.enterpriseNeedArtifact,t,"enterprise-need",r),this.uploadAIArtifact(e.solutionArtifact,t,"solution",r),this.uploadAIArtifact(e.riskArtifact,t,"risk",r)]);return{enterpriseNeedArtifactUrl:a,solutionArtifactUrl:s,riskArtifactUrl:l}}catch(e){throw console.error("Error uploading AI artifacts:",e),Error("Failed to upload AI artifacts")}}static async deleteAIArtifacts(e){try{let t=(0,g.KR)(c.IG,"templates/".concat(e,"/artifacts/")),r=(await (0,g._w)(t)).items.map(e=>(0,g.XR)(e));await Promise.all(r)}catch(e){console.error("Error deleting AI artifacts:",e)}}static validateFile(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[".pdf",".doc",".docx",".txt",".jpg",".jpeg",".png"],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;if(e.size>1024*a*1024)return{isValid:!1,error:"File size must be less than ".concat(a,"MB")};let s="."+(null==(t=e.name.split(".").pop())?void 0:t.toLowerCase());return r.includes(s)?{isValid:!0}:{isValid:!1,error:"Please select a valid file type: ".concat(r.join(", "))}}}function h(){let{user:e,loading:t,isAdmin:r}=(0,l.A)(),n=(0,i.useRouter)(),g=(0,i.useParams)().id,[h,y]=(0,s.useState)(null),[f,v]=(0,s.useState)(null),[j,k]=(0,s.useState)(!0),[N,w]=(0,s.useState)(!1),[A,I]=(0,s.useState)("step2"),[D,C]=(0,s.useState)(!0),[F,U]=(0,s.useState)({enterpriseNeed:"",solutionDescriptionDocument:"",riskOfNoInvestment:"",enterpriseNeedFileUrl:"",solutionDescriptionFileUrl:"",riskOfNoInvestmentFileUrl:""}),[E,V]=(0,s.useState)("claude"),[S,P]=(0,s.useState)(""),[O,B]=(0,s.useState)(""),[R,z]=(0,s.useState)(!1),[H,L]=(0,s.useState)(null),[T,M]=(0,s.useState)({enterpriseNeed:!1,solutionDescription:!1,riskOfNoInvestment:!1}),[q,G]=(0,s.useState)({solutionProviderName:"",solutionName:"",solutionDescription:"",templateVersion:"",templateVersionDate:"",category:"",price:0});(0,s.useEffect)(()=>{if(!t&&!r)return void n.push("/");r&&g&&_()},[t,r,n,g]),(0,s.useEffect)(()=>{if(!g)return;let e=(0,d.aQ)((0,d.H9)(c.db,"templates",g),e=>{if(e.exists()){let t={id:e.id,...e.data()};if(y(t),"generating-artifacts"===t.status)z(!0);else if("draft"===t.status&&R)z(!1),t.generatedArtifacts&&(L(t.generatedArtifacts),B(t.aiPrompt||""),alert("AI artifacts generated successfully!"));else if("generation-failed"===t.status&&R){z(!1);let e=t.generationError||"AI generation failed";alert("AI generation failed: ".concat(e))}}});return()=>e()},[g,R]);let _=async()=>{try{k(!0);let e=await (0,d.x7)((0,d.H9)(c.db,"templates",g));if(e.exists()){let t={id:e.id,...e.data()};y(t),U({enterpriseNeed:t.enterpriseNeed||"",solutionDescriptionDocument:t.solutionDescriptionDocument||"",riskOfNoInvestment:t.riskOfNoInvestment||"",enterpriseNeedFileUrl:t.enterpriseNeedFileUrl||"",solutionDescriptionFileUrl:t.solutionDescriptionFileUrl||"",riskOfNoInvestmentFileUrl:t.riskOfNoInvestmentFileUrl||""}),G({solutionProviderName:t.solutionProviderName||"",solutionName:t.solutionName||"",solutionDescription:t.solutionDescription||"",templateVersion:t.templateVersion||"",templateVersionDate:t.templateVersionDate?t.templateVersionDate instanceof Date?t.templateVersionDate.toISOString().split("T")[0]:new Date(1e3*t.templateVersionDate.seconds).toISOString().split("T")[0]:"",category:t.category||"",price:t.price||0}),V(t.selectedLLM||"claude"),B(t.aiPrompt||""),L(t.generatedArtifacts||null),z("generating-artifacts"===t.status);let r=await (0,d.x7)((0,d.H9)(c.db,"brands",t.brandId));r.exists()&&v({id:r.id,...r.data()})}else n.push("/admin")}catch(e){console.error("Error fetching template data:",e),n.push("/admin")}finally{k(!1)}},W=async e=>{if(e.preventDefault(),h)try{w(!0),await (0,d.mZ)((0,d.H9)(c.db,"templates",g),{solutionProviderName:q.solutionProviderName,solutionName:q.solutionName,solutionDescription:q.solutionDescription,templateVersion:q.templateVersion,templateVersionDate:d.Dc.fromDate(new Date(q.templateVersionDate)),category:q.category,price:q.price,name:q.solutionName,description:q.solutionDescription,updatedAt:d.Dc.now()}),alert("Step 1 information updated successfully!"),await _()}catch(e){console.error("Error updating template:",e),alert("Error updating template. Please try again.")}finally{w(!1)}},Z=async(e,t)=>{if(h)try{M(e=>({...e,[t]:!0}));let r=await b.uploadTemplateDocument(e,g,t),a="".concat(t,"FileUrl");U(e=>({...e,[a]:r})),await (0,d.mZ)((0,d.H9)(c.db,"templates",g),{["".concat(t,"FileUrl")]:r,updatedAt:d.Dc.now()}),alert("File uploaded successfully!")}catch(e){console.error("Error uploading file:",e),alert("Error uploading file. Please try again.")}finally{M(e=>({...e,[t]:!1}))}},K=async e=>{if(h)try{let t="".concat(e,"FileUrl"),r=F[t];r&&await b.deleteFile(r),U(e=>({...e,[t]:""})),await (0,d.mZ)((0,d.H9)(c.db,"templates",g),{["".concat(e,"FileUrl")]:"",updatedAt:d.Dc.now()}),alert("File removed successfully!")}catch(e){console.error("Error removing file:",e),alert("Error removing file. Please try again.")}},Q=async e=>{if(e.preventDefault(),h){if(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)return void alert("Please upload all three document files before proceeding.");try{w(!0),await (0,d.mZ)((0,d.H9)(c.db,"templates",g),{enterpriseNeedFileUrl:F.enterpriseNeedFileUrl,solutionDescriptionFileUrl:F.solutionDescriptionFileUrl,riskOfNoInvestmentFileUrl:F.riskOfNoInvestmentFileUrl,step2Completed:!0,updatedAt:d.Dc.now()}),alert("Documents uploaded successfully! You can now proceed to Step 3 to generate AI artifacts."),await _()}catch(e){console.error("Error updating template:",e),alert("Error updating template. Please try again.")}finally{w(!1)}}},Y=async()=>{if(h){if(!h.step1Completed||!h.step2Completed||!h.step3Completed)return void alert("Please complete all three steps before publishing.");if(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)return void alert("Please upload all three document files before publishing.");if(!H)return void alert("Please generate AI artifacts in Step 3 before publishing.");if(window.confirm("Are you sure you want to publish this template? Once published, it cannot be edited. You can only create new versions from published templates."))try{w(!0),await (0,d.mZ)((0,d.H9)(c.db,"templates",g),{status:"published",isActive:!0,publishedAt:d.Dc.now(),updatedAt:d.Dc.now()}),alert("Template published successfully! It can no longer be edited."),await _()}catch(e){console.error("Error publishing template:",e),alert("Error publishing template. Please try again.")}finally{w(!1)}}},X=async()=>{if(h&&f&&window.confirm("This will create a new draft version of this template that you can edit. Continue?"))try{w(!0);let e=(h.templateVersion||"1.0").split("."),t=parseInt(e[0])||1,r=parseInt(e[1])||0,a="".concat(t,".").concat(r+1),s={name:h.name,description:h.description,brandId:h.brandId,category:h.category,price:h.price,solutionProviderName:h.solutionProviderName,solutionName:h.solutionName,solutionDescription:h.solutionDescription,templateVersion:a,templateVersionDate:d.Dc.now(),status:"draft",step1Completed:!0,step2Completed:!1,step3Completed:!1,isActive:!1,fileUrls:{},enterpriseNeed:h.enterpriseNeed||"",solutionDescriptionDocument:h.solutionDescriptionDocument||"",riskOfNoInvestment:h.riskOfNoInvestment||"",enterpriseNeedFileUrl:"",solutionDescriptionFileUrl:"",riskOfNoInvestmentFileUrl:"",createdAt:d.Dc.now(),updatedAt:d.Dc.now(),basedOnTemplateId:g,basedOnVersion:h.templateVersion},l=await (0,d.gS)((0,d.rJ)(c.db,"templates"),s);alert("New version ".concat(a," created successfully!")),n.push("/admin/templates/".concat(l.id,"/edit"))}catch(e){console.error("Error creating new version:",e),alert("Error creating new version. Please try again.")}finally{w(!1)}},J=async()=>{if(h){if(!E)return void alert("Please select an AI model.");if(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)return void alert("Please upload all three document files before generating AI artifacts.");try{z(!0),await (0,d.mZ)((0,d.H9)(c.db,"templates",g),{status:"generating-artifacts",selectedLLM:E,companyName:S||(null==f?void 0:f.name)||"Company Name",updatedAt:d.Dc.now()}),console.log("AI artifact generation started via Firebase Function")}catch(e){console.error("Error starting AI generation:",e),alert("Error starting AI generation. Please try again."),z(!1)}}};return t||j?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):r&&h?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(m.A,{}),(0,a.jsx)("div",{className:"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"published"===h.status?"View Template":"Edit Template"}),(0,a.jsxs)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:[h.solutionName," - ",null==f?void 0:f.name]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("published"===h.status?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"),children:"published"===h.status?"Published":"Draft"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Version ",h.templateVersion]}),h.basedOnTemplateId&&(0,a.jsxs)("span",{className:"text-xs text-blue-600 dark:text-blue-400",children:["Based on v",h.basedOnVersion]})]}),"published"===h.status&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[(0,a.jsx)("strong",{children:"Note:"})," This template is published and cannot be edited. You can create a new version to make changes."]})})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:["published"===h.status&&(0,a.jsxs)("button",{onClick:X,disabled:N,className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[N&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:N?"Creating...":"Create New Version"})]}),(0,a.jsx)(o(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Back to Admin"})]})]})}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex items-center ".concat(h.step1Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"),children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 ".concat(h.step1Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"," text-white rounded-full text-sm font-medium"),children:h.step1Completed?"✓":"1"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Basic Information"})]}),(0,a.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"flex items-center ".concat(h.step2Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"),children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 ".concat(h.step2Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"," text-white rounded-full text-sm font-medium"),children:h.step2Completed?"✓":"2"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Documents"})]}),(0,a.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"flex items-center ".concat(h.step3Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"),children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 ".concat(h.step3Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"," text-white rounded-full text-sm font-medium"),children:h.step3Completed?"✓":"3"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"AI Artifacts"})]})]})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>I("step1"),disabled:"published"===h.status,className:"flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ".concat("step1"===A?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"," ").concat("published"===h.status?"cursor-not-allowed opacity-60":""),children:"published"===h.status?"View Basic Information":"Edit Basic Information"}),(0,a.jsx)("button",{onClick:()=>I("step2"),disabled:"published"===h.status,className:"flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ".concat("step2"===A?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"," ").concat("published"===h.status?"cursor-not-allowed opacity-60":""),children:"published"===h.status?"View Documents":"Edit Documents"}),(0,a.jsx)("button",{onClick:()=>I("step3"),disabled:"published"===h.status,className:"flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ".concat("step3"===A?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"," ").concat("published"===h.status?"cursor-not-allowed opacity-60":""),children:"published"===h.status?"View AI Artifacts":"Generate AI Artifacts"})]})}),"step1"===A&&(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:W,className:"p-6 space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"published"===h.status?"Basic Information (Read Only)":"Basic Information"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Provider Name *"}),(0,a.jsx)("input",{type:"text",value:q.solutionProviderName,onChange:e=>G(t=>({...t,solutionProviderName:e.target.value})),disabled:"published"===h.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Name *"}),(0,a.jsx)("input",{type:"text",value:q.solutionName,onChange:e=>G(t=>({...t,solutionName:e.target.value})),disabled:"published"===h.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Solution Description *"}),(0,a.jsx)("button",{type:"button",onClick:()=>C(!D),className:"text-xs text-blue-600 dark:text-blue-400 hover:underline",children:D?"Try Advanced Editor":"Use Simple Editor"})]}),D?(0,a.jsx)(x.A,{value:q.solutionDescription,onChange:e=>G(t=>({...t,solutionDescription:e})),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md",disabled:"published"===h.status}):(0,a.jsx)(u.A,{value:q.solutionDescription,onChange:e=>G(t=>({...t,solutionDescription:e})),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md",disabled:"published"===h.status})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version *"}),(0,a.jsx)("input",{type:"text",value:q.templateVersion,onChange:e=>G(t=>({...t,templateVersion:e.target.value})),disabled:"published"===h.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version Date *"}),(0,a.jsx)("input",{type:"date",value:q.templateVersionDate,onChange:e=>G(t=>({...t,templateVersionDate:e.target.value})),disabled:"published"===h.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:q.category,onChange:e=>G(t=>({...t,category:e.target.value})),disabled:"published"===h.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed",children:[(0,a.jsx)("option",{value:"Business Analysis",children:"Business Analysis"}),(0,a.jsx)("option",{value:"Financial Planning",children:"Financial Planning"}),(0,a.jsx)("option",{value:"Technology Assessment",children:"Technology Assessment"}),(0,a.jsx)("option",{value:"Risk Management",children:"Risk Management"}),(0,a.jsx)("option",{value:"Strategic Planning",children:"Strategic Planning"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Price ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:q.price,onChange:e=>G(t=>({...t,price:parseFloat(e.target.value)||0})),disabled:"published"===h.status,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"})]})]}),"published"!==h.status&&(0,a.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsx)("button",{type:"submit",disabled:N,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed",children:N?"Updating...":"Update Basic Information"})})]})}),"step2"===A&&(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:Q,className:"p-6 space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"published"===h.status?"View Documents (Read Only)":"Upload Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"published"===h.status?"View the three essential documents for this Business Value Analysis template.":"Upload the three essential documents for your Business Value Analysis template. These files will be used to generate AI artifacts in Step 3."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Enterprise Need Document *"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Upload a document that describes the business need or challenge that this solution addresses."}),(0,a.jsx)(p,{onFileSelect:e=>Z(e,"enterpriseNeed"),onFileRemove:()=>K("enterpriseNeed"),currentFileUrl:F.enterpriseNeedFileUrl,currentFileName:F.enterpriseNeedFileUrl?b.getFileNameFromUrl(F.enterpriseNeedFileUrl):void 0,disabled:T.enterpriseNeed||"published"===h.status,className:"mt-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Description Document *"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Upload a document that provides a comprehensive description of how your solution works and its key features."}),(0,a.jsx)(p,{onFileSelect:e=>Z(e,"solutionDescription"),onFileRemove:()=>K("solutionDescription"),currentFileUrl:F.solutionDescriptionFileUrl,currentFileName:F.solutionDescriptionFileUrl?b.getFileNameFromUrl(F.solutionDescriptionFileUrl):void 0,disabled:T.solutionDescription||"published"===h.status,className:"mt-2"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Risk of No Investment Document *"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Upload a document that explains the potential risks and consequences of not implementing this solution."}),(0,a.jsx)(p,{onFileSelect:e=>Z(e,"riskOfNoInvestment"),onFileRemove:()=>K("riskOfNoInvestment"),currentFileUrl:F.riskOfNoInvestmentFileUrl,currentFileName:F.riskOfNoInvestmentFileUrl?b.getFileNameFromUrl(F.riskOfNoInvestmentFileUrl):void 0,disabled:T.riskOfNoInvestment||"published"===h.status,className:"mt-2"})]}),"published"!==h.status&&(0,a.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("button",{type:"submit",disabled:N,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[N&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:N?"Saving...":"Save & Continue to Step 3"})]})})]})}),"step3"===A&&(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("div",{className:"p-6 space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"published"===h.status?"View AI Artifacts (Read Only)":"Generate AI Artifacts"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"published"===h.status?"View the AI-generated artifacts for this Business Value Analysis template.":"Generate AI artifacts based on the uploaded documents. These artifacts will be used in the final Business Value Analysis template."})]}),(!F.enterpriseNeedFileUrl||!F.solutionDescriptionFileUrl||!F.riskOfNoInvestmentFileUrl)&&(0,a.jsx)("div",{className:"p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Prerequisites Required"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-yellow-700 dark:text-yellow-300",children:(0,a.jsx)("p",{children:"Please complete Step 2 by uploading all three document files before generating AI artifacts."})})]})]})}),"published"!==h.status&&F.enterpriseNeedFileUrl&&F.solutionDescriptionFileUrl&&F.riskOfNoInvestmentFileUrl&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Select AI Model *"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)("div",{className:"relative rounded-lg border p-4 cursor-pointer transition-colors ".concat("claude"===E?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"),onClick:()=>V("claude"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"llm",value:"claude",checked:"claude"===E,onChange:()=>V("claude"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-900 dark:text-white",children:"Claude AI API"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Anthropic's Claude for advanced reasoning"})]})]})}),(0,a.jsx)("div",{className:"relative rounded-lg border p-4 cursor-pointer transition-colors ".concat("openai"===E?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"),onClick:()=>V("openai"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"llm",value:"openai",checked:"openai"===E,onChange:()=>V("openai"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-900 dark:text-white",children:"OpenAI API"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"OpenAI's GPT for creative content generation"})]})]})})]})]}),"published"!==h.status&&F.enterpriseNeedFileUrl&&F.solutionDescriptionFileUrl&&F.riskOfNoInvestmentFileUrl&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Company Name (Optional)"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Enter the company name to be used in the artifacts. If left blank, the brand name will be used."}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>P(e.target.value),placeholder:"Default: ".concat((null==f?void 0:f.name)||"Company Name"),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"})]}),O&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"AI Generation Prompt"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"This is the exact prompt that will be sent to the selected AI model."}),(0,a.jsx)("textarea",{value:O,readOnly:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white text-sm"})]}),"published"!==h.status&&F.enterpriseNeedFileUrl&&F.solutionDescriptionFileUrl&&F.riskOfNoInvestmentFileUrl&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("button",{type:"button",onClick:J,disabled:R,className:"bg-purple-600 dark:bg-purple-700 text-white px-8 py-3 rounded-md font-medium hover:bg-purple-700 dark:hover:bg-purple-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[R&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:R?"Generating AI Artifacts...":"Generate AI Artifacts"})]})}),(R||"generating-artifacts"===h.status)&&(0,a.jsx)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"animate-spin h-5 w-5 text-blue-600 dark:text-blue-400 mr-3",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"AI Artifacts Being Generated"}),(0,a.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:"Please wait while the AI processes your documents and generates the artifacts. This may take a few minutes."})]})]})}),"generation-failed"===h.status&&(0,a.jsx)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"AI Generation Failed"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:h.generationError||"An error occurred while generating AI artifacts. Please try again."}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("button",{onClick:()=>{(0,d.mZ)((0,d.H9)(c.db,"templates",g),{status:"draft",updatedAt:d.Dc.now()})},className:"text-sm text-red-800 dark:text-red-200 underline hover:no-underline",children:"Try Again"})})]})]})}),H&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-green-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"AI Artifacts Generated Successfully"}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"The AI has successfully generated three artifacts based on your uploaded documents."})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Enterprise Need Artifact"}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono",children:H.enterpriseNeedArtifact})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Proposed Solution Artifact"}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono",children:H.solutionArtifact})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"Risk of No Investment Artifact"}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-200 font-mono",children:H.riskArtifact})})]})]}),"published"!==h.status&&h.step3Completed&&H&&(0,a.jsx)("div",{className:"flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("button",{type:"button",onClick:Y,disabled:N,className:"bg-green-600 dark:bg-green-700 text-white px-8 py-3 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[N&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:N?"Publishing...":"Publish Template"})]})})]})})]})})]}):null}},1901:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(5155),s=r(9137),l=r.n(s),i=r(2115);let n=e=>{let{value:t,onChange:r,placeholder:s="Enter text...",className:n="",disabled:o=!1}=e,d=(0,i.useRef)(null),c=(e,t)=>{document.execCommand(e,!1,t),d.current&&(d.current.focus(),r(d.current.innerHTML))},m=e=>{c(e?"insertOrderedList":"insertUnorderedList")};return(0,a.jsxs)("div",{className:"jsx-570c7a2a0768ea83 "+"simple-rich-text-editor ".concat(n),children:[(0,a.jsxs)("div",{className:"jsx-570c7a2a0768ea83 border border-gray-300 dark:border-gray-600 border-b-0 bg-gray-50 dark:bg-gray-700 p-2 flex flex-wrap gap-1 rounded-t-md",children:[(0,a.jsx)("button",{type:"button",onClick:()=>c("bold"),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:(0,a.jsx)("strong",{className:"jsx-570c7a2a0768ea83",children:"B"})}),(0,a.jsx)("button",{type:"button",onClick:()=>c("italic"),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:(0,a.jsx)("em",{className:"jsx-570c7a2a0768ea83",children:"I"})}),(0,a.jsx)("button",{type:"button",onClick:()=>c("underline"),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:(0,a.jsx)("u",{className:"jsx-570c7a2a0768ea83",children:"U"})}),(0,a.jsx)("div",{className:"jsx-570c7a2a0768ea83 w-px bg-gray-300 dark:bg-gray-600 mx-1"}),(0,a.jsx)("button",{type:"button",onClick:()=>m(!1),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:"• List"}),(0,a.jsx)("button",{type:"button",onClick:()=>m(!0),disabled:o,className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none",children:"1. List"}),(0,a.jsx)("div",{className:"jsx-570c7a2a0768ea83 w-px bg-gray-300 dark:bg-gray-600 mx-1"}),(0,a.jsxs)("select",{onChange:e=>c("formatBlock",e.target.value),disabled:o,defaultValue:"",className:"jsx-570c7a2a0768ea83 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"",className:"jsx-570c7a2a0768ea83",children:"Normal"}),(0,a.jsx)("option",{value:"h1",className:"jsx-570c7a2a0768ea83",children:"Heading 1"}),(0,a.jsx)("option",{value:"h2",className:"jsx-570c7a2a0768ea83",children:"Heading 2"}),(0,a.jsx)("option",{value:"h3",className:"jsx-570c7a2a0768ea83",children:"Heading 3"}),(0,a.jsx)("option",{value:"h4",className:"jsx-570c7a2a0768ea83",children:"Heading 4"}),(0,a.jsx)("option",{value:"h5",className:"jsx-570c7a2a0768ea83",children:"Heading 5"}),(0,a.jsx)("option",{value:"h6",className:"jsx-570c7a2a0768ea83",children:"Heading 6"})]})]}),(0,a.jsx)("div",{ref:d,contentEditable:!o,onInput:()=>{d.current&&r(d.current.innerHTML)},dangerouslySetInnerHTML:{__html:t},style:{backgroundColor:o?"#f9fafb":void 0},"data-placeholder":s,className:"jsx-570c7a2a0768ea83 "+"\n          min-h-[150px] p-3 border border-gray-300 dark:border-gray-600 rounded-b-md\n          bg-white dark:bg-gray-700 text-gray-900 dark:text-white\n          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\n          ".concat(o?"bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"","\n        ")}),(0,a.jsx)(l(),{id:"570c7a2a0768ea83",children:".simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83:empty:before{content:attr(data-placeholder);color:#9ca3af;pointer-events:none}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83{outline:none}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h1.jsx-570c7a2a0768ea83{font-size:2em;font-weight:bold;margin:.67em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h2.jsx-570c7a2a0768ea83{font-size:1.5em;font-weight:bold;margin:.75em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h3.jsx-570c7a2a0768ea83{font-size:1.17em;font-weight:bold;margin:.83em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h4.jsx-570c7a2a0768ea83{font-size:1em;font-weight:bold;margin:1.12em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h5.jsx-570c7a2a0768ea83{font-size:.83em;font-weight:bold;margin:1.5em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 h6.jsx-570c7a2a0768ea83{font-size:.75em;font-weight:bold;margin:1.67em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 ul.jsx-570c7a2a0768ea83{list-style-type:disc;margin:1em 0;padding-left:2em}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 ol.jsx-570c7a2a0768ea83{list-style-type:decimal;margin:1em 0;padding-left:2em}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 li.jsx-570c7a2a0768ea83{margin:.5em 0}.simple-rich-text-editor.jsx-570c7a2a0768ea83 [contenteditable].jsx-570c7a2a0768ea83 p.jsx-570c7a2a0768ea83{margin:1em 0}"})]})}},2547:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(5155),s=r(9137),l=r.n(s),i=r(2115),n=r(5028),o=r(1901);class d extends i.Component{static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(e,t){console.error("ReactQuill Error:",e,t),this.props.onError()}render(){return this.state.hasError?null:this.props.children}constructor(e){super(e),this.state={hasError:!1}}}let c=(0,n.default)(()=>Promise.all([r.e(553),r.e(981)]).then(r.t.bind(r,1981,23)),{loadableGenerated:{webpack:()=>[1981]},ssr:!1,loading:()=>(0,a.jsx)("div",{className:"h-32 bg-gray-100 dark:bg-gray-700 rounded animate-pulse"})}),m=e=>{let{value:t,onChange:r,placeholder:s="Enter text...",className:n="",disabled:m=!1}=e,[u,x]=(0,i.useState)(!1),[p,g]=(0,i.useState)(!1);(0,i.useEffect)(()=>{g(!0)},[]);let b={toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],["link"],[{align:[]}],["clean"]]};return u||!p?(0,a.jsx)(o.A,{value:t,onChange:r,placeholder:s,className:n,disabled:m}):(0,a.jsxs)("div",{className:"jsx-3979297ce4da0409 "+"rich-text-editor ".concat(n),children:[(0,a.jsx)("div",{className:"jsx-3979297ce4da0409 mb-2",children:(0,a.jsx)("button",{type:"button",onClick:()=>x(!0),className:"jsx-3979297ce4da0409 text-xs text-blue-600 dark:text-blue-400 hover:underline",children:"Switch to Simple Editor"})}),(0,a.jsx)(d,{onError:()=>{console.warn("ReactQuill error detected, falling back to simple editor"),x(!0)},children:(0,a.jsx)(c,{theme:"snow",value:t,onChange:r,modules:b,formats:["header","bold","italic","underline","strike","list","bullet","indent","link","align"],placeholder:s,readOnly:m,style:{backgroundColor:m?"#f9fafb":"white"}})}),(0,a.jsx)(l(),{id:"3979297ce4da0409",children:".rich-text-editor .ql-editor{min-height:150px}.rich-text-editor .ql-toolbar{border-top:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.rich-text-editor .ql-container{border-bottom:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.dark .rich-text-editor .ql-toolbar{border-color:#4b5563;background-color:#374151}.dark .rich-text-editor .ql-container{border-color:#4b5563;background-color:#1f2937}.dark .rich-text-editor .ql-editor{color:#f9fafb}.dark .rich-text-editor .ql-toolbar .ql-stroke{stroke:#9ca3af}.dark .rich-text-editor .ql-toolbar .ql-fill{fill:#9ca3af}"})]})}},8195:(e,t,r)=>{Promise.resolve().then(r.bind(r,573))}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,424,573,441,684,358],()=>t(8195)),_N_E=e.O()}]);